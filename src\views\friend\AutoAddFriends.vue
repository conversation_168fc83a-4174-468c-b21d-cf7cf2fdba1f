<template>
  <div class="auto-add-friends">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>自动添加好友</h1>
          <p>微信自动添加好友记录管理和状态跟踪</p>
        </div>
        
        <div class="header-actions">
          <a-button type="primary" @click="showManualAddModal">
            <PlusOutlined />
            手动添加记录
          </a-button>
          
          <a-button @click="exportRecords" :loading="exporting">
            <DownloadOutlined />
            导出记录
          </a-button>
          
          <a-button @click="refreshData" :loading="refreshing">
            <ReloadOutlined />
            刷新数据
          </a-button>
        </div>
      </div>
    </div>

    <!-- 统计概览卡片 -->
    <div class="overview-cards" v-if="!loading">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="总添加次数"
              :value="overview.总添加次数"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <UserAddOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="成功添加"
              :value="overview.成功添加数"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <CheckCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="待处理"
              :value="overview.待处理数"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <ClockCircleOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日添加"
              :value="overview.今日添加数"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <TrophyOutlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 筛选和搜索区域 -->
    <a-card class="filter-card" :bordered="false">
      <div class="filter-content">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-input
              v-model:value="searchKeyword"
              placeholder="搜索微信号或达人信息"
              allow-clear
              @change="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-select
              v-model:value="statusFilter"
              placeholder="好友请求状态"
              allow-clear
              @change="handleFilterChange"
              style="width: 100%"
            >
              <a-select-option :value="1">已添加成功</a-select-option>
              <a-select-option :value="0">待处理</a-select-option>
              <a-select-option :value="-1">添加失败</a-select-option>
              <a-select-option :value="-2">被拒绝</a-select-option>
            </a-select>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-select
              v-model:value="wechatFilter"
              placeholder="筛选微信账号"
              allow-clear
              @change="handleFilterChange"
              style="width: 100%"
            >
              <a-select-option v-for="account in wechatAccounts" :key="account.id" :value="account.id">
                {{ account.微信号 }}
              </a-select-option>
            </a-select>
          </a-col>
          
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-range-picker
              v-model:value="dateRange"
              @change="handleDateRangeChange"
              style="width: 100%"
            />
          </a-col>
        </a-row>
      </div>
    </a-card>

    <!-- 记录列表 -->
    <a-card class="records-card" :bordered="false">
      <template #title>
        <div class="table-title">
          <span>添加记录列表</span>
          <a-badge :count="paginationConfig.total" style="margin-left: 8px" />
        </div>
      </template>
      
      <template #extra>
        <a-space>
          <a-button size="small" @click="batchDelete" :disabled="selectedRowKeys.length === 0" danger>
            <DeleteOutlined />
            批量删除
          </a-button>
        </a-space>
      </template>
      
      <!-- 调试信息 -->
      <div v-if="recordsList.length === 0 && !listLoading" style="margin-bottom: 16px; padding: 16px; background: #f0f0f0; border-radius: 4px;">
        <p><strong>调试信息：</strong></p>
        <p>数据列表长度: {{ recordsList.length }}</p>
        <p>加载状态: {{ listLoading }}</p>
        <p>分页总数: {{ paginationConfig.total }}</p>
      </div>

      <a-table
        :columns="tableColumns"
        :data-source="recordsList"
        :loading="listLoading"
        :pagination="paginationConfig"
        :row-selection="rowSelection"
        @change="handleTableChange"
        row-key="id"
        :scroll="{ x: 1400 }"
      >
        <!-- 自定义渲染 -->
        <template #bodyCell="{ column, record }">
          <!-- 状态列 -->
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.好友请求状态)">
              {{ getStatusText(record.好友请求状态) }}
            </a-tag>
          </template>

          <!-- 计划时间列 -->
          <template v-else-if="column.key === 'planTime'">
            <span v-if="record.计划添加时间">
              {{ formatDateTime(record.计划添加时间) }}
            </span>
            <span v-else class="text-muted">未设置</span>
          </template>

          <!-- 实际时间列 -->
          <template v-else-if="column.key === 'actualTime'">
            <span v-if="record.实际添加时间">
              {{ formatDateTime(record.实际添加时间) }}
            </span>
            <span v-else class="text-muted">未执行</span>
          </template>

          <!-- 微信账号列 -->
          <template v-else-if="column.key === 'wechatAccount'">
            <div class="wechat-info">
              <a-avatar size="small" :src="record.微信头像" style="margin-right: 8px">
                <template #icon><UserOutlined /></template>
              </a-avatar>
              <div>
                <div class="wechat-name">{{ record.我方微信昵称 || record.我方微信号 || '未知账号' }}</div>
                <div class="wechat-id text-muted" v-if="record.我方微信昵称">{{ record.我方微信号 }}</div>
              </div>
            </div>
          </template>

          <!-- 达人信息列 -->
          <template v-else-if="column.key === 'talentInfo'">
            <div class="talent-info">
              <div class="talent-header">
                <a-avatar size="small" :src="record.达人头像" style="margin-right: 8px">
                  <template #icon><UserOutlined /></template>
                </a-avatar>
                <div class="talent-details">
                  <div class="talent-name">{{ record.达人昵称 || '未知达人' }}</div>
                  <div class="talent-contact text-muted">
                    <span>{{ record.目标联系方式 || record.联系方式 || '无联系方式' }}</span>
                    <a-tag size="small" :color="getContactTypeColor(record.联系方式类型)" style="margin-left: 4px">
                      {{ record.联系方式类型 || '未知' }}
                    </a-tag>
                  </div>
                </div>
              </div>
              <div class="talent-stats" v-if="record.粉丝数 || record.抖音账号">
                <a-tag size="small" color="blue" v-if="record.粉丝数">
                  粉丝: {{ formatNumber(record.粉丝数) }}
                </a-tag>
                <a-tag size="small" color="green" v-if="record.抖音账号">
                  抖音: {{ record.抖音账号 }}
                </a-tag>
                <a-tag size="small" color="purple" v-if="record.联系方式来源类型">
                  来源: {{ record.联系方式来源类型 }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 统计信息列 -->
          <template v-else-if="column.key === 'statistics'">
            <div class="statistics-info">
              <div class="stat-item">
                <span class="label">当日:</span>
                <a-tag size="small" color="blue">{{ record.当日添加计数 || 0 }}</a-tag>
              </div>
              <div class="stat-item">
                <span class="label">连续:</span>
                <a-tag size="small" color="orange">{{ record.连续添加计数 || 0 }}</a-tag>
              </div>
              <div class="stat-item" v-if="record.时间控制状态">
                <span class="label">控制:</span>
                <a-tag size="small" :color="getTimeControlColor(record.时间控制状态)">
                  {{ getTimeControlText(record.时间控制状态) }}
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createTime'">
            <span v-if="record.创建时间">
              {{ formatDateTime(record.创建时间) }}
            </span>
            <span v-else class="text-muted">未知</span>
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'action'">
            <a-space size="small">
              <a-button type="link" size="small" @click="viewRecord(record)">
                <EyeOutlined />
                详情
              </a-button>

              <a-popconfirm
                title="确定要删除这条记录吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteRecord(record.id)"
              >
                <a-button type="link" size="small" danger>
                  <DeleteOutlined />
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 记录详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="添加记录详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedRecord" class="record-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="记录id">{{ selectedRecord.id }}</a-descriptions-item>
          <a-descriptions-item label="添加状态">
            <a-tag :color="getStatusColor(selectedRecord.好友请求状态)">
              {{ getStatusText(selectedRecord.好友请求状态) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="我方微信号">{{ selectedRecord.我方微信号 }}</a-descriptions-item>
          <a-descriptions-item label="目标联系方式">{{ selectedRecord.目标联系方式 }}</a-descriptions-item>
          <a-descriptions-item label="达人昵称">{{ selectedRecord.达人昵称 }}</a-descriptions-item>
          <a-descriptions-item label="联系方式类型">{{ selectedRecord.联系方式类型 }}</a-descriptions-item>
          <a-descriptions-item label="计划添加时间">
            {{ selectedRecord.计划添加时间 ? formatDateTime(selectedRecord.计划添加时间) : '未设置' }}
          </a-descriptions-item>
          <a-descriptions-item label="实际添加时间">
            {{ selectedRecord.实际添加时间 ? formatDateTime(selectedRecord.实际添加时间) : '未执行' }}
          </a-descriptions-item>
          <a-descriptions-item label="当日添加计数">{{ selectedRecord.当日添加计数 || 0 }}</a-descriptions-item>
          <a-descriptions-item label="连续添加计数">{{ selectedRecord.连续添加计数 || 0 }}</a-descriptions-item>
          <a-descriptions-item label="时间控制状态">{{ selectedRecord.时间控制状态 || 'normal' }}</a-descriptions-item>
          <a-descriptions-item label="最后长休息时间">
            {{ selectedRecord.最后长休息时间 ? formatDateTime(selectedRecord.最后长休息时间) : '无记录' }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(selectedRecord.创建时间) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间" :span="2">
            {{ formatDateTime(selectedRecord.更新时间) }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>

    <!-- 手动添加记录弹窗 -->
    <a-modal
      v-model:open="manualAddModalVisible"
      title="手动添加记录"
      width="600px"
      :confirm-loading="manualAddLoading"
      @ok="handleManualAdd"
    >
      <a-form ref="manualAddFormRef" :model="manualAddForm" :rules="manualAddRules" layout="vertical">
        <a-form-item label="选择微信账号" name="微信信息表id">
          <a-select v-model:value="manualAddForm.微信信息表id" placeholder="请选择微信账号">
            <a-select-option v-for="account in wechatAccounts" :key="account.id" :value="account.id">
              {{ account.微信号 }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="目标联系方式" name="目标联系方式">
          <a-input v-model:value="manualAddForm.目标联系方式" placeholder="请输入目标微信号或手机号" />
        </a-form-item>
        
        <a-form-item label="联系方式类型" name="联系方式类型">
          <a-select v-model:value="manualAddForm.联系方式类型" placeholder="请选择联系方式类型">
            <a-select-option value="微信号">微信号</a-select-option>
            <a-select-option value="手机号">手机号</a-select-option>
            <a-select-option value="QQ号">QQ号</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="计划添加时间" name="计划添加时间">
          <a-date-picker 
            v-model:value="manualAddForm.计划添加时间" 
            show-time 
            format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="验证消息" name="验证消息">
          <a-textarea 
            v-model:value="manualAddForm.验证消息" 
            placeholder="请输入好友验证消息"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined, ReloadOutlined, DownloadOutlined, SearchOutlined,
  UserAddOutlined, CheckCircleOutlined, ClockCircleOutlined, TrophyOutlined,
  EyeOutlined, DeleteOutlined, UserOutlined
} from '@ant-design/icons-vue'
import { autoAddFriendsService } from '@/services/friend/autoAddFriendsService'

// 定义组件名称
defineOptions({ name: 'AutoAddFriends' })

// 响应式数据定义
const loading = ref(true)
const refreshing = ref(false)
const exporting = ref(false)
const listLoading = ref(false)

// 列表数据
const recordsList = ref([])
const wechatAccounts = ref([])

console.log('📊 初始化数据状态:', {
  recordsList: recordsList.value,
  wechatAccounts: wechatAccounts.value
})

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref()
const wechatFilter = ref()
const dateRange = ref()

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 概览统计数据
const overview = reactive({
  总添加次数: 0,
  成功添加数: 0,
  待处理数: 0,
  今日添加数: 0
})

// 表格行选择
const selectedRowKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 弹窗控制
const detailModalVisible = ref(false)
const manualAddModalVisible = ref(false)
const selectedRecord = ref(null)
const manualAddLoading = ref(false)

// 手动添加表单
const manualAddFormRef = ref()
const manualAddForm = reactive({
  微信信息表id: null,
  目标联系方式: '',
  联系方式类型: '微信号',
  计划添加时间: null,
  验证消息: ''
})

// 手动添加表单验证规则
const manualAddRules = {
  微信信息表id: [{ required: true, message: '请选择微信账号' }],
  目标联系方式: [{ required: true, message: '请输入目标联系方式' }],
  联系方式类型: [{ required: true, message: '请选择联系方式类型' }]
}

// 表格列定义
const tableColumns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 70,
    sorter: true
  },
  {
    title: '我方微信',
    key: 'wechatAccount',
    width: 140
  },
  {
    title: '达人信息',
    key: 'talentInfo',
    width: 250
  },
  {
    title: '状态',
    key: 'status',
    width: 90
  },
  {
    title: '计划时间',
    key: 'planTime',
    width: 140,
    sorter: true
  },
  {
    title: '实际时间',
    key: 'actualTime',
    width: 140,
    sorter: true
  },
  {
    title: '统计信息',
    key: 'statistics',
    width: 120
  },
  {
    title: '创建时间',
    dataIndex: '创建时间',
    key: 'createTime',
    width: 140,
    sorter: true
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right'
  }
]

console.log('📋 表格列配置:', tableColumns)

// 计算属性和方法
const getStatusColor = (status) => {
  // 处理null值和不同状态值
  if (status === null || status === undefined) {
    return 'default'  // 未设置状态
  }

  const statusColors = {
    1: 'success',     // 已添加成功
    0: 'processing',  // 待处理
    2: 'warning',     // 进行中/被拒绝
    '-1': 'error',    // 添加失败
    '-2': 'warning'   // 被拒绝
  }
  return statusColors[status] || 'default'
}

const getStatusText = (status) => {
  // 处理null值和不同状态值
  if (status === null || status === undefined) {
    return '未设置'  // 未设置状态
  }

  const statusTexts = {
    1: '已添加成功',
    0: '待处理',
    2: '被拒绝',      // 根据接口数据，状态2表示被拒绝
    '-1': '添加失败',
    '-2': '被拒绝'
  }
  return statusTexts[status] || '未知状态'
}

// 获取联系方式类型颜色
const getContactTypeColor = (type) => {
  const typeColors = {
    '微信号': 'green',
    '手机号': 'blue',
    'QQ号': 'purple'
  }
  return typeColors[type] || 'default'
}

// 获取时间控制状态颜色
const getTimeControlColor = (status) => {
  const controlColors = {
    '正常': 'green',        // 正常
    '批量休息': 'orange',   // 批量休息
    '超时重置': 'blue',     // 超时重置
    '受限': 'orange',       // 受限
    '阻塞': 'red',          // 阻塞
    '等待': 'blue'          // 等待
  }
  return controlColors[status] || 'default'
}

// 获取时间控制状态文本
const getTimeControlText = (status) => {
  return status || '未知'
}

const formatDateTime = (dateStr) => {
  return dateStr ? dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss') : ''
}

const formatNumber = (num) => {
  if (!num && num !== 0) return ''
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

// 页面生命周期
onMounted(() => {
  console.log('🚀 AutoAddFriends组件已挂载，开始加载数据')
  loadPageData()
})

// 数据加载方法
const loadPageData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadOverviewData(),
      loadRecordsList(),
      loadWechatAccounts()
    ])
  } finally {
    loading.value = false
  }
}

const loadOverviewData = async () => {
  try {
    const response = await autoAddFriendsService.getOverview()
    if (response.status === 100) {
      Object.assign(overview, response.data)
    }
  } catch (error) {
    console.error('加载概览数据失败:', error)
  }
}

const loadRecordsList = async () => {
  listLoading.value = true
  try {
    const params = {
      页码: paginationConfig.current,
      每页数量: paginationConfig.pageSize,
      搜索关键词: searchKeyword.value,
      状态筛选: statusFilter.value,
      微信账号筛选: wechatFilter.value,
      时间范围: dateRange.value
    }
    
    const response = await autoAddFriendsService.getRecordsList(params)
    console.log('📊 API响应数据:', response)

    if (response.status === 100) {
      recordsList.value = response.data.列表 || []
      paginationConfig.total = response.data.总数 || 0
      console.log('✅ 数据加载成功:', {
        列表长度: recordsList.value.length,
        总数: paginationConfig.total,
        示例数据: recordsList.value[0]
      })
    } else {
      console.error('❌ API返回错误:', response)
      message.error(response.message || '加载记录列表失败')
    }
  } catch (error) {
    console.error('加载记录列表失败:', error)
    message.error('加载记录列表失败')
  } finally {
    listLoading.value = false
  }
}

const loadWechatAccounts = async () => {
  try {
    const response = await autoAddFriendsService.getWechatAccounts()
    if (response.status === 100) {
      wechatAccounts.value = response.data || []
    }
  } catch (error) {
    console.error('加载微信账号列表失败:', error)
  }
}

// 事件处理方法
const refreshData = async () => {
  refreshing.value = true
  try {
    await loadPageData()
    message.success('数据已刷新')
  } finally {
    refreshing.value = false
  }
}

const handleSearch = () => {
  paginationConfig.current = 1
  loadRecordsList()
}

const handleFilterChange = () => {
  paginationConfig.current = 1
  loadRecordsList()
}

const handleDateRangeChange = () => {
  paginationConfig.current = 1
  loadRecordsList()
}

const handleTableChange = (pagination) => {
  paginationConfig.current = pagination.current
  paginationConfig.pageSize = pagination.pageSize
  loadRecordsList()
}

const viewRecord = (record) => {
  selectedRecord.value = record
  detailModalVisible.value = true
}

const showManualAddModal = () => {
  manualAddModalVisible.value = true
}

const handleManualAdd = async () => {
  try {
    const valid = await manualAddFormRef.value.validate()
    if (!valid) return
    
    manualAddLoading.value = true
    
    const response = await autoAddFriendsService.manualAdd(manualAddForm)
    if (response.status === 100) {
      message.success('手动添加记录创建成功')
      manualAddModalVisible.value = false
      resetManualAddForm()
      loadRecordsList()
    } else {
      message.error(response.message || '创建记录失败')
    }
  } catch (error) {
    console.error('创建手动添加记录失败:', error)
    message.error('创建记录失败')
  } finally {
    manualAddLoading.value = false
  }
}

const resetManualAddForm = () => {
  Object.assign(manualAddForm, {
    微信信息表id: null,
    目标联系方式: '',
    联系方式类型: '微信号',
    计划添加时间: null,
    验证消息: ''
  })
}

const deleteRecord = async (recordId) => {
  try {
    const response = await autoAddFriendsService.deleteRecord(recordId)
    if (response.status === 100) {
      message.success('记录删除成功')
      loadRecordsList()
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除记录失败:', error)
    message.error('删除失败')
  }
}

const batchDelete = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录')
    return
  }
  
  try {
    const response = await autoAddFriendsService.batchDelete(selectedRowKeys.value)
    if (response.status === 100) {
      message.success(`已删除 ${selectedRowKeys.value.length} 条记录`)
      selectedRowKeys.value = []
      loadRecordsList()
    } else {
      message.error(response.message || '批量删除失败')
    }
  } catch (error) {
    console.error('批量删除失败:', error)
    message.error('批量删除失败')
  }
}

const exportRecords = async () => {
  exporting.value = true
  try {
    const filterParams = {
      搜索关键词: searchKeyword.value,
      状态筛选: statusFilter.value,
      微信账号筛选: wechatFilter.value,
      时间范围: dateRange.value
    }
    
    const response = await autoAddFriendsService.exportRecords(filterParams)
    
    // 创建下载链接
    const url = window.URL.createObjectURL(response.data)
    const a = document.createElement('a')
    a.href = url
    a.download = `自动添加记录_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    message.success('导出完成')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style lang="less" scoped>
.auto-add-friends {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  .page-header {
    margin-bottom: 20px;
    padding: 24px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        h1 {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: #1890ff;
        }

        p {
          margin: 8px 0 0;
          color: #666;
          font-size: 14px;
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .overview-cards {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .filter-card {
    margin-bottom: 20px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .filter-content {
      padding: 8px 0;
    }
  }

  .records-card {
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .table-title {
      display: flex;
      align-items: center;
      font-weight: 600;
    }
  }

  .wechat-info {
    display: flex;
    align-items: center;

    .wechat-name {
      font-weight: 500;
    }

    .wechat-id {
      font-size: 12px;
      margin-top: 2px;
    }
  }

  .talent-info {
    .talent-header {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
    }

    .talent-details {
      flex: 1;
    }

    .talent-name {
      font-weight: 500;
      margin-bottom: 2px;
    }

    .talent-contact {
      font-size: 12px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 4px;
    }

    .talent-stats {
      margin-top: 4px;

      .ant-tag {
        margin-right: 4px;
        margin-bottom: 2px;
      }
    }
  }

  .statistics-info {
    font-size: 12px;

    .stat-item {
      display: flex;
      align-items: center;
      margin-bottom: 2px;

      .label {
        font-weight: 500;
        margin-right: 4px;
        min-width: 32px;
      }
    }
  }

  .text-muted {
    color: #999;
  }

  .record-detail {
    .ant-descriptions-item-label {
      font-weight: 500;
    }
  }
}
</style> 