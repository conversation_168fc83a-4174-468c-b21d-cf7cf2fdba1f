<template>
  <div class="create-contact-with-talent">
    <a-card :title="isEditMode ? '编辑联系人' : '新增联系人'" class="main-card">
      <a-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        @finish="handleSubmit"
      >
        <!-- 联系人基本信息 -->
        <a-divider orientation="left">联系人基本信息</a-divider>
        
        <a-form-item label="联系人姓名" name="姓名" required>
          <a-input
            v-model:value="form.姓名"
            placeholder="请输入联系人姓名"
            :maxlength="50"
            show-count
          />
        </a-form-item>

        <!-- 寄样信息区域 -->
        <a-form-item label="寄样信息">
          <div class="shipping-info-section">
            <div 
              v-for="(info, index) in form.寄样信息" 
              :key="index" 
              class="shipping-info-item"
            >
              <a-card size="small" :title="`寄样信息 ${index + 1}`">
                <template #extra>
                  <a-button 
                    type="text" 
                    danger 
                    size="small" 
                    @click="removeShippingInfo(index)"
                    :disabled="form.寄样信息.length <= 1"
                  >
                    删除
                  </a-button>
                </template>
                
                <a-row :gutter="16">
                  <a-col :span="8">
                    <a-form-item 
                      :name="['寄样信息', index, '收件人']" 
                      label="收件人"
                      :rules="[{ required: true, message: '请输入收件人' }]"
                    >
                      <a-input
                        v-model:value="info.收件人"
                        placeholder="请输入收件人姓名"
                        :maxlength="50"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item 
                      :name="['寄样信息', index, '电话']" 
                      label="联系电话"
                      :rules="[{ required: true, message: '请输入联系电话' }]"
                    >
                      <a-input
                        v-model:value="info.电话"
                        placeholder="请输入联系电话"
                        :maxlength="20"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item 
                      :name="['寄样信息', index, '地址']" 
                      label="寄样地址"
                      :rules="[{ required: true, message: '请输入寄样地址' }]"
                    >
                      <a-textarea
                        v-model:value="info.地址"
                        placeholder="请输入详细地址"
                        :rows="2"
                        :maxlength="200"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </div>
            
            <a-button 
              type="dashed" 
              block 
              @click="addShippingInfo"
              style="margin-top: 16px;"
            >
              <plus-outlined />
              添加寄样信息
            </a-button>
          </div>
        </a-form-item>

        <!-- 编辑模式：显示当前关联的达人信息 -->
        <template v-if="isEditMode">
          <a-divider orientation="left">当前关联的达人信息</a-divider>

          <a-form-item label="关联达人">
            <a-spin :spinning="loadingTalentInfo">
              <a-card v-if="currentTalentInfo" size="small" class="talent-info-card">
                <div class="current-talent-info">
                  <a-descriptions :column="1" size="small">
                    <a-descriptions-item label="联系方式">
                      <strong>{{ currentTalentInfo.联系方式 }}</strong>
                      <a-tag color="blue" size="small" style="margin-left: 8px;">
                        {{ currentTalentInfo.联系方式类型 }}
                      </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="平台信息" v-if="currentTalentInfo.平台账号">
                      {{ currentTalentInfo.平台 }}：{{ currentTalentInfo.平台账号 }}
                    </a-descriptions-item>
                    <a-descriptions-item label="个人备注" v-if="currentTalentInfo.个人备注">
                      {{ currentTalentInfo.个人备注 }}
                    </a-descriptions-item>
                    <a-descriptions-item label="个人标签" v-if="currentTalentInfo.个人标签 && currentTalentInfo.个人标签.length > 0">
                      <a-space>
                        <a-tag v-for="tag in currentTalentInfo.个人标签" :key="tag" size="small">
                          {{ tag }}
                        </a-tag>
                      </a-space>
                    </a-descriptions-item>
                  </a-descriptions>
                </div>
                <template #actions>
                  <a-button type="link" @click="showChangeAssociation = true" size="small">
                    <edit-outlined />
                    更改关联
                  </a-button>
                </template>
              </a-card>
              <a-empty v-else description="未找到关联的达人信息" />
            </a-spin>
          </a-form-item>
        </template>

        <!-- 达人关联区域 -->
        <template v-if="!isEditMode || showChangeAssociation">
          <a-divider orientation="left">{{ isEditMode ? '更改达人关联' : '达人关联设置' }}</a-divider>

          <a-alert
            v-if="isEditMode && showChangeAssociation"
            message="注意：修改达人关联会影响现有的业务关系，请谨慎操作"
            type="warning"
            style="margin-bottom: 16px;"
            show-icon
          />

          <a-form-item label="关联方式" name="关联方式" required>
          <a-radio-group v-model:value="form.关联方式" @change="handleAssociationTypeChange">
            <a-radio value="existing">选择已有达人联系方式</a-radio>
            <a-radio value="create">创建新的达人联系方式</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 选择已有达人联系方式 -->
        <template v-if="form.关联方式 === 'existing'">
          <a-form-item label="搜索达人信息" name="搜索关键词">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索联系方式、类型、备注或平台账号"
              enter-button="搜索"
              @search="handleSearchTalentInfo"
              @change="handleSearchInputChange"
              allow-clear
            />
          </a-form-item>

          <a-form-item label="选择达人信息" name="补充信息id" required>
            <a-spin :spinning="searchLoading">
              <div class="talent-list" v-if="talentList.length > 0">
                <div 
                  v-for="talent in talentList" 
                  :key="talent.补充信息id"
                  class="talent-item"
                  :class="{ active: form.补充信息id === talent.补充信息id }"
                  @click="selectTalent(talent)"
                >
                  <div class="talent-info">
                    <div class="contact-method">
                      <strong>{{ talent.联系方式 }}</strong>
                      <a-tag color="blue" size="small">{{ talent.联系方式类型 }}</a-tag>
                    </div>
                    <div class="platform-info" v-if="talent.平台账号">
                      <span class="platform">{{ talent.平台 }}：{{ talent.平台账号 }}</span>
                    </div>
                    <div class="remark" v-if="talent.个人备注">
                      <span class="remark-text">备注：{{ talent.个人备注 }}</span>
                    </div>
                  </div>
                  <div class="select-indicator" v-if="form.补充信息id === talent.补充信息id">
                    <check-circle-filled style="color: #1890ff;" />
                  </div>
                </div>
              </div>
              <a-empty v-else-if="searchExecuted && !searchLoading" description="暂无匹配的达人信息" />
              <div v-else-if="!searchExecuted" class="search-tip">
                <a-typography-text type="secondary">请输入关键词搜索达人信息</a-typography-text>
              </div>
            </a-spin>
          </a-form-item>
        </template>

        <!-- 创建新的达人联系方式 -->
        <template v-if="form.关联方式 === 'create'">
          <a-form-item label="联系方式" name="['达人联系方式信息', '联系方式']" required>
            <a-input
              v-model:value="form.达人联系方式信息.联系方式"
              placeholder="请输入联系方式（微信号、手机号等）"
              :maxlength="50"
              @input="handleContactMethodInput"
            />
          </a-form-item>

          <a-form-item label="联系方式类型" name="['达人联系方式信息', '联系方式类型']" required>
            <a-select
              v-model:value="form.达人联系方式信息.联系方式类型"
              placeholder="请选择联系方式类型"
              @change="handleContactTypeChange"
            >
              <a-select-option value="微信">微信</a-select-option>
              <a-select-option value="手机">手机</a-select-option>
              <a-select-option value="QQ">QQ</a-select-option>
              <a-select-option value="邮箱">邮箱</a-select-option>
              <a-select-option value="其他">其他</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="个人备注" name="['达人联系方式信息', '个人备注']">
            <a-textarea
              v-model:value="form.达人联系方式信息.个人备注"
              placeholder="请输入个人备注（可选）"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>

          <a-form-item label="个人标签" name="['达人联系方式信息', '个人标签']">
            <a-select
              v-model:value="form.达人联系方式信息.个人标签"
              mode="tags"
              placeholder="请输入个人标签（可选）"
              :max-tag-count="5"
            >
            </a-select>
          </a-form-item>

          <a-form-item label="补充信息" name="['达人联系方式信息', '补充信息']">
            <a-textarea
              v-model:value="form.达人联系方式信息.补充信息"
              placeholder="请输入补充信息（可选）"
              :rows="3"
              :maxlength="1000"
              show-count
            />
          </a-form-item>
        </template>
        </template>

        <!-- 提交按钮 -->
        <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitLoading" size="large">
              {{ isEditMode ? '更新联系人' : '创建联系人' }}
            </a-button>
            <a-button @click="handleCancel" size="large">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { userContactApi } from '@/api/contact'
import { CheckCircleFilled, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { nextTick, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()

// 编辑模式状态
const isEditMode = ref(false)
const contactId = ref(null)
const showChangeAssociation = ref(false)
const currentTalentInfo = ref(null)
const loadingTalentInfo = ref(false)

// 表单引用
const formRef = ref()

// 表单数据
const form = reactive({
  姓名: '',
  寄样信息: [{
    收件人: '',
    地址: '',
    电话: ''
  }],
  关联方式: 'existing',
  补充信息id: null,
  达人联系方式信息: {
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: ''
  }
})

// 表单验证规则
const formRules = reactive({
  姓名: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  关联方式: [
    {
      required: true,
      message: '请选择关联方式',
      trigger: 'change',
      validator: (_rule, value) => {
        if (!isEditMode.value && !value) {
          return Promise.reject('请选择关联方式')
        }
        return Promise.resolve()
      }
    }
  ],
  补充信息id: [
    {
      required: true,
      message: '请选择要关联的达人信息',
      trigger: 'change',
      validator: (_rule, value) => {
        if (!isEditMode.value && form.关联方式 === 'existing' && !value) {
          return Promise.reject('请选择要关联的达人信息')
        }
        if (isEditMode.value && showChangeAssociation.value && form.关联方式 === 'existing' && !value) {
          return Promise.reject('请选择要关联的达人信息')
        }
        return Promise.resolve()
      }
    }
  ],
  [['达人联系方式信息', '联系方式']]: [
    {
      required: true,
      message: '请输入联系方式',
      trigger: 'blur',
      validator: (_rule, value) => {
        // 检查是否需要验证
        const needValidation = (!isEditMode.value && form.关联方式 === 'create') ||
                              (isEditMode.value && showChangeAssociation.value && form.关联方式 === 'create')

        if (!needValidation) {
          return Promise.resolve()
        }

        if (!value || !value.trim()) {
          return Promise.reject('请输入联系方式')
        }

        // 根据联系方式类型进行格式验证
        const contactType = form.达人联系方式信息.联系方式类型
        const trimmedValue = value.trim()

        if (!contactType) {
          return Promise.reject('请先选择联系方式类型')
        }

        // 验证格式
        const validationResult = validateContactMethod(trimmedValue, contactType)
        if (!validationResult.isValid) {
          return Promise.reject(validationResult.message)
        }

        return Promise.resolve()
      }
    }
  ],
  [['达人联系方式信息', '联系方式类型']]: [
    {
      required: true,
      message: '请选择联系方式类型',
      trigger: 'change',
      validator: (_rule, value) => {
        if (!isEditMode.value && form.关联方式 === 'create' && !value) {
          return Promise.reject('请选择联系方式类型')
        }
        if (isEditMode.value && showChangeAssociation.value && form.关联方式 === 'create' && !value) {
          return Promise.reject('请选择联系方式类型')
        }
        return Promise.resolve()
      }
    }
  ]
})

// 搜索相关状态
const searchKeyword = ref('')
const searchLoading = ref(false)
const searchExecuted = ref(false)
const talentList = ref([])
const submitLoading = ref(false)

// 搜索防抖定时器
let searchTimeout = null

// 添加寄样信息
const addShippingInfo = () => {
  form.寄样信息.push({
    收件人: '',
    地址: '',
    电话: ''
  })
}

// 删除寄样信息
const removeShippingInfo = (index) => {
  if (form.寄样信息.length > 1) {
    form.寄样信息.splice(index, 1)
  }
}

// 联系方式验证函数
const validateContactMethod = (value, type) => {
  if (!value || !value.trim()) {
    return { isValid: false, message: '请输入联系方式' }
  }

  const trimmedValue = value.trim()

  switch (type) {
    case '微信':
      // 微信号规则：6-20位，字母开头，可包含字母、数字、下划线、减号
      const wechatRegex = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
      if (!wechatRegex.test(trimmedValue)) {
        return {
          isValid: false,
          message: '微信号格式不正确，应为6-20位字符，以字母开头，可包含字母、数字、下划线、减号'
        }
      }
      break

    case '手机':
      // 中国手机号规则：11位数字，1开头
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(trimmedValue)) {
        return {
          isValid: false,
          message: '手机号格式不正确，应为11位数字，以1开头'
        }
      }
      break

    case 'QQ':
      // QQ号规则：5-11位数字，不能以0开头
      const qqRegex = /^[1-9]\d{4,10}$/
      if (!qqRegex.test(trimmedValue)) {
        return {
          isValid: false,
          message: 'QQ号格式不正确，应为5-11位数字，不能以0开头'
        }
      }
      break

    case '邮箱':
      // 邮箱规则：标准邮箱格式
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      if (!emailRegex.test(trimmedValue)) {
        return {
          isValid: false,
          message: '邮箱格式不正确，请输入有效的邮箱地址'
        }
      }
      break

    case '其他':
      // 其他类型：至少2个字符，不能全是空格
      if (trimmedValue.length < 2) {
        return {
          isValid: false,
          message: '联系方式至少需要2个字符'
        }
      }
      break

    default:
      return {
        isValid: false,
        message: '请选择联系方式类型'
      }
  }

  return { isValid: true, message: '' }
}

// 关联方式变化处理
const handleAssociationTypeChange = () => {
  // 重置相关字段
  form.补充信息id = null
  form.达人联系方式信息 = {
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: ''
  }
  searchKeyword.value = ''
  talentList.value = []
  searchExecuted.value = false

  // 清除验证错误
  if (formRef.value) {
    nextTick(() => {
      formRef.value.clearValidate([['达人联系方式信息', '联系方式'], ['达人联系方式信息', '联系方式类型']])
    })
  }
}

// 联系方式类型变化处理
const handleContactTypeChange = () => {
  // 清除相关字段的验证错误
  if (formRef.value) {
    // 使用nextTick确保DOM更新后清除验证
    nextTick(() => {
      formRef.value.clearValidate([['达人联系方式信息', '联系方式']])
      formRef.value.clearValidate([['达人联系方式信息', '联系方式类型']])
    })
  }
}

// 联系方式输入处理
const handleContactMethodInput = () => {
  // 清除联系方式字段的验证错误
  if (formRef.value) {
    // 使用nextTick确保DOM更新后清除验证
    nextTick(() => {
      formRef.value.clearValidate([['达人联系方式信息', '联系方式']])
    })
  }
}

// 搜索达人信息
const handleSearchTalentInfo = async () => {
  if (!searchKeyword.value.trim()) {
    message.warning('请输入搜索关键词')
    return
  }

  searchLoading.value = true
  searchExecuted.value = true

  try {
    const response = await userContactApi.searchTalentInfoForAssociation({
      关键词: searchKeyword.value.trim(),
      页码: 1,
      每页数量: 50
    })

    if (response.状态码 === 100) {
      talentList.value = response.数据.列表 || []
      if (talentList.value.length === 0) {
        message.info('未找到匹配的达人信息')
      }
    } else {
      message.error(response.消息 || '搜索失败')
      talentList.value = []
    }
  } catch (error) {
    console.error('搜索达人信息失败:', error)
    message.error('搜索失败')
    talentList.value = []
  } finally {
    searchLoading.value = false
  }
}

// 搜索输入变化（防抖搜索）
const handleSearchInputChange = () => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  // 如果输入为空，清空结果
  if (!searchKeyword.value.trim()) {
    talentList.value = []
    searchExecuted.value = false
    form.补充信息id = null
    return
  }

  // 设置新的防抖定时器
  searchTimeout = setTimeout(() => {
    handleSearchTalentInfo()
  }, 500)
}

// 选择达人
const selectTalent = (talent) => {
  form.补充信息id = talent.补充信息id
}

// 表单提交
const handleSubmit = async () => {
  try {
    // 验证表单
    await formRef.value.validate()

    // 过滤有效的寄样信息
    const 有效寄样信息 = form.寄样信息.filter(info =>
      info.收件人.trim() && info.地址.trim() && info.电话.trim()
    )

    // 构建提交数据
    const submitData = {
      姓名: form.姓名.trim(),
      寄样信息: 有效寄样信息.length > 0 ? 有效寄样信息 : null
    }

    // 只有在非编辑模式或编辑模式下选择了更改关联时才需要处理达人关联
    if (!isEditMode.value || showChangeAssociation.value) {
      submitData.关联方式 = form.关联方式

      if (form.关联方式 === 'existing') {
        if (!form.补充信息id) {
          message.error('请选择要关联的达人信息')
          return
        }
        submitData.补充信息id = form.补充信息id
      } else {
        if (!form.达人联系方式信息.联系方式.trim() || !form.达人联系方式信息.联系方式类型) {
          message.error('请完善达人联系方式信息')
          return
        }
        submitData.达人联系方式信息 = {
          联系方式: form.达人联系方式信息.联系方式.trim(),
          联系方式类型: form.达人联系方式信息.联系方式类型,
          个人备注: form.达人联系方式信息.个人备注.trim() || null,
          个人标签: form.达人联系方式信息.个人标签.length > 0 ? form.达人联系方式信息.个人标签 : null,
          补充信息: form.达人联系方式信息.补充信息.trim() || null
        }
      }
    }

    submitLoading.value = true

    let response
    if (isEditMode.value) {
      if (showChangeAssociation.value) {
        // 编辑模式且要更改关联：更新联系人并重新关联达人
        const editSubmitData = {
          ...submitData,
          用户联系人id: contactId.value
        }
        response = await userContactApi.createContactWithTalentAssociation(editSubmitData)

        if (response.状态码 === 100) {
          message.success('更新联系人并重新关联成功')
          router.push('/talent/user-contacts')
        } else {
          message.error(response.消息 || '更新失败')
        }
      } else {
        // 编辑模式：只更新联系人基本信息
        response = await userContactApi.updateContact({
          用户联系人id: contactId.value,
          姓名: submitData.姓名,
          寄样信息: submitData.寄样信息
        })

        if (response.状态码 === 100) {
          message.success('更新联系人成功')
          router.push('/talent/user-contacts')
        } else {
          message.error(response.消息 || '更新失败')
        }
      }
    } else {
      // 创建模式：创建联系人并关联达人
      response = await userContactApi.createContactWithTalentAssociation(submitData)

      if (response.状态码 === 100) {
        message.success('创建联系人并关联成功')
        router.push('/talent/user-contacts')
      } else {
        message.error(response.消息 || '创建失败')
      }
    }
  } catch (error) {
    console.error(isEditMode.value ? '更新联系人失败:' : '创建联系人失败:', error)
    if (error.errorFields) {
      message.error('请检查表单填写')
    } else {
      message.error(isEditMode.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 初始化页面
const initializePage = async () => {
  // 检查是否为编辑模式
  if (route.query.edit === 'true' && route.query.contactId) {
    isEditMode.value = true
    contactId.value = route.query.contactId
    await loadContactData()
  }
}

// 加载联系人数据
const loadContactData = async () => {
  try {
    const response = await userContactApi.getContactDetail(contactId.value)
    if (response.状态码 === 100) {
      const contactData = response.数据

      // 填充表单数据
      form.姓名 = contactData.姓名 || ''

      // 处理寄样信息
      if (contactData.寄样信息 && Array.isArray(contactData.寄样信息) && contactData.寄样信息.length > 0) {
        form.寄样信息 = contactData.寄样信息.map(info => ({
          收件人: info.收件人 || '',
          地址: info.地址 || '',
          电话: info.电话 || ''
        }))
      }

      // 编辑模式下默认选择"创建新的达人联系方式"
      form.关联方式 = 'create'

      // 加载关联的达人信息
      await loadTalentInfo(contactData)
    } else {
      message.error('加载联系人数据失败')
      router.push('/talent/user-contacts')
    }
  } catch (error) {
    console.error('加载联系人数据失败:', error)
    message.error('加载联系人数据失败')
    router.push('/talent/user-contacts')
  }
}

// 加载关联的达人信息
const loadTalentInfo = async (contactData) => {
  loadingTalentInfo.value = true
  try {
    // 从联系人数据中获取关联的达人信息
    if (contactData.关联达人信息) {
      currentTalentInfo.value = contactData.关联达人信息
    } else {
      currentTalentInfo.value = null
    }
  } catch (error) {
    console.error('加载达人信息失败:', error)
    message.warning('加载关联的达人信息失败')
    currentTalentInfo.value = null
  } finally {
    loadingTalentInfo.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.push('/talent/user-contacts')
}

// 页面初始化
onMounted(() => {
  initializePage()
})
</script>

<style scoped>
.create-contact-with-talent {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.main-card {
  max-width: 1200px;
  margin: 0 auto;
}

.shipping-info-section {
  max-width: 100%;
}

.shipping-info-item {
  margin-bottom: 16px;
}

.shipping-info-item:last-child {
  margin-bottom: 0;
}

.shipping-info-item .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.shipping-info-item .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.shipping-info-item .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.talent-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
}

.talent-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.talent-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.talent-item.active {
  border-color: #1890ff;
  background-color: #f6ffed;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.talent-item:last-child {
  margin-bottom: 0;
}

.talent-info {
  flex: 1;
}

.contact-method {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.contact-method strong {
  font-size: 16px;
  color: #262626;
}

.platform-info {
  margin-bottom: 4px;
}

.platform {
  font-size: 14px;
  color: #666;
}

.remark {
  margin-bottom: 0;
}

.remark-text {
  font-size: 12px;
  color: #999;
}

.select-indicator {
  font-size: 20px;
}

.search-tip {
  text-align: center;
  padding: 40px 20px;
}

.ant-divider-horizontal.ant-divider-with-text-left::before {
  width: 5%;
}

.ant-divider-horizontal.ant-divider-with-text-left::after {
  width: 95%;
}

.talent-info-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.talent-info-card .ant-card-body {
  padding: 16px;
}

.current-talent-info {
  margin-bottom: 12px;
}

.current-talent-info .ant-descriptions-item-label {
  font-weight: 600;
  color: #262626;
}

.current-talent-info .ant-descriptions-item-content {
  color: #595959;
}
</style>
