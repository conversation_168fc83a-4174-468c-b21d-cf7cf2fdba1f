from datetime import datetime
from typing import Optional, Union, List

from pydantic import BaseModel, Field


class 物流查询请求模型(BaseModel):
    """物流信息查询请求模型"""
    快递单号: str = Field(..., description="快递单号")
    收件人电话: Optional[str] = Field(None, description="收件人电话，可选") 

class 样品信息请求模型(BaseModel):
    """样品信息操作请求模型"""
    收件人: Optional[str] = Field(None, description="收件人姓名，可选")
    地址: Optional[str] = Field(None, description="收件地址，可选")
    电话: Optional[str] = Field(None, description="联系电话，可选")
    产品id: int = Field(..., description="产品id")
    数量: Optional[int] = Field(None, description="样品数量，可选")
    快递单号: Optional[str] = Field(None, description="快递单号，可选")
    寄样备注: Optional[str] = Field(None, description="寄样备注信息，可选")
    样品id: Optional[int] = Field(None, description="样品记录id，提供时表示更新现有记录")
    审核状态: Optional[int] = Field(0, description="审核状态: -1-拒绝、0-未审核、1-通过") 
    审核人ID: Optional[int] = Field(None, description="审核人用户id，可选")
    审核备注: Optional[str] = Field(None, description="审核备注信息，可选")
    审核时间: Optional[Union[datetime, str]] = Field(None, description="审核时间，可选")
    微信产品对接进度表id: Optional[int] = Field(None, description="微信产品对接进度表id，可选")
    快递状态: Optional[int] = Field(0, description="快递状态: -1-已退回、0-待出库、1-运输中、2-已签收")
    快递状态变更时间: Optional[Union[datetime, str]] = Field(None, description="快递状态最后变更时间，可选")

class 样品信息查询请求模型(BaseModel):
    """样品信息查询请求模型"""
    页码: Optional[int] = Field(1, description="页码，从1开始")
    每页数量: Optional[int] = Field(10, description="每页记录数量")
    收件人: Optional[str] = Field(None, description="收件人姓名查询条件，可选")
    产品id: Optional[int] = Field(None, description="产品id查询条件，可选")
    审核状态: Optional[int] = Field(None, description="审核状态查询条件，可选")
    快递单号: Optional[str] = Field(None, description="快递单号查询条件，可选")
    快递状态: Optional[int] = Field(None, description="快递状态查询条件，可选")

class 样品详情请求模型(BaseModel):
    """样品详情查询请求模型"""
    样品id: int = Field(..., description="样品记录id")

class 样品审核请求模型(BaseModel):
    """样品审核操作请求模型"""
    样品id: int = Field(..., description="样品记录id")
    审核状态: int = Field(..., description="审核状态: -1-拒绝、1-通过")
    审核备注: Optional[str] = Field(None, description="审核备注，可选")

class 更新快递状态请求模型(BaseModel):
    """更新快递状态请求模型"""
    样品id: int
    快递状态: str

class 更新快递单号请求模型(BaseModel):
    """更新快递单号请求模型"""
    样品id: int
    快递单号: str
    
    class Config:
        _json_schema_extra = {
            "example": {
                "样品id": 123,
                "快递单号": "SF1234567890"
            }
        }

class 更新物流状态请求模型(BaseModel):
    """更新物流状态请求模型（自动获取物流信息）"""
    样品id: int
    
    class Config:
        _json_schema_extra = {
            "example": {
                "样品id": 123
            }
        }

# 样品管理相关数据模型

class 样品申请模型(BaseModel):
    """
    样品申请模型
    
    用于用户申请样品的数据模型
    
    Attributes:
        产品id (int): 产品id
        申请数量 (int): 申请数量，默认为1
        申请原因 (Optional[str]): 申请原因说明
        收货地址 (str): 收货地址
        联系电话 (str): 联系电话
        备注 (Optional[str]): 备注信息
    """
    产品id: int = Field(..., description="产品id")
    申请数量: int = Field(1, description="申请数量")
    申请原因: Optional[str] = Field(None, description="申请原因")
    收货地址: str = Field(..., description="收货地址")
    联系电话: str = Field(..., description="联系电话")
    备注: Optional[str] = Field(None, description="备注")

class 样品列表查询模型(BaseModel):
    """
    样品列表查询模型
    
    用于查询样品申请列表的参数模型
    
    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20
        状态 (Optional[str]): 申请状态筛选
        开始日期 (Optional[datetime]): 申请开始日期
        结束日期 (Optional[datetime]): 申请结束日期
        产品名称 (Optional[str]): 产品名称搜索
        申请人 (Optional[str]): 申请人搜索
    """
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    状态: Optional[str] = Field(None, description="申请状态筛选")
    开始日期: Optional[datetime] = Field(None, description="申请开始日期")
    结束日期: Optional[datetime] = Field(None, description="申请结束日期")
    产品名称: Optional[str] = Field(None, description="产品名称搜索")
    申请人: Optional[str] = Field(None, description="申请人搜索")

class 样品管理模型(BaseModel):
    """
    样品管理模型
    
    用于管理员处理样品申请的数据模型
    
    Attributes:
        样品id (int): 样品申请ID
        处理状态 (str): 处理状态（审核中、已通过、已拒绝、已发货、已完成）
        审核意见 (Optional[str]): 审核意见
        快递单号 (Optional[str]): 快递单号
        快递公司 (Optional[str]): 快递公司
        发货时间 (Optional[datetime]): 发货时间
        预计到货时间 (Optional[datetime]): 预计到货时间
        备注 (Optional[str]): 管理员备注
    """
    样品id: int = Field(..., description="样品申请ID")
    处理状态: str = Field(..., description="处理状态")
    审核意见: Optional[str] = Field(None, description="审核意见")
    快递单号: Optional[str] = Field(None, description="快递单号")
    快递公司: Optional[str] = Field(None, description="快递公司")
    发货时间: Optional[datetime] = Field(None, description="发货时间")
    预计到货时间: Optional[datetime] = Field(None, description="预计到货时间")
    备注: Optional[str] = Field(None, description="管理员备注")

class 样品追踪模型(BaseModel):
    """
    样品追踪模型
    
    用于追踪样品物流状态的数据模型
    
    Attributes:
        样品id (int): 样品申请ID
        快递单号 (str): 快递单号
        快递公司 (str): 快递公司
    """
    样品id: int = Field(..., description="样品申请ID")
    快递单号: str = Field(..., description="快递单号")
    快递公司: str = Field(..., description="快递公司")

class 样品反馈模型(BaseModel):
    """
    样品反馈模型
    
    用于用户对样品进行反馈的数据模型
    
    Attributes:
        样品id (int): 样品申请ID
        满意度评分 (int): 满意度评分（1-5分）
        使用体验 (Optional[str]): 使用体验描述
        建议意见 (Optional[str]): 建议意见
        是否愿意推荐 (bool): 是否愿意推荐给其他人
        反馈图片 (Optional[List[str]]): 反馈图片URL列表
    """
    样品id: int = Field(..., description="样品申请ID")
    满意度评分: int = Field(..., ge=1, le=5, description="满意度评分（1-5分）")
    使用体验: Optional[str] = Field(None, description="使用体验描述")
    建议意见: Optional[str] = Field(None, description="建议意见")
    是否愿意推荐: bool = Field(..., description="是否愿意推荐")
    反馈图片: Optional[List[str]] = Field(None, description="反馈图片列表")

class 样品统计查询模型(BaseModel):
    """
    样品统计查询模型
    
    用于查询样品申请统计数据的参数模型
    
    Attributes:
        统计类型 (str): 统计类型（申请量、通过率、满意度等）
        时间范围 (str): 时间范围（7d、30d、90d、custom）
        开始日期 (Optional[datetime]): 自定义开始日期
        结束日期 (Optional[datetime]): 自定义结束日期
        产品筛选 (Optional[str]): 产品筛选条件
        状态筛选 (Optional[str]): 状态筛选条件
    """
    统计类型: str = Field(..., description="统计类型")
    时间范围: str = Field("30d", description="时间范围")
    开始日期: Optional[datetime] = Field(None, description="开始日期")
    结束日期: Optional[datetime] = Field(None, description="结束日期")
    产品筛选: Optional[str] = Field(None, description="产品筛选")
    状态筛选: Optional[str] = Field(None, description="状态筛选")