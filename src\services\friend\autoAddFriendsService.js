import { 主API客户端 as apiClient } from '../apiClientFactory'

/**
 * 自动添加好友服务类
 * 
 * 负责处理自动添加好友相关的API调用
 * 包括记录列表、统计概览、CRUD操作等功能
 */
class AutoAddFriendsService {
  
  /**
   * 获取概览统计数据
   * 
   * @returns {Promise<Object>} API响应对象
   */
  async getOverview() {
    try {
      const response = await apiClient.post('/wechat/automation/overview')
      return response
    } catch (error) {
      console.error('获取自动添加概览失败:', error)
      throw error
    }
  }

  /**
   * 获取添加记录列表
   * 支持分页、搜索和筛选功能
   * 
   * @param {Object} params - 查询参数
   * @param {number} params.页码 - 页码
   * @param {number} params.每页数量 - 每页显示数量
   * @param {string} [params.搜索关键词] - 搜索关键词
   * @param {number} [params.状态筛选] - 状态筛选
   * @param {number} [params.微信账号筛选] - 微信账号筛选
   * @param {Array} [params.时间范围] - 时间范围
   * @returns {Promise<Object>} API响应对象
   */
  async getRecordsList(params = {}) {
    try {
      const requestParams = {
        页码: params.页码 || 1,
        每页数量: params.每页数量 || 10,
        搜索关键词: params.搜索关键词 || '',
        状态筛选: params.状态筛选,
        微信账号筛选: params.微信账号筛选,
        时间范围: params.时间范围
      }
      
      const response = await apiClient.post('/wechat/automation/records-list', requestParams)
      return response
    } catch (error) {
      console.error('获取添加记录列表失败:', error)
      throw error
    }
  }

  /**
   * 获取微信账号列表
   * 用于筛选和表单选择
   * 
   * @returns {Promise<Object>} API响应对象
   */
  async getWechatAccounts() {
    try {
      const response = await apiClient.post('/wechat/automation/accounts')
      return response
    } catch (error) {
      console.error('获取微信账号列表失败:', error)
      throw error
    }
  }

  /**
   * 手动添加记录
   * 
   * @param {Object} recordData - 记录数据
   * @param {number} recordData.微信信息表id - 微信账号id
   * @param {string} recordData.目标联系方式 - 目标联系方式
   * @param {string} recordData.联系方式类型 - 联系方式类型
   * @param {string} [recordData.计划添加时间] - 计划添加时间
   * @param {string} [recordData.验证消息] - 验证消息
   * @returns {Promise<Object>} API响应对象
   */
  async manualAdd(recordData) {
    try {
      // 格式化数据
      const requestData = this.formatRecordData(recordData)
      
      const response = await apiClient.post('/wechat/automation/manual-add', requestData)
      return response
    } catch (error) {
      console.error('手动添加记录失败:', error)
      throw error
    }
  }



  /**
   * 删除单个添加记录
   * 
   * @param {number} recordId - 记录id
   * @returns {Promise<Object>} API响应对象
   */
  async deleteRecord(recordId) {
    try {
      if (!recordId) {
        throw new Error('记录id不能为空')
      }

      const response = await apiClient.post('/wechat/automation/delete-record', {
        记录id: recordId
      })
      return response
    } catch (error) {
      console.error('删除添加记录失败:', error)
      throw error
    }
  }

  /**
   * 批量删除添加记录
   * 
   * @param {Array<number>} recordIds - 记录id数组
   * @returns {Promise<Object>} API响应对象
   */
  async batchDelete(recordIds) {
    try {
      if (!Array.isArray(recordIds) || recordIds.length === 0) {
        throw new Error('记录id列表不能为空')
      }

      const response = await apiClient.post('/wechat/automation/batch-delete', {
        记录ids: recordIds
      })
      return response
    } catch (error) {
      console.error('批量删除失败:', error)
      throw error
    }
  }

  /**
   * 导出添加记录
   * 
   * @param {Object} filterParams - 筛选参数
   * @param {string} [filterParams.搜索关键词] - 搜索关键词
   * @param {number} [filterParams.状态筛选] - 状态筛选
   * @param {number} [filterParams.微信账号筛选] - 微信账号筛选
   * @param {Array} [filterParams.时间范围] - 时间范围
   * @returns {Promise<Blob>} 导出文件Blob
   */
  async exportRecords(filterParams = {}) {
    try {
      const response = await apiClient.post('/wechat/automation/export', {
        搜索关键词: filterParams.搜索关键词 || '',
        状态筛选: filterParams.状态筛选,
        微信账号筛选: filterParams.微信账号筛选,
        时间范围: filterParams.时间范围
      }, {
        responseType: 'blob'  // 设置响应类型为blob
      })
      
      return response
    } catch (error) {
      console.error('导出记录失败:', error)
      throw error
    }
  }

  /**
   * 获取记录详情
   * 
   * @param {number} recordId - 记录id
   * @returns {Promise<Object>} API响应对象
   */
  async getRecordDetail(recordId) {
    try {
      if (!recordId) {
        throw new Error('记录id不能为空')
      }

      const response = await apiClient.post('/wechat/automation/detail', {
        记录id: recordId
      })
      return response
    } catch (error) {
      console.error('获取记录详情失败:', error)
      throw error
    }
  }

  /**
   * 格式化记录数据
   * 在发送到后端前对数据进行格式化处理
   * 
   * @private
   * @param {Object} recordData - 原始记录数据
   * @returns {Object} 格式化后的记录数据
   */
  formatRecordData(recordData) {
    const formatted = { ...recordData }
    
    // 处理时间格式
    if (formatted.计划添加时间 && typeof formatted.计划添加时间 === 'object' && formatted.计划添加时间.format) {
      formatted.计划添加时间 = formatted.计划添加时间.format('YYYY-MM-DD HH:mm:ss')
    }
    
    // 确保数值类型
    if (formatted.微信信息表id) {
      formatted.微信信息表id = Number(formatted.微信信息表id)
    }
    
    // 清理空字符串
    Object.keys(formatted).forEach(key => {
      if (formatted[key] === '') {
        formatted[key] = null
      }
    })
    
    console.log('📝 格式化记录数据:', { original: recordData, formatted })
    
    return formatted
  }

  /**
   * 验证记录数据
   * 验证必填字段和数据格式
   * 
   * @param {Object} recordData - 记录数据
   * @returns {Object} 验证结果 { valid: boolean, errors: Array }
   */
  validateRecordData(recordData) {
    const errors = []
    
    // 必填字段验证
    if (!recordData.微信信息表id) {
      errors.push({ field: '微信信息表id', message: '请选择微信账号' })
    }
    
    if (!recordData.目标联系方式 || !recordData.目标联系方式.trim()) {
      errors.push({ field: '目标联系方式', message: '请输入目标联系方式' })
    }
    
    if (!recordData.联系方式类型) {
      errors.push({ field: '联系方式类型', message: '请选择联系方式类型' })
    }
    
    // 格式验证
    if (recordData.目标联系方式) {
      const contactType = recordData.联系方式类型
      const contactValue = recordData.目标联系方式.trim()
      
      if (contactType === '微信号' && contactValue.length < 6) {
        errors.push({ field: '目标联系方式', message: '微信号长度不能少于6位' })
      }
      
      if (contactType === '手机号' && !/^1[3-9]\d{9}$/.test(contactValue)) {
        errors.push({ field: '目标联系方式', message: '请输入正确的手机号格式' })
      }
      
      if (contactType === 'QQ号' && !/^\d{5,11}$/.test(contactValue)) {
        errors.push({ field: '目标联系方式', message: 'QQ号应为5-11位数字' })
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 获取状态文本
   * 
   * @param {number} status - 状态码
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusTexts = {
      1: '已添加成功',
      0: '待处理',
      '-1': '添加失败',
      '-2': '被拒绝',
      '-3': '账号异常',
      '-4': '频率限制'
    }
    return statusTexts[status] || '未知状态'
  }

  /**
   * 获取状态颜色
   * 
   * @param {number} status - 状态码
   * @returns {string} 颜色类型
   */
  getStatusColor(status) {
    const statusColors = {
      1: 'success',     // 已添加成功
      0: 'processing',  // 待处理
      '-1': 'error',    // 添加失败
      '-2': 'warning',  // 被拒绝
      '-3': 'error',    // 账号异常
      '-4': 'orange'    // 频率限制
    }
    return statusColors[status] || 'default'
  }

  /**
   * 格式化时间显示
   * 
   * @param {string} dateStr - 时间字符串
   * @param {string} format - 格式化模板
   * @returns {string} 格式化后的时间
   */
  formatDateTime(dateStr, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!dateStr) return ''
    
    try {
      const dayjs = require('dayjs')
      return dayjs(dateStr).format(format)
    } catch (error) {
      console.error('时间格式化失败:', error)
      return dateStr
    }
  }

  /**
   * 获取默认筛选参数
   * 
   * @returns {Object} 默认筛选参数
   */
  getDefaultFilterParams() {
    return {
      页码: 1,
      每页数量: 10,
      搜索关键词: '',
      状态筛选: null,
      微信账号筛选: null,
      时间范围: null
    }
  }

  /**
   * 获取默认手动添加表单数据
   * 
   * @returns {Object} 默认表单数据
   */
  getDefaultManualAddForm() {
    return {
      微信信息表id: null,
      目标联系方式: '',
      联系方式类型: '微信号',
      计划添加时间: null,
      验证消息: '你好，我是通过朋友介绍认识的，希望能加个好友。'
    }
  }
}

// 导出服务实例
export const autoAddFriendsService = new AutoAddFriendsService()

// 默认导出
export default autoAddFriendsService 