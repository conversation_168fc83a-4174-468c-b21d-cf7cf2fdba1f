"""
用户端 - LangChain智能体访问路由

功能：
1. 获取用户可用的智能体列表
2. 智能体对话接口
3. 对话历史查询
4. 智能体使用统计
"""

from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Body, Depends
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据模型.响应模型 import 统一响应模型
from 日志 import 应用日志器 as 路由日志器
from 日志 import 错误日志器
from 服务.LangChain_智能体服务 import LangChain智能体服务实例

# 创建路由器
用户LangChain智能体路由 = APIRouter(tags=["用户-LangChain智能体"])


# ==================== 请求模型定义 ====================


class 用户智能体对话请求模型(BaseModel):
    """用户智能体对话请求模型"""

    智能体id: int = Field(..., description="智能体id")
    消息内容: str = Field(..., description="用户消息内容", min_length=1)
    会话id: Optional[str] = Field(None, description="会话id")
    上下文信息: Optional[str] = Field(None, description="额外上下文信息")


class 用户智能体列表请求模型(BaseModel):
    """用户智能体列表请求模型"""

    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(20, description="每页数量", ge=1, le=100)

    搜索关键词: Optional[str] = Field(None, description="搜索关键词")


class 对话历史查询请求模型(BaseModel):
    """对话历史查询请求模型"""

    智能体id: Optional[int] = Field(None, description="智能体id过滤")
    会话id: Optional[str] = Field(None, description="会话id过滤")
    页码: int = Field(1, description="页码", ge=1)
    每页数量: int = Field(50, description="每页数量", ge=1, le=100)
    开始时间: Optional[str] = Field(None, description="开始时间")
    结束时间: Optional[str] = Field(None, description="结束时间")


class 清空智能体记忆请求模型(BaseModel):
    """清空智能体记忆请求模型"""

    智能体id: int = Field(..., description="智能体id")
    会话id: Optional[str] = Field(None, description="会话id，不指定则清空所有记忆")


# ==================== API接口实现 ====================


@用户LangChain智能体路由.post("/agents/available", summary="获取用户可用智能体列表")
async def 获取用户可用智能体列表(
    请求数据: 用户智能体列表请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取用户可用的智能体列表
    包括：分配给用户的智能体 + 公开智能体
    """
    try:
        # 初始化服务层
        if not LangChain智能体服务实例.已初始化:
            await LangChain智能体服务实例.初始化()

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "搜索关键词": 请求数据.搜索关键词,
        }

        # 调用服务层获取数据
        结果 = await LangChain智能体服务实例.获取用户可用智能体列表(
            用户["id"], 查询参数
        )

        路由日志器.info(
            f"用户 {用户['id']} 获取智能体列表成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取智能体列表成功")

    except Exception as e:
        错误日志器.error(
            f"获取用户智能体列表API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1601, f"获取智能体列表失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/chat", summary="用户与智能体对话")
async def 用户与智能体对话(
    请求数据: 用户智能体对话请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    用户与智能体对话的正式API接口

    功能特点:
    - 消耗用户算力
    - 记录对话历史
    - 支持结构化输出
    - 完整的用户体验优化

    请求参数:
    - **消息内容**: 用户要发送的消息
    - **会话id**: 可选的会话id，用于保持对话上下文
    - **上下文信息**: 可选的额外上下文信息
    """
    路由日志器.info(
        f"💬 用户对话请求 - 用户id: {用户['id']}, 智能体id: {请求数据.智能体id}"
    )

    try:
        # 初始化服务层
        路由日志器.info("🔧 初始化LangChain智能体服务...")
        if not LangChain智能体服务实例.已初始化:
            初始化结果 = await LangChain智能体服务实例.初始化()
            路由日志器.info(f"🔧 服务初始化结果: {初始化结果}")

        # 验证用户是否有权限访问该智能体
        路由日志器.info(
            f"🔍 验证用户访问权限: 用户{用户['id']} -> 智能体{请求数据.智能体id}"
        )
        权限验证结果 = await LangChain智能体服务实例.验证用户智能体访问权限(
            用户["id"], 请求数据.智能体id
        )

        if not 权限验证结果.get("success"):
            错误信息 = 权限验证结果.get("error", "无权限访问该智能体")
            路由日志器.warning(f"⚠️ 用户权限验证失败: {错误信息}")
            return 统一响应模型.失败(1403, 错误信息)

        # 生成会话id（如果未提供）
        会话id = (
            请求数据.会话id
            or f"user_{用户['id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        )
        路由日志器.info(f"🆔 使用会话id: {会话id}")

        # 调用服务层进行对话 - 正式模式，使用RAG增强对话
        路由日志器.info("🚀 开始调用智能体RAG增强对话服务...")
        对话结果 = await LangChain智能体服务实例.管理员RAG增强对话(
            智能体id=请求数据.智能体id,
            用户表id=用户["id"],
            用户消息=请求数据.消息内容,
            会话id=会话id,
            测试模式=False,  # 用户正式对话，非测试模式
        )

        路由日志器.info(f"📨 对话服务返回结果: status={对话结果.get('status')}")

        # 检查对话结果状态
        if 对话结果.get("status") == 100:  # 成功状态码
            路由日志器.info("✅ 用户智能体对话成功")

            # 提取data字段中的实际数据
            对话数据 = 对话结果.get("data", {})

            # 处理智能体回复 - 如果是结构化输出，解析JSON为对象
            智能体回复 = 对话数据.get("智能体回复")
            结构化输出 = 对话数据.get("结构化输出", False)
            输出格式 = 对话数据.get("输出格式", "text")

            # 如果是结构化输出且回复是JSON字符串，解析为对象
            if (结构化输出 or 输出格式 in ["json", "structured"]) and isinstance(
                智能体回复, str
            ):
                try:
                    if 智能体回复.strip().startswith(
                        "{"
                    ) and 智能体回复.strip().endswith("}"):
                        import json

                        智能体回复 = json.loads(智能体回复)
                        路由日志器.info("✅ 结构化输出JSON解析成功，返回对象格式")
                    else:
                        路由日志器.info("📝 结构化输出但非JSON格式，保持原始文本")
                except json.JSONDecodeError as e:
                    路由日志器.warning(f"⚠️ 结构化输出JSON解析失败: {e}，保持原始文本")
                except Exception as e:
                    路由日志器.warning(f"⚠️ 结构化输出处理异常: {e}，保持原始文本")

            # 构建用户友好的响应数据
            响应数据 = {
                "对话信息": {
                    "智能体回复": 智能体回复,
                    "会话id": 对话数据.get("会话id", 会话id),
                    "对话时间": 对话数据.get("对话时间"),
                    "处理时长": 对话数据.get("处理时长", 0),
                },
                "智能体信息": {
                    "智能体id": 对话数据.get("智能体id", 请求数据.智能体id),
                    "智能体名称": 对话数据.get("智能体名称", "智能助手"),
                    "输出格式": 输出格式,
                    "结构化输出": 结构化输出,
                },
                "使用统计": {
                    "令牌消耗": 对话数据.get("令牌消耗", 0),
                    "知识库使用": 对话数据.get("知识库使用", False),
                },
            }

            # 如果有RAG检索信息，添加到响应中
            if 对话数据.get("RAG检索信息"):
                响应数据["知识库信息"] = 对话数据.get("RAG检索信息")

            路由日志器.info("📋 构建用户对话响应数据完成")
            return 统一响应模型.成功(响应数据, "智能体对话成功")

        else:
            # 对话失败
            错误信息 = 对话结果.get("message", "对话失败")
            路由日志器.error(f"❌ 用户智能体对话失败: {错误信息}")
            return 统一响应模型.失败(1602, f"智能体对话失败: {错误信息}")

    except Exception as e:
        路由日志器.error(f"❌ 用户智能体对话异常: {str(e)}", exc_info=True)
        return 统一响应模型.失败(1603, f"智能体对话失败: {str(e)}")


@用户LangChain智能体路由.post("/conversations/history", summary="获取对话历史")
async def 获取对话历史(
    请求数据: 对话历史查询请求模型, 用户: dict = Depends(获取当前用户)
):
    """
    获取用户的对话历史记录
    """
    try:
        # 初始化服务层
        if not LangChain智能体服务实例.已初始化:
            await LangChain智能体服务实例.初始化()

        # 构建查询参数
        查询参数 = {
            "页码": 请求数据.页码,
            "每页数量": 请求数据.每页数量,
            "智能体id": 请求数据.智能体id,
            "会话id": 请求数据.会话id,
            "开始时间": 请求数据.开始时间,
            "结束时间": 请求数据.结束时间,
        }

        # 调用服务层获取数据
        结果 = await LangChain智能体服务实例.获取用户对话历史(用户["id"], 查询参数)

        路由日志器.info(
            f"用户 {用户['id']} 获取对话历史成功，返回 {结果['总数量']} 条记录"
        )

        return 统一响应模型.成功(结果, "获取对话历史成功")

    except Exception as e:
        错误日志器.error(
            f"获取对话历史API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1604, f"获取对话历史失败: {str(e)}")


@用户LangChain智能体路由.post("/agents/clear-memory", summary="清空智能体记忆")
async def 清空智能体记忆(
    请求数据: 清空智能体记忆请求模型 = Body(...), 用户: dict = Depends(获取当前用户)
):
    """
    清空智能体的记忆（用户操作）
    """
    try:
        # 初始化服务层
        if not LangChain智能体服务实例.已初始化:
            await LangChain智能体服务实例.初始化()

        # 调用服务层清空记忆
        结果 = await LangChain智能体服务实例.清空智能体记忆(
            用户["id"], 请求数据.智能体id
        )

        if 结果.get("success"):
            路由日志器.info(
                f"用户 {用户['id']} 清空智能体 {请求数据.智能体id} 记忆成功"
            )
            return 统一响应模型.成功(None, 结果.get("message", "智能体记忆清空成功"))
        else:
            return 统一响应模型.失败(1403, 结果.get("error", "清空智能体记忆失败"))

    except Exception as e:
        错误日志器.error(
            f"清空智能体记忆API异常 (用户: {用户['id']}, 智能体: {请求数据.智能体id}): {str(e)}",
            exc_info=True,
        )
        return 统一响应模型.失败(1605, f"清空智能体记忆失败: {str(e)}")


@用户LangChain智能体路由.post("/my-statistics", summary="获取我的智能体使用统计")
async def 获取我的智能体使用统计(用户: dict = Depends(获取当前用户)):
    """
    获取用户的智能体使用统计
    """
    try:
        # 初始化服务层
        if not LangChain智能体服务实例.已初始化:
            await LangChain智能体服务实例.初始化()

        # 调用服务层获取统计数据
        结果 = await LangChain智能体服务实例.获取用户智能体使用统计(用户["id"])

        if 结果.get("success"):
            路由日志器.info(f"用户 {用户['id']} 获取智能体使用统计成功")
            return 统一响应模型.成功(结果.get("统计数据"), "获取智能体使用统计成功")
        else:
            return 统一响应模型.失败(1606, 结果.get("error", "获取智能体使用统计失败"))

    except Exception as e:
        错误日志器.error(
            f"获取用户统计API异常 (用户: {用户['id']}): {str(e)}", exc_info=True
        )
        return 统一响应模型.失败(1606, f"获取使用统计失败: {str(e)}")
