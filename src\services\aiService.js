import api from './api'

/**
 * AI智能助手服务模块 - 简化高效版本
 * 专注核心对话功能，删除无关代码
 */

class AIService {
  constructor() {
    // 简化配置
    this.config = {
      timeout: 30000,
      maxRetries: 2
    }

    // 简化会话管理
    this.sessionContext = {
      sessionId: this.generateSessionId(),
      messageHistory: []
    }
  }

  /**
   * 生成简单的会话id
   */
  generateSessionId() {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substr(2, 8)
    return `ai_${timestamp}_${random}`
  }

  /**
   * 发送消息到AI助手 - 核心功能
   * @param {string} message - 用户消息
   * @returns {Promise<Object>} AI响应
   */
  async sendMessage(message) {
    try {
      // 基本验证
      if (!message || typeof message !== 'string') {
        throw new Error('消息内容不能为空')
      }

      // 构建请求数据
      const requestData = {
        message: message.trim(),
        sessionId: this.sessionContext.sessionId,
        timestamp: new Date().toISOString(),
        clientInfo: {
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      }

      // 发送请求 - 修正API路径
      const response = await api.post('/api/ai-assistant/chat', requestData)

      // 处理成功响应
      if (response.status === 100) {
        // 更新消息历史
        this.sessionContext.messageHistory.push(
          { role: 'user', content: message, timestamp: new Date() },
          { role: 'assistant', content: response.data.content, timestamp: new Date() }
        )

        // 限制历史记录长度
        if (this.sessionContext.messageHistory.length > 20) {
          this.sessionContext.messageHistory = this.sessionContext.messageHistory.slice(-20)
        }

        return {
          content: response.data.content,
          timestamp: response.data.timestamp
        }
      } else {
        throw new Error(response.msg || 'AI服务响应异常')
      }

    } catch (error) {
      console.error('AI消息发送失败:', error)

      // 简化错误处理
      if (error.response?.status === 429) {
        throw new Error('请求过于频繁，请稍后重试')
      } else if (error.response?.status >= 500) {
        throw new Error('AI服务暂时不可用，请稍后重试')
      } else {
        throw new Error('处理请求时发生错误，请稍后重试')
      }
    }
  }

  /**
   * 清空对话上下文
   */
  async clearContext() {
    try {
      const requestData = {
        sessionId: this.sessionContext.sessionId,
        timestamp: new Date().toISOString()
      }

      await api.post('/api/ai-assistant/clear-context', requestData)

      // 清空本地历史
      this.sessionContext.messageHistory = []
      this.sessionContext.sessionId = this.generateSessionId()

      return { success: true }
    } catch (error) {
      console.error('清空上下文失败:', error)
      throw new Error('清空对话失败')
    }
  }

  /**
   * 获取AI服务状态
   */
  async getStatus() {
    try {
      const response = await api.get('/api/ai-assistant/status')
      return response
    } catch (error) {
      console.error('获取AI状态失败:', error)
      return { success: false, message: '服务不可用' }
    }
  }
}

// 导出单例
export default new AIService() 