<template>
  <div class="user-contacts">


    <!-- 搜索和操作区域 -->
    <div class="search-filters">
      <a-card>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input-search
              v-model:value="searchForm.关键词"
              placeholder="搜索联系人姓名或寄样信息"
              enter-button="搜索"
              size="large"
              @search="handleSearch"
              allow-clear
            />
          </a-col>
          <a-col :span="4">
            <a-button type="default" size="large" @click="handleReset" block>
              <reload-outlined />
              重置
            </a-button>
          </a-col>
          <a-col :span="4">
            <a-button type="primary" size="large" @click="handleAddContactWithTalent" block>
              <plus-outlined />
              新增联系人
            </a-button>
          </a-col>
          <a-col :span="4">
            <a-button type="default" size="large" @click="refreshList" :loading="contactsLoading" block>
              <reload-outlined />
              刷新
            </a-button>
          </a-col>
        </a-row>
      </a-card>
    </div>

    <!-- 联系人列表 -->
    <div class="contacts-list">
      <a-card>
        <template #title>
          <div style="display: flex; align-items: center; gap: 12px;">
            <span>联系人列表</span>
            <a-tag color="blue" style="margin: 0;">
              总数：{{ pagination.total || 0 }}
            </a-tag>
          </div>
        </template>

        <a-spin :spinning="contactsLoading" tip="正在加载联系人信息...">
          <a-table
            :dataSource="contactsList"
            :columns="tableColumns"
            :pagination="paginationConfig"
            :rowKey="record => record.用户联系人id"
            @change="handleTableChange"
            size="middle"
            bordered
          >
            <!-- 姓名列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'name'">
                <div class="contact-name">
                  <a-typography-text strong>{{ record.姓名 }}</a-typography-text>
                </div>
              </template>

              <!-- 寄样信息列 -->
              <template v-else-if="column.key === 'address'">
                <div class="contact-address">
                  <template v-if="record.寄样信息 && record.寄样信息.length > 0">
                    <div class="shipping-info-preview">
                      <a-typography-text
                        :ellipsis="{ tooltip: getShippingInfoTooltip(record.寄样信息) }"
                        :content="`${record.寄样信息[0].收件人} - ${record.寄样信息[0].地址}`"
                      />
                      <a-tag v-if="record.寄样信息.length > 1" color="blue" size="small" style="margin-left: 8px;">
                        +{{ record.寄样信息.length - 1 }}
                      </a-tag>
                    </div>
                  </template>
                  <template v-else-if="record.寄样地址">
                    <!-- 兼容旧数据 -->
                    <a-typography-text
                      :ellipsis="{ tooltip: record.寄样地址 }"
                      type="warning"
                      :content="record.寄样地址"
                    />
                  </template>
                  <template v-else>
                    <a-typography-text type="secondary">未设置</a-typography-text>
                  </template>
                </div>
              </template>

              <!-- 创建时间列 -->
              <template v-else-if="column.key === 'createTime'">
                <a-typography-text type="secondary">
                  {{ record.创建时间 ? formatDateTime(record.创建时间) : '--' }}
                </a-typography-text>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="handleViewContact(record)">
                    <eye-outlined />
                    查看
                  </a-button>
                  <a-button type="link" size="small" @click="handleEditContact(record)">
                    <edit-outlined />
                    编辑
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个联系人吗？"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="handleDeleteContact(record)"
                  >
                    <a-button type="link" size="small" danger>
                      <delete-outlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-spin>
      </a-card>
    </div>


    <!-- 联系人详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="联系人详情"
      :width="600"
      :footer="null"
    >
      <div v-if="selectedContact" class="contact-detail">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="联系人ID">
            <a-typography-text copyable>{{ selectedContact.用户联系人id }}</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="姓名">
            <a-typography-text strong>{{ selectedContact.姓名 }}</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="寄样信息">
            <div v-if="selectedContact.寄样信息 && selectedContact.寄样信息.length > 0">
              <div
                v-for="(info, index) in selectedContact.寄样信息"
                :key="index"
                style="margin-bottom: 12px; padding: 12px; background: #fafafa; border-radius: 6px;"
              >
                <div><strong>收件人：</strong>{{ info.收件人 }}</div>
                <div><strong>联系电话：</strong>{{ info.电话 }}</div>
                <div><strong>寄样地址：</strong>{{ info.地址 }}</div>
              </div>
            </div>
            <a-typography-text v-else type="secondary">未设置</a-typography-text>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <a-typography-text type="secondary">
              {{ selectedContact.创建时间 ? formatDateTime(selectedContact.创建时间) : '--' }}
            </a-typography-text>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { userContactApi } from '@/api/contact'
import { formatDateTime } from '@/utils/dateUtils'
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const contactsLoading = ref(false)
const contactsList = ref([])
const detailModalVisible = ref(false)
const selectedContact = ref(null)

// 搜索表单
const searchForm = reactive({
  关键词: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 表格列配置
const tableColumns = [
  {
    title: '姓名',
    key: 'name',
    width: 150,
    fixed: 'left'
  },
  {
    title: '寄样信息',
    key: 'address',
    width: 300,
    ellipsis: true
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180,
    sorter: true
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]



// 计算属性
const paginationConfig = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100']
}))

// 方法定义
const loadContactsList = async () => {
  contactsLoading.value = true
  try {
    const params = {
      关键词: searchForm.关键词 || undefined
    }

    const response = await userContactApi.getContactList(params)

    if (response.状态码 === 100) {
      contactsList.value = response.数据 || []
      pagination.total = contactsList.value.length
    } else {
      message.error(response.消息 || '获取联系人列表失败')
    }
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    message.error('获取联系人列表失败')
  } finally {
    contactsLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.current = 1
  loadContactsList()
}

// 重置搜索
const handleReset = () => {
  searchForm.关键词 = ''
  pagination.current = 1
  loadContactsList()
}

// 刷新列表
const refreshList = () => {
  loadContactsList()
}

// 表格变化处理
const handleTableChange = (pag, filters, sorter) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadContactsList()
}





// 新增联系人（关联达人创建）
const handleAddContactWithTalent = () => {
  router.push('/talent/create-contact-with-talent')
}

// 编辑联系人
const handleEditContact = (record) => {
  // 跳转到编辑页面，传递联系人ID作为查询参数
  router.push({
    path: '/talent/create-contact-with-talent',
    query: {
      edit: true,
      contactId: record.用户联系人id
    }
  })
}

// 查看联系人详情
const handleViewContact = async (record) => {
  try {
    const response = await userContactApi.getContactDetail(record.用户联系人id)
    if (response.状态码 === 100) {
      selectedContact.value = response.数据
      detailModalVisible.value = true
    } else {
      message.error(response.消息 || '获取联系人详情失败')
    }
  } catch (error) {
    console.error('获取联系人详情失败:', error)
    message.error('获取联系人详情失败')
  }
}

// 删除联系人
const handleDeleteContact = async (record) => {
  try {
    const response = await userContactApi.deleteContact({
      用户联系人id: record.用户联系人id
    })

    if (response.状态码 === 100) {
      message.success('删除联系人成功')
      loadContactsList()
    } else {
      message.error(response.消息 || '删除联系人失败')
    }
  } catch (error) {
    console.error('删除联系人失败:', error)
    message.error('删除联系人失败')
  }
}



// 获取寄样信息的工具提示内容
const getShippingInfoTooltip = (寄样信息列表) => {
  if (!寄样信息列表 || 寄样信息列表.length === 0) {
    return '无寄样信息'
  }

  return 寄样信息列表.map((info, index) =>
    `${index + 1}. ${info.收件人} - ${info.电话}\n   ${info.地址}`
  ).join('\n\n')
}

// 页面初始化
onMounted(() => {
  loadContactsList()
})
</script>

<style scoped>
.user-contacts {
  padding: 0;
  background: #f5f5f5;
  min-height: 100vh;
}

.search-filters {
  margin-bottom: 16px;
}

.search-filters .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.contacts-list .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.contact-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.contact-address {
  max-width: 300px;
}

.shipping-info-preview {
  display: flex;
  align-items: center;
  gap: 8px;
}

.shipping-info-section {
  max-width: 100%;
}

.shipping-info-item {
  margin-bottom: 16px;
}

.shipping-info-item:last-child {
  margin-bottom: 0;
}

.shipping-info-item .ant-card {
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

.shipping-info-item .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.shipping-info-item .ant-card-head-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.contact-detail {
  padding: 16px 0;
}

.contact-detail .ant-descriptions {
  margin-top: 16px;
}

/* 表格样式优化 */
:deep(.ant-table) {
  border-radius: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f8f9ff;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.ant-btn-default {
  border-color: #d9d9d9;
  color: #595959;
}

.ant-btn-default:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 2px 8px;
}

/* 模态框样式 */
:deep(.ant-modal) {
  border-radius: 12px;
}

:deep(.ant-modal-header) {
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-modal-body) {
  padding: 24px;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #f0f0f0;
  padding: 16px 24px 20px;
  border-radius: 0 0 12px 12px;
}

/* 表单样式 */
:deep(.ant-form-item-label > label) {
  font-weight: 600;
  color: #262626;
}

:deep(.ant-input),
:deep(.ant-input-search),
:deep(.ant-select-selector),
:deep(.ant-textarea) {
  border-radius: 6px;
  border-color: #d9d9d9;
  transition: all 0.3s ease;
}

:deep(.ant-input:focus),
:deep(.ant-input-search .ant-input:focus),
:deep(.ant-select-focused .ant-select-selector),
:deep(.ant-textarea:focus) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 描述列表样式 */
:deep(.ant-descriptions-bordered .ant-descriptions-item-label) {
  background: #fafafa;
  font-weight: 600;
  color: #262626;
  width: 120px;
}

:deep(.ant-descriptions-bordered .ant-descriptions-item-content) {
  background: #fff;
  color: #595959;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filters .ant-col {
    margin-bottom: 8px;
  }

  .contact-address {
    max-width: 200px;
  }

  :deep(.ant-table) {
    font-size: 12px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
  }
}

@media (max-width: 576px) {
  .user-contacts {
    padding: 0 8px;
  }

  .search-filters .ant-row {
    flex-direction: column;
  }

  .search-filters .ant-col {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
