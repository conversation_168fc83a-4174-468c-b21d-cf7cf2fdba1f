"""
异步工作台服务
基于真实业务数据的工作台数据服务
"""

import asyncio
import hashlib
import json
import time
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional

# import aiomysql  # 已迁移到PostgreSQL，不再需要aiomysql

from 工具.时间范围工具 import 时间范围工具
# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例
from 数据.工作台数据 import 工作台数据访问层
from 日志 import 应用日志器, 系统日志器


class 异步工作台服务:
    """异步工作台服务类"""

    def __init__(self, 数据访问层: Optional[工作台数据访问层] = None):
        """初始化异步工作台服务"""
        self.数据库连接池 = 异步连接池实例
        self.数据访问层 = 数据访问层 or 工作台数据访问层(self.数据库连接池)
        # 缓存配置
        self._缓存 = {}
        self._缓存过期时间 = 300  # 5分钟缓存
        self._最大缓存大小 = 1000  # 最大缓存条目数

    def _生成缓存键(self, 前缀: str, **参数) -> str:
        """生成缓存键"""
        参数字符串 = json.dumps(参数, sort_keys=True, default=str)
        哈希值 = hashlib.md5(参数字符串.encode()).hexdigest()
        return f"{前缀}:{哈希值}"

    def _获取缓存(self, 缓存键: str) -> Optional[Any]:
        """获取缓存数据"""
        if 缓存键 in self._缓存:
            缓存项 = self._缓存[缓存键]
            当前时间 = time.time()

            # 检查是否过期
            if 当前时间 - 缓存项["时间"] < self._缓存过期时间:
                return 缓存项["数据"]
            else:
                # 删除过期缓存
                del self._缓存[缓存键]

        return None

    def _设置缓存(self, 缓存键: str, 数据: Any) -> None:
        """设置缓存数据"""
        # 如果缓存已满，删除最旧的条目
        if len(self._缓存) >= self._最大缓存大小:
            最旧键 = min(self._缓存.keys(), key=lambda k: self._缓存[k]["时间"])
            del self._缓存[最旧键]

        self._缓存[缓存键] = {"数据": 数据, "时间": time.time()}

    def _获取时间范围前缀(self, 时间范围: str) -> str:
        """获取时间范围对应的前缀"""
        时间前缀映射 = {
            "昨日": "昨日",
            "今日": "今日",
            "本周": "本周",
            "上周": "上周",
            "本月": "本月",
            "上月": "上月",
            "本季度": "本季度",
            "上季度": "上季度",
            "自定义": "期间",
        }
        return 时间前缀映射.get(时间范围, "期间")

    def _清理过期缓存(self) -> None:
        """清理过期缓存"""
        当前时间 = time.time()
        过期键列表 = []

        for 键, 缓存项 in self._缓存.items():
            if 当前时间 - 缓存项["时间"] >= self._缓存过期时间:
                过期键列表.append(键)

        for 键 in 过期键列表:
            del self._缓存[键]

    @staticmethod
    def _解析时间范围(
        时间范围: str, 开始日期: Optional[date] = None, 结束日期: Optional[date] = None
    ) -> tuple:
        """
        解析时间范围，返回开始和结束时间
        支持中文语义化参数和英文参数（向后兼容）

        参数:
            时间范围: 时间范围参数（支持中文：昨日、今日、本周、上周、本月、上月、本季度、上季度）
            开始日期: 自定义开始日期
            结束日期: 自定义结束日期

        返回:
            (开始时间, 结束时间) 元组
        """
        try:
            # 使用时间范围工具进行解析
            开始时间, 结束时间 = 时间范围工具.解析时间范围(时间范围, 开始日期, 结束日期)

            # 记录时间范围解析日志
            时间范围描述 = 时间范围工具.获取时间范围描述(时间范围)
            系统日志器.debug(
                f"时间范围解析: {时间范围} -> {时间范围描述} ({开始时间} 到 {结束时间})"
            )

            return 开始时间, 结束时间

        except Exception as e:
            系统日志器.error(f"时间范围解析失败: {时间范围}, 错误: {e}")
            # 默认返回今日
            现在 = datetime.now()
            开始时间 = datetime.combine(现在.date(), datetime.min.time())
            结束时间 = 现在
            return 开始时间, 结束时间

    async def 获取个人微信运营数据(
        self,
        用户id: int,
        时间范围: str = "本月",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取个人微信运营数据"""
        try:
            # 解析时间范围
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取微信运营统计数据
            统计数据 = await self.数据访问层.获取_微信运营统计_数据(用户id, 开始时间, 结束时间)

            微信账号数 = (统计数据.get("微信账号数", 0) if 统计数据 else 0) or 0
            好友总数 = (统计数据.get("好友总数", 0) if 统计数据 else 0) or 0
            今日新增好友 = (统计数据.get("今日新增好友", 0) if 统计数据 else 0) or 0
            昨日新增好友 = (统计数据.get("昨日新增好友", 0) if 统计数据 else 0) or 0
            本周新增好友 = (统计数据.get("本周新增好友", 0) if 统计数据 else 0) or 0
            本月新增好友 = (统计数据.get("本月新增好友", 0) if 统计数据 else 0) or 0
            时间范围新增好友 = (统计数据.get("时间范围新增好友", 0) if 统计数据 else 0) or 0

            # 计算平均好友数
            平均好友数 = (
                round(好友总数 / 微信账号数, 1) if 微信账号数 > 0 else 0
            )

            # 根据时间范围确定显示的新增好友数和标题
            if 时间范围 == "今日":
                当前新增好友 = 今日新增好友
                新增好友标题 = "今日新增"
            elif 时间范围 == "昨日":
                当前新增好友 = 昨日新增好友
                新增好友标题 = "昨日新增"
            elif 时间范围 == "本周":
                当前新增好友 = 本周新增好友
                新增好友标题 = "本周新增"
            elif 时间范围 == "上周":
                当前新增好友 = 时间范围新增好友
                新增好友标题 = "上周新增"
            elif 时间范围 == "本月":
                当前新增好友 = 本月新增好友
                新增好友标题 = "本月新增"
            elif 时间范围 == "上月":
                当前新增好友 = 时间范围新增好友
                新增好友标题 = "上月新增"
            elif 时间范围 == "本季度":
                当前新增好友 = 时间范围新增好友
                新增好友标题 = "本季度新增"
            elif 时间范围 == "上季度":
                当前新增好友 = 时间范围新增好友
                新增好友标题 = "上季度新增"
            else:
                当前新增好友 = 时间范围新增好友
                新增好友标题 = "期间新增"

            return {
                "微信账号数": 微信账号数,
                "好友总数": 好友总数,
                "今日新增好友": 今日新增好友,
                "昨日新增好友": 昨日新增好友,
                "本周新增好友": 本周新增好友,
                "本月新增好友": 本月新增好友,
                "时间范围新增好友": 时间范围新增好友,
                "当前新增好友": 当前新增好友,
                "新增好友标题": 新增好友标题,
                "平均好友数": 平均好友数,
                "指标卡片": [
                    {
                        "标题": "微信账号",
                        "数值": 微信账号数,
                        "格式化数值": str(微信账号数),
                        "趋势": "稳定",
                        "趋势类型": "stable",
                        "图标": "wechat",
                        "颜色": "#1890ff",
                    },
                    {
                        "标题": "好友总数",
                        "数值": 好友总数,
                        "格式化数值": f"{好友总数:,}",
                        "趋势": f"+{今日新增好友}",
                        "趋势数值": 今日新增好友,
                        "趋势类型": "up" if 今日新增好友 > 0 else "stable",
                        "图标": "team",
                        "颜色": "#52c41a",
                    },
                    {
                        "标题": 新增好友标题,
                        "数值": 当前新增好友,
                        "格式化数值": str(当前新增好友),
                        "趋势": "新增好友",
                        "趋势类型": "up" if 当前新增好友 > 0 else "stable",
                        "图标": "user-add",
                        "颜色": "#fa8c16",
                    },
                ],
            }
        except Exception as e:
            系统日志器.error(f"获取个人微信运营数据失败: {str(e)}")
            raise e

    async def 获取微信运营核心指标(
        self,
        用户id: int,
        时间范围: str = "今日",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """
        获取微信运营核心指标数据

        7个关键指标：
        1. 微信账号数量 - 用户绑定的有效微信账号总数
        2. 好友总数 - 当前用户微信账号的好友总数量
        3. 今日新增 - 今日新增的好友数量
        4. 发送好友请求数 - 主动发送的好友请求总数
        5. 沟通好友数 - 有过沟通交流的好友数量
                  6. 互动好友数 - 我方最后一条消息发送时间与对方最后一条消息发送时间都有的好友数量
        """
        try:
            # 生成缓存键
            缓存键 = self._生成缓存键(
                "wechat_core_metrics",
                用户id=用户id,
                时间范围=时间范围,
                开始日期=开始日期,
                结束日期=结束日期,
            )

            # 尝试从缓存获取数据
            缓存数据 = self._获取缓存(缓存键)
            if 缓存数据:
                系统日志器.debug(f"从缓存获取微信运营核心指标: {缓存键}")
                return 缓存数据

            # 解析时间范围
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取微信核心指标数据
            统计数据 = await self.数据访问层.获取_微信核心指标_数据(用户id, 开始时间, 结束时间)

            # 提取数据并处理空值
            微信账号数量 = (统计数据.get("微信账号数量", 0) if 统计数据 else 0) or 0
            好友总数 = (统计数据.get("好友总数", 0) if 统计数据 else 0) or 0
            今日新增 = (统计数据.get("今日新增", 0) if 统计数据 else 0) or 0
            昨日新增 = (统计数据.get("昨日新增", 0) if 统计数据 else 0) or 0
            本周新增 = (统计数据.get("本周新增", 0) if 统计数据 else 0) or 0
            上周新增 = (统计数据.get("上周新增", 0) if 统计数据 else 0) or 0
            本月新增 = (统计数据.get("本月新增", 0) if 统计数据 else 0) or 0
            上月新增 = (统计数据.get("上月新增", 0) if 统计数据 else 0) or 0
            本季度新增 = (统计数据.get("本季度新增", 0) if 统计数据 else 0) or 0
            上季度新增 = (统计数据.get("上季度新增", 0) if 统计数据 else 0) or 0
            时间范围新增 = (统计数据.get("时间范围新增", 0) if 统计数据 else 0) or 0
            发送好友请求数 = (统计数据.get("发送好友请求数", 0) if 统计数据 else 0) or 0
            沟通好友数 = (统计数据.get("沟通好友数", 0) if 统计数据 else 0) or 0
            互动好友数 = (统计数据.get("互动好友数", 0) if 统计数据 else 0) or 0

            # 统一使用时间范围新增的结果，确保逻辑一致性
            # 所有时间范围都使用基于时间范围工具解析后的时间范围进行查询
            当前时间范围新增 = 时间范围新增

            # 获取时间范围前缀（复用达人管理模块的逻辑）
            时间前缀 = self._获取时间范围前缀(时间范围)

            # 根据时间范围生成动态标题和描述
            时间范围标题映射 = {
                "今日": "今日新增",
                "昨日": "昨日新增",
                "本周": "本周新增",
                "上周": "上周新增",
                "本月": "本月新增",
                "上月": "上月新增",
                "本季度": "本季度新增",
                "上季度": "上季度新增",
            }

            时间范围描述映射 = {
                "今日": "今日新增的好友数量",
                "昨日": "昨日新增的好友数量",
                "本周": "本周新增的好友数量",
                "上周": "上周新增的好友数量",
                "本月": "本月新增的好友数量",
                "上月": "上月新增的好友数量",
                "本季度": "本季度新增的好友数量",
                "上季度": "上季度新增的好友数量",
            }

            # 构建指标卡片数据
            指标卡片 = [
                    {
                        "标题": "微信账号",
                        "数值": 微信账号数量,
                        "格式化数值": str(微信账号数量),
                        "趋势": f"总计{微信账号数量}个",
                        "趋势数值": 微信账号数量,
                        "趋势类型": "stable",
                        "图标": "wechat",
                        "颜色": "#1890ff",
                        "描述": "绑定的有效微信账号总数",
                    },
                    {
                        "标题": "好友总数",
                        "数值": 好友总数,
                        "格式化数值": f"{好友总数:,}",
                        "趋势": f"总计{好友总数:,}人",
                        "趋势数值": 好友总数,
                        "趋势类型": "stable",
                        "图标": "team",
                        "颜色": "#52c41a",
                        "描述": "所有微信账号的好友总数量",
                    },
                    {
                        "标题": 时间范围标题映射.get(时间范围, "期间新增"),
                        "数值": 当前时间范围新增,
                        "格式化数值": str(当前时间范围新增),
                        "趋势": f"+{当前时间范围新增}",
                        "趋势数值": 当前时间范围新增,
                        "趋势类型": "up" if 当前时间范围新增 > 0 else "stable",
                        "图标": "user-add",
                        "颜色": "#fa8c16",
                        "描述": 时间范围描述映射.get(
                            时间范围, f"{时间范围}新增的好友数量"
                        ),
                    },
                    {
                        "标题": f"{时间前缀}发送请求",
                        "数值": 发送好友请求数,
                        "格式化数值": str(发送好友请求数),
                        "趋势": f"+{发送好友请求数}",
                        "趋势数值": 发送好友请求数,
                        "趋势类型": "up" if 发送好友请求数 > 0 else "stable",
                        "图标": "send",
                        "颜色": "#722ed1",
                        "描述": f"{时间前缀}发送的好友请求数量",
                    },
                    {
                        "标题": f"{时间前缀}沟通好友",
                        "数值": 沟通好友数,
                        "格式化数值": str(沟通好友数),
                        "趋势": f"+{沟通好友数}",
                        "趋势数值": 沟通好友数,
                        "趋势类型": "up" if 沟通好友数 > 0 else "stable",
                        "图标": "message",
                        "颜色": "#eb2f96",
                        "描述": f"{时间前缀}有沟通交流的好友数量",
                    },
                    {
                        "标题": f"{时间前缀}互动好友",
                        "数值": 互动好友数,
                        "格式化数值": str(互动好友数),
                        "趋势": f"+{互动好友数}",
                        "趋势数值": 互动好友数,
                        "趋势类型": "up" if 互动好友数 > 0 else "stable",
                        "图标": "interaction",
                        "颜色": "#f5222d",
                        "描述": "我方和对方都有消息记录的好友数量",
                    },
            ]

            结果数据 = {
                "微信账号数量": 微信账号数量,
                "好友总数": 好友总数,
                "今日新增": 今日新增,
                "昨日新增": 昨日新增,
                "本周新增": 本周新增,
                "上周新增": 上周新增,
                "本月新增": 本月新增,
                "上月新增": 上月新增,
                "本季度新增": 本季度新增,
                "上季度新增": 上季度新增,
                "时间范围新增": 时间范围新增,
                "当前时间范围新增": 当前时间范围新增,
                "发送好友请求数": 发送好友请求数,
                "沟通好友数": 沟通好友数,
                "互动好友数": 互动好友数,
                "指标卡片": 指标卡片,
                "时间范围": 时间范围,
                "查询时间": {
                    "开始时间": 开始时间.strftime("%Y-%m-%d %H:%M:%S"),
                    "结束时间": 结束时间.strftime("%Y-%m-%d %H:%M:%S"),
                },
            }

            # 将结果存入缓存
            self._设置缓存(缓存键, 结果数据)
            系统日志器.debug(f"微信运营核心指标已缓存: {缓存键}")

            return 结果数据

        except Exception as e:
            系统日志器.error(f"获取微信运营核心指标失败: {str(e)}")
            raise e

    # 删除错误的_时间范围转天数方法，改用时间范围工具进行正确的时间解析

    async def 获取邀约业务数据(
        self,
        用户id: int,
        时间范围: str = "本月",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取邀约业务数据"""
        try:
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取邀约业务数据
            邀约数据 = await self.数据访问层.获取_邀约业务_数据(用户id, 开始时间, 结束时间)

            邀约总数 = (邀约数据.get("邀约总数", 0) if 邀约数据 else 0) or 0
            今日邀约数 = (邀约数据.get("今日邀约数", 0) if 邀约数据 else 0) or 0
            昨日邀约数 = (邀约数据.get("昨日邀约数", 0) if 邀约数据 else 0) or 0
            本周邀约数 = (邀约数据.get("本周邀约数", 0) if 邀约数据 else 0) or 0
            本月邀约数 = (邀约数据.get("本月邀约数", 0) if 邀约数据 else 0) or 0
            时间范围邀约数 = (邀约数据.get("时间范围邀约数", 0) if 邀约数据 else 0) or 0
            意向合作数 = (邀约数据.get("意向合作数", 0) if 邀约数据 else 0) or 0
            已建联数 = (邀约数据.get("已建联数", 0) if 邀约数据 else 0) or 0
            合作中数 = (邀约数据.get("合作中数", 0) if 邀约数据 else 0) or 0

            # 计算成功率
            邀约成功率 = (
                round((意向合作数 + 已建联数 + 合作中数) / 邀约总数 * 100, 1)
                if 邀约总数 > 0
                else 0
            )

            # 根据时间范围确定当前邀约数
            if 时间范围 == "1d":
                当前邀约数 = 今日邀约数
                邀约标题 = "今日邀约"
            elif 时间范围 == "yesterday":
                当前邀约数 = 昨日邀约数
                邀约标题 = "昨日邀约"
            elif 时间范围 == "7d":
                当前邀约数 = 本周邀约数
                邀约标题 = "7日邀约"
            elif 时间范围 == "30d":
                当前邀约数 = 本月邀约数
                邀约标题 = "30日邀约"
            elif 时间范围 == "90d":
                当前邀约数 = 时间范围邀约数
                邀约标题 = "90日邀约"
            else:
                当前邀约数 = 时间范围邀约数
                邀约标题 = "期间邀约"

            return {
                "邀约总数": 邀约总数,
                "今日邀约数": 今日邀约数,
                "昨日邀约数": 昨日邀约数,
                "本周邀约数": 本周邀约数,
                "本月邀约数": 本月邀约数,
                "时间范围邀约数": 时间范围邀约数,
                "当前邀约数": 当前邀约数,
                "邀约标题": 邀约标题,
                "邀约成功率": 邀约成功率,
                "意向合作数": 意向合作数,
                "已建联数": 已建联数,
                "合作中数": 合作中数,
                "指标卡片": [
                    {
                        "标题": "总邀约数",
                        "数值": 邀约总数,
                        "格式化数值": f"{邀约总数:,}",
                        "趋势": f"+{今日邀约数}",
                        "趋势数值": 今日邀约数,
                        "趋势类型": "up" if 今日邀约数 > 0 else "stable",
                        "图标": "mail",
                        "颜色": "#1890ff",
                    }
                ],
            }

        except Exception as e:
            系统日志器.error(f"获取邀约业务数据失败: {str(e)}")
            raise e

    async def 获取达人管理数据(
        self,
        用户id: int,
        时间范围: str = "本月",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取达人管理数据"""
        try:
            # 解析时间范围
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取达人认领数据
            达人数据 = await self.数据访问层.获取_达人认领_数据(用户id, 开始时间, 结束时间)

            认领达人数 = (达人数据.get("认领达人数", 0) if 达人数据 else 0) or 0
            有效认领数 = (达人数据.get("有效认领数", 0) if 达人数据 else 0) or 0
            今日新增认领 = (达人数据.get("今日新增认领", 0) if 达人数据 else 0) or 0
            昨日新增认领 = (达人数据.get("昨日新增认领", 0) if 达人数据 else 0) or 0
            本周新增认领 = (达人数据.get("本周新增认领", 0) if 达人数据 else 0) or 0
            本月新增认领 = (达人数据.get("本月新增认领", 0) if 达人数据 else 0) or 0
            时间范围新增认领 = (达人数据.get("时间范围新增认领", 0) if 达人数据 else 0) or 0

            # 计算活跃度（基于多维度评分模型）
            达人活跃数, 达人活跃率 = await self._计算达人活跃度(
                用户id, 有效认领数
            )

            # 根据时间范围确定当前新增认领数
            if 时间范围 == "1d":
                当前新增认领 = 今日新增认领
                认领标题 = "今日认领"
            elif 时间范围 == "yesterday":
                当前新增认领 = 昨日新增认领
                认领标题 = "昨日认领"
            elif 时间范围 == "7d":
                当前新增认领 = 本周新增认领
                认领标题 = "7日认领"
            elif 时间范围 == "30d":
                当前新增认领 = 本月新增认领
                认领标题 = "30日认领"
            else:
                当前新增认领 = 时间范围新增认领
                认领标题 = "期间认领"

            return {
                    "认领达人数": 认领达人数,
                    "有效认领数": 有效认领数,
                    "今日新增认领": 今日新增认领,
                    "昨日新增认领": 昨日新增认领,
                    "本周新增认领": 本周新增认领,
                    "本月新增认领": 本月新增认领,
                    "时间范围新增认领": 时间范围新增认领,
                    "当前新增认领": 当前新增认领,
                    "认领标题": 认领标题,
                    "达人活跃数": 达人活跃数,
                    "达人活跃率": 达人活跃率,
                    "指标卡片": [
                        {
                            "标题": "认领达人",
                            "数值": 认领达人数,
                            "格式化数值": str(认领达人数),
                            "趋势": f"+{当前新增认领}",
                            "趋势数值": 当前新增认领,
                            "趋势类型": "up" if 当前新增认领 > 0 else "stable",
                            "图标": "user-add",
                            "颜色": "#1890ff",
                        },
                        {
                            "标题": "有效认领",
                            "数值": 有效认领数,
                            "格式化数值": str(有效认领数),
                            "趋势": f"有效率{round(有效认领数 / 认领达人数 * 100, 1) if 认领达人数 > 0 else 0}%",
                            "趋势类型": "up",
                            "图标": "check-circle",
                            "颜色": "#52c41a",
                        },
                        {
                            "标题": "活跃达人",
                            "数值": 达人活跃数,
                            "格式化数值": str(达人活跃数),
                            "趋势": f"活跃度{达人活跃率}分",
                            "趋势数值": 达人活跃率,
                            "趋势类型": "up"
                            if 达人活跃率 >= 70
                            else "stable"
                            if 达人活跃率 >= 40
                            else "down",
                            "图标": "fire",
                            "颜色": "#fa8c16",
                            "描述": "基于沟通、业务、信息、时效四维度评分的活跃达人数量",
                        },
                    ],
                }
        except Exception as e:
            系统日志器.error(f"获取达人管理数据失败: {str(e)}")
            raise e

    async def _计算达人活跃度(
        self, 用户id: int, 有效认领数: int
    ) -> tuple[int, float]:
        """
        计算达人活跃度 - 多维度评分模型

        评分体系（总分100分）：
        1. 沟通活跃度（40分）：基于微信消息互动时间
        2. 业务参与度（30分）：基于寄样申请、产品对接等行为
        3. 信息完整度（20分）：基于补充信息完整性
        4. 认领时效性（10分）：基于认领时间的新旧

        活跃阈值：
        - 高活跃：≥70分
        - 中活跃：40-69分
        - 低活跃：20-39分
        - 不活跃：<20分
        """
        if 有效认领数 == 0:
            return 0, 0.0

        try:
            # 调用数据访问层获取达人活跃度详细数据
            达人记录 = await self.数据访问层.获取_达人活跃度详细_数据(用户id)

            if not 达人记录:
                return 0, 0.0

            现在时间 = datetime.now()
            活跃达人数 = 0
            总活跃分数 = 0

            for 记录 in 达人记录:
                活跃分数 = 0

                # 1. 沟通活跃度（40分）
                最近消息时间 = 记录.get("最近消息时间")
                if 最近消息时间 and 最近消息时间 != datetime(1970, 1, 1):
                    消息间隔天数 = (现在时间 - 最近消息时间).days
                    if 消息间隔天数 <= 7:
                        活跃分数 += 40
                    elif 消息间隔天数 <= 30:
                        活跃分数 += 30
                    elif 消息间隔天数 <= 90:
                        活跃分数 += 20
                    else:
                        活跃分数 += 10

                # 2. 业务参与度（30分）
                最近寄样时间 = 记录.get("最近寄样时间")
                寄样申请次数 = 记录.get("寄样申请次数", 0)

                if 最近寄样时间:
                    寄样间隔天数 = (现在时间 - 最近寄样时间).days
                    if 寄样间隔天数 <= 30:
                        活跃分数 += 20  # 近期有寄样
                    else:
                        活跃分数 += 5  # 历史有寄样

                if 寄样申请次数 > 0:
                    活跃分数 += min(寄样申请次数 * 2, 10)  # 每次寄样+2分，最多10分

                # 3. 信息完整度（20分）
                if 记录.get("有联系方式"):
                    活跃分数 += 10
                if 记录.get("有个人标签"):
                    活跃分数 += 5
                if 记录.get("有备注信息"):
                    活跃分数 += 5

                # 4. 认领时效性（10分）
                认领时间 = 记录.get("认领时间")
                if 认领时间:
                    认领间隔天数 = (现在时间 - 认领时间).days
                    if 认领间隔天数 <= 30:
                        活跃分数 += 10
                    elif 认领间隔天数 <= 90:
                        活跃分数 += 7
                    elif 认领间隔天数 <= 180:
                        活跃分数 += 5
                    else:
                        活跃分数 += 3

                # 判断是否为活跃达人（≥40分）
                if 活跃分数 >= 40:
                    活跃达人数 += 1

                总活跃分数 += 活跃分数

            # 计算平均活跃率
            平均活跃分数 = 总活跃分数 / len(达人记录) if 达人记录 else 0
            达人活跃率 = round(平均活跃分数, 1)

            系统日志器.info(
                f"达人活跃度计算完成 - 用户id: {用户id}, 有效认领数: {有效认领数}, 活跃达人数: {活跃达人数}, 平均活跃分数: {平均活跃分数}"
            )

            return 活跃达人数, 达人活跃率

        except Exception as e:
            系统日志器.error(f"计算达人活跃度失败: {str(e)}")
            # 降级为简单计算
            fallback_活跃数 = int(有效认领数 * 0.4)  # 降低默认比例
            fallback_活跃率 = 40.0
            return fallback_活跃数, fallback_活跃率

    async def 获取分平台达人管理统计(
        self,
        用户id: int,
        时间范围: str = "本月",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取分平台达人管理统计数据（带缓存）"""
        try:
            # 生成缓存键
            缓存键 = self._生成缓存键(
                "talent_platform_stats",
                用户id=用户id,
                时间范围=时间范围,
                开始日期=开始日期,
                结束日期=结束日期,
            )

            # 尝试从缓存获取数据
            缓存数据 = self._获取缓存(缓存键)
            if 缓存数据:
                系统日志器.debug(f"从缓存获取分平台达人管理统计: {缓存键}")
                return 缓存数据

            # 解析时间范围
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 获取分平台基础数据
            基础数据 = await self._获取分平台基础数据(用户id, 时间范围, 开始时间, 结束时间, 开始日期, 结束日期)

            # 构建结果数据
            结果数据 = await self._构建分平台统计结果(基础数据, 时间范围)

            # 将结果存入缓存
            self._设置缓存(缓存键, 结果数据)
            系统日志器.info(f"[获取分平台达人管理统计] 分平台达人管理统计已缓存: {缓存键}")

            return 结果数据

        except Exception as e:
            系统日志器.error(f"获取分平台达人管理统计失败: {str(e)}")
            # 返回默认数据，确保前端不会崩溃
            return self._构建分平台默认数据()

    async def _获取分平台基础数据(
        self,
        用户id: int,
        时间范围: str,
        开始时间: datetime,
        结束时间: datetime,
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None
    ) -> Dict[str, Any]:
        """获取分平台基础数据"""
        # 获取邀约业务数据（用于总邀约数指标）
        系统日志器.info(
            f"[获取分平台达人管理统计] 开始获取邀约业务数据 - 用户id: {用户id}, 时间范围: {时间范围}"
        )
        邀约数据 = await self.获取邀约业务数据(用户id, 时间范围, 开始日期, 结束日期)
        系统日志器.info(
            f"[获取分平台达人管理统计] 邀约业务数据获取完成: {邀约数据}"
        )

        # 获取真实的分平台达人统计数据
        import asyncio

        微信统计, 抖音统计 = await asyncio.gather(
            self._获取用户平台达人统计(用户id, "微信", 开始时间, 结束时间),
            self._获取用户平台达人统计(用户id, "抖音", 开始时间, 结束时间),
        )

        # 获取新增的联系方式和好友统计数据
        联系方式统计, 好友统计 = await asyncio.gather(
            self._获取联系方式统计数据(用户id, 时间范围, 开始时间, 结束时间),
            self._获取好友统计数据(用户id, 时间范围, 开始时间, 结束时间),
        )

        return {
            "邀约数据": 邀约数据,
            "微信统计": 微信统计,
            "抖音统计": 抖音统计,
            "联系方式统计": 联系方式统计,
            "好友统计": 好友统计,
        }

    async def _构建分平台统计结果(self, 基础数据: Dict[str, Any], 时间范围: str) -> Dict[str, Any]:
        """构建分平台统计结果数据"""
        邀约数据 = 基础数据["邀约数据"]
        微信统计 = 基础数据["微信统计"]
        抖音统计 = 基础数据["抖音统计"]
        联系方式统计 = 基础数据["联系方式统计"]
        好友统计 = 基础数据["好友统计"]

        # 计算汇总数据
        汇总数据 = self._计算分平台汇总数据(微信统计, 抖音统计, 联系方式统计, 好友统计)

        # 构建指标卡片
        指标卡片 = self._构建分平台指标卡片(邀约数据, 微信统计, 抖音统计, 联系方式统计, 好友统计, 时间范围)

        结果数据 = {
            "微信平台": {
                "认领达人数": 微信统计.get("metrics", {}).get("total", 0),
                "新增达人数": 微信统计.get("metrics", {}).get("new7d", 0),
                "有联系方式达人数": 微信统计.get("metrics", {}).get("withContact", 0),
                "活跃达人数": 微信统计.get("metrics", {}).get("active7d", 0),
                "好友转化数": 好友统计.get("微信", 0),
            },
            "抖音平台": {
                "认领达人数": 抖音统计.get("metrics", {}).get("total", 0),
                "新增达人数": 抖音统计.get("metrics", {}).get("new7d", 0),
                "有联系方式达人数": 抖音统计.get("metrics", {}).get("withContact", 0),
                "活跃达人数": 抖音统计.get("metrics", {}).get("active7d", 0),
                "好友转化数": 好友统计.get("抖音", 0),
            },
            "汇总数据": 汇总数据,
            "指标卡片": 指标卡片,
        }

        # 记录最终构建的指标卡片数量和第一个指标
        系统日志器.info(f"[获取分平台达人管理统计] 最终指标卡片数量: {len(指标卡片)}")
        if 指标卡片:
            第一个指标 = 指标卡片[0]
            系统日志器.info(
                f"[获取分平台达人管理统计] 第一个指标: {第一个指标.get('标题')} - 数值: {第一个指标.get('数值')}"
            )

        系统日志器.info(
            f"[获取分平台达人管理统计] 返回结果包含指标卡片: {len(结果数据.get('指标卡片', []))}"
        )

        return 结果数据

    def _计算分平台汇总数据(self, 微信统计: Dict, 抖音统计: Dict, 联系方式统计: Dict, 好友统计: Dict) -> Dict[str, Any]:
        """计算分平台汇总数据"""
        # 标记未使用的参数
        _ = 联系方式统计, 好友统计

        总认领达人数 = 微信统计.get("metrics", {}).get("total", 0) + 抖音统计.get("metrics", {}).get("total", 0)
        总新增达人数 = 微信统计.get("metrics", {}).get("timeRangeNew", 0) + 抖音统计.get("metrics", {}).get("timeRangeNew", 0)
        总联系方式达人数 = 微信统计.get("metrics", {}).get("withContact", 0) + 抖音统计.get("metrics", {}).get("withContact", 0)
        总好友达人数 = 好友统计.get("微信", 0) + 好友统计.get("抖音", 0)

        return {
            "总认领达人数": 总认领达人数,
            "总新增达人数": 总新增达人数,
            "总联系方式达人数": 总联系方式达人数,
            "总好友达人数": 总好友达人数,
            "微信占比": round(微信统计.get("metrics", {}).get("total", 0) / 总认领达人数 * 100, 1) if 总认领达人数 > 0 else 0,
            "抖音占比": round(抖音统计.get("metrics", {}).get("total", 0) / 总认领达人数 * 100, 1) if 总认领达人数 > 0 else 0,
            "联系方式获取率": round(总联系方式达人数 / 总认领达人数 * 100, 1) if 总认领达人数 > 0 else 0,
            "好友转化率": round(总好友达人数 / 总联系方式达人数 * 100, 1) if 总联系方式达人数 > 0 else 0,
        }

    def _构建分平台指标卡片(
        self,
        邀约数据: Dict,
        微信统计: Dict,
        抖音统计: Dict,
        联系方式统计: Dict,
        好友统计: Dict,
        时间范围: str
    ) -> List[Dict[str, Any]]:
        """构建分平台指标卡片"""
        总邀约数 = 邀约数据.get("邀约总数", 0)
        今日邀约数 = 邀约数据.get("今日邀约数", 0)
        系统日志器.info(f"[获取分平台达人管理统计] 构建总邀约数指标 - 总数: {总邀约数}, 今日: {今日邀约数}")

        # 获取时间范围对应的前缀
        时间前缀 = self._获取时间范围前缀(时间范围)

        总新增达人数 = 微信统计.get("metrics", {}).get("timeRangeNew", 0) + 抖音统计.get("metrics", {}).get("timeRangeNew", 0)

        return [
            {
                "标题": "总邀约数",
                "数值": 总邀约数,
                "格式化数值": f"{总邀约数:,}",
                "趋势": f"+{今日邀约数}",
                "趋势数值": 今日邀约数,
                "趋势类型": "up" if 今日邀约数 > 0 else "stable",
                "图标": "mail",
                "颜色": "#1890ff",
                "描述": "发起的达人邀约总数量",
            },
            {
                "标题": "总认领达人",
                "数值": 微信统计.get("metrics", {}).get("total", 0) + 抖音统计.get("metrics", {}).get("total", 0),
                "格式化数值": str(微信统计.get("metrics", {}).get("total", 0) + 抖音统计.get("metrics", {}).get("total", 0)),
                "趋势": f"+{总新增达人数}",
                "趋势数值": 总新增达人数,
                "趋势类型": "up" if 总新增达人数 > 0 else "stable",
                "图标": "team",
                "颜色": "#52c41a",
                "描述": "所有平台认领的达人总数",
            },
            {
                "标题": f"{时间前缀}微信认领达人",
                "数值": 微信统计.get("metrics", {}).get("timeRangeNew", 0),
                "格式化数值": str(微信统计.get("metrics", {}).get("timeRangeNew", 0)),
                "趋势": f"+{微信统计.get('metrics', {}).get('timeRangeNew', 0)}",
                "趋势数值": 微信统计.get("metrics", {}).get("timeRangeNew", 0),
                "趋势类型": "up" if 微信统计.get("metrics", {}).get("timeRangeNew", 0) > 0 else "stable",
                "图标": "wechat",
                "颜色": "#52c41a",
                "描述": f"微信平台{时间前缀}认领的达人数量",
            },
            {
                "标题": f"{时间前缀}抖音认领达人",
                "数值": 抖音统计.get("metrics", {}).get("timeRangeNew", 0),
                "格式化数值": str(抖音统计.get("metrics", {}).get("timeRangeNew", 0)),
                "趋势": f"+{抖音统计.get('metrics', {}).get('timeRangeNew', 0)}",
                "趋势数值": 抖音统计.get("metrics", {}).get("timeRangeNew", 0),
                "趋势类型": "up" if 抖音统计.get("metrics", {}).get("timeRangeNew", 0) > 0 else "stable",
                "图标": "video-camera",
                "颜色": "#fa8c16",
                "描述": f"抖音平台{时间前缀}认领的达人数量",
            },
            {
                "标题": f"{时间前缀}联系方式获取",
                "数值": 联系方式统计.get("微信", 0) + 联系方式统计.get("抖音", 0),
                "格式化数值": str(联系方式统计.get("微信", 0) + 联系方式统计.get("抖音", 0)),
                "趋势": f"+{联系方式统计.get('微信', 0) + 联系方式统计.get('抖音', 0)}",
                "趋势数值": 联系方式统计.get("微信", 0) + 联系方式统计.get("抖音", 0),
                "趋势类型": "up" if (联系方式统计.get("微信", 0) + 联系方式统计.get("抖音", 0)) > 0 else "stable",
                "图标": "contacts",
                "颜色": "#722ed1",
                "描述": f"{时间前缀}认领的达人中已获取商务微信联系方式的数量",
            },
            {
                "标题": f"{时间前缀}好友转化",
                "数值": 好友统计.get("微信", 0) + 好友统计.get("抖音", 0),
                "格式化数值": str(好友统计.get("微信", 0) + 好友统计.get("抖音", 0)),
                "趋势": f"+{好友统计.get('微信', 0) + 好友统计.get('抖音', 0)}",
                "趋势数值": 好友统计.get("微信", 0) + 好友统计.get("抖音", 0),
                "趋势类型": "up" if (好友统计.get("微信", 0) + 好友统计.get("抖音", 0)) > 0 else "stable",
                "图标": "user-add",
                "颜色": "#13c2c2",
                "描述": f"{时间前缀}认领的达人中已成功添加为微信好友的数量",
            },
        ]

    def _构建分平台默认数据(self) -> Dict[str, Any]:
        """构建分平台默认数据"""
        return {
            "微信平台": {
                "认领达人数": 0,
                "新增达人数": 0,
                "有联系方式达人数": 0,
                "活跃达人数": 0,
                "好友转化数": 0,
            },
            "抖音平台": {
                "认领达人数": 0,
                "新增达人数": 0,
                "有联系方式达人数": 0,
                "活跃达人数": 0,
                "好友转化数": 0,
            },
            "汇总数据": {
                "总认领达人数": 0,
                "总新增达人数": 0,
                "总联系方式达人数": 0,
                "总好友达人数": 0,
                "微信占比": 0,
                "抖音占比": 0,
                "联系方式获取率": 0,
                "好友转化率": 0,
            },
            "指标卡片": [
                {
                    "标题": "总邀约数",
                    "数值": 0,
                    "格式化数值": "0",
                    "趋势": "+0",
                    "趋势数值": 0,
                    "趋势类型": "stable",
                    "图标": "mail",
                    "颜色": "#1890ff",
                    "描述": "发起的达人邀约总数量",
                },
                {
                    "标题": "总认领达人",
                    "数值": 0,
                    "格式化数值": "0",
                    "趋势": "+0",
                    "趋势数值": 0,
                    "趋势类型": "stable",
                    "图标": "team",
                    "颜色": "#52c41a",
                    "描述": "所有平台认领的达人总数",
                },
            ],
        }

    async def _获取联系方式统计数据(
        self, 用户id: int, 时间范围: str, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, int]:
        """
        获取已获取联系方式的达人统计数据

        统计在用户达人补充信息表中有关联数据的达人数量，按达人id去重

        参数:
            用户id: 用户id
            时间范围: 统计时间范围（语义化参数）
            开始时间: 具体开始时间
            结束时间: 具体结束时间

        返回:
            包含微信和抖音平台联系方式统计的字典
        """
        try:
            # 调用数据访问层获取联系方式统计数据
            联系方式统计 = await self.数据访问层.获取_联系方式统计_数据(用户id, 开始时间, 结束时间)

            系统日志器.info(
                f"用户 {用户id} 时间范围({时间范围}) 联系方式统计 - 微信: {联系方式统计.get('微信', 0)}, 抖音: {联系方式统计.get('抖音', 0)}"
            )

            return 联系方式统计
        except Exception as e:
            系统日志器.error(f"获取联系方式统计数据失败: {str(e)}")
            return {"微信": 0, "抖音": 0}

    async def _获取好友统计数据(
        self, 用户id: int, 时间范围: str, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, int]:
        """
        获取已添加好友的达人统计数据

        参数:
            用户id: 用户id
            时间范围: 统计时间范围（语义化参数）
            开始时间: 具体开始时间
            结束时间: 具体结束时间

        返回:
            包含微信和抖音平台好友统计的字典
        """
        try:
            # 标记未使用的参数
            _ = 开始时间

            # 调用数据访问层获取好友统计数据
            好友统计 = await self.数据访问层.获取_好友统计_数据(用户id, 结束时间)

            系统日志器.info(
                f"用户 {用户id} 时间范围({时间范围}) 好友统计 - 微信: {好友统计.get('微信', 0)}, 抖音: {好友统计.get('抖音', 0)}"
            )

            return 好友统计

        except Exception as e:
            系统日志器.error(f"获取好友统计数据失败: {str(e)}")
            return {"微信": 0, "抖音": 0}

    async def 获取合作项目数据(
        self,
        用户id: int,
        时间范围: str = "30d",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取合作项目数据"""
        try:
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取合作项目数据
            项目数据 = await self.数据访问层.获取_合作项目_数据(用户id, 开始时间, 结束时间)

            合作项目数 = 项目数据.get("合作项目数", 0) or 0 if 项目数据 else 0
            意向合作数 = 项目数据.get("意向合作数", 0) or 0 if 项目数据 else 0
            样品已发数 = 项目数据.get("样品已发数", 0) or 0 if 项目数据 else 0
            已开播数 = 项目数据.get("已开播数", 0) or 0 if 项目数据 else 0
            总销售额 = float(项目数据.get("总销售额", 0) or 0) if 项目数据 else 0.0
            今日新增项目 = 项目数据.get("今日新增项目", 0) or 0 if 项目数据 else 0
            昨日新增项目 = 项目数据.get("昨日新增项目", 0) or 0 if 项目数据 else 0
            本周新增项目 = 项目数据.get("本周新增项目", 0) or 0 if 项目数据 else 0
            本月新增项目 = 项目数据.get("本月新增项目", 0) or 0 if 项目数据 else 0
            时间范围新增项目 = 项目数据.get("时间范围新增项目", 0) or 0 if 项目数据 else 0

            # 计算本月销售额（模拟数据）
            本月销售额 = 总销售额 * 0.3  # 假设本月占总销售额的30%

            # 根据时间范围确定当前新增项目数
            if 时间范围 == "今日":
                当前新增项目 = 今日新增项目
                项目标题 = "今日项目"
            elif 时间范围 == "昨日":
                当前新增项目 = 昨日新增项目
                项目标题 = "昨日项目"
            elif 时间范围 == "本周":
                当前新增项目 = 本周新增项目
                项目标题 = "本周项目"
            elif 时间范围 == "上周":
                当前新增项目 = 本周新增项目  # 使用本周数据作为示例
                项目标题 = "上周项目"
            elif 时间范围 == "本月":
                当前新增项目 = 本月新增项目
                项目标题 = "本月项目"
            elif 时间范围 == "上月":
                当前新增项目 = 本月新增项目  # 使用本月数据作为示例
                项目标题 = "上月项目"
            elif 时间范围 == "本季度":
                当前新增项目 = 本月新增项目  # 使用本月数据作为示例
                项目标题 = "本季度项目"
            elif 时间范围 == "上季度":
                当前新增项目 = 本月新增项目  # 使用本月数据作为示例
                项目标题 = "上季度项目"
            else:
                当前新增项目 = 时间范围新增项目
                项目标题 = "期间项目"

            return {
                "合作项目数": 合作项目数,
                        "意向合作数": 意向合作数,
                        "样品已发数": 样品已发数,
                        "已开播数": 已开播数,
                        "总销售额": 总销售额,
                        "今日新增项目": 今日新增项目,
                        "昨日新增项目": 昨日新增项目,
                        "本周新增项目": 本周新增项目,
                        "本月新增项目": 本月新增项目,
                        "时间范围新增项目": 时间范围新增项目,
                        "当前新增项目": 当前新增项目,
                        "项目标题": 项目标题,
                        "本月销售额": 本月销售额,
                        "指标卡片": [
                            {
                                "标题": "合作项目",
                                "数值": 合作项目数,
                                "格式化数值": str(合作项目数),
                                "趋势": f"+{当前新增项目}",
                                "趋势数值": 当前新增项目,
                                "趋势类型": "up" if 当前新增项目 > 0 else "stable",
                                "图标": "project",
                                "颜色": "#1890ff",
                            },
                            {
                                "标题": "销售额",
                                "数值": 总销售额,
                                "格式化数值": f"¥{总销售额:,.2f}",
                                "趋势": f"+¥{本月销售额:,.2f}",
                                "趋势数值": 本月销售额,
                                "趋势类型": "up",
                                "图标": "dollar",
                                "颜色": "#52c41a",
                            },
                            {
                                "标题": "开播率",
                                "数值": round(已开播数 / 合作项目数 * 100, 1)
                                if 合作项目数 > 0
                                else 0,
                                "格式化数值": f"{round(已开播数 / 合作项目数 * 100, 1) if 合作项目数 > 0 else 0}%",
                                "趋势": "较上月",
                                "趋势类型": "up",
                                "图标": "play-circle",
                                "颜色": "#fa8c16",
                            },
                        ],
                    }

        except Exception as e:
            系统日志器.error(f"获取合作项目数据失败: {str(e)}")
            raise e

    async def 获取团队数据概览(
        self,
        用户id: int,
        时间范围: str = "30d",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取团队数据概览"""
        try:
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取团队数据概览
            团队数据 = await self.数据访问层.获取_团队数据_概览(用户id, 开始时间, 结束时间)

            # 获取用户的主要团队（最近加入的团队）
            主要团队数据 = await self.数据访问层.获取_主要团队_数据(用户id)

            # 移除无效的数据查询（合作项目和销售额在数据库中无对应数据源）
            # 只保留可从数据库真实获取的数据
            参与团队数 = 团队数据.get("参与团队数", 0) or 0 if 团队数据 else 0
            团队成员总数 = 团队数据.get("团队成员总数", 0) or 0 if 团队数据 else 0
            团队邀约总数 = 团队数据.get("团队邀约总数", 0) or 0 if 团队数据 else 0

            return {
                "参与团队数": 参与团队数,
                "团队成员总数": 团队成员总数,
                "团队邀约总数": 团队邀约总数,
                # 添加主要团队信息用于跳转
                "主要团队id": 主要团队数据.get("团队id")
                if 主要团队数据
                else None,
                "主要团队名称": 主要团队数据.get("团队名称")
                if 主要团队数据
                else None,
                        # 移除无效数据项：团队合作项目数、团队销售额、我的团队排名
                        "指标卡片": [
                            {
                                "标题": "参与团队",
                                "数值": 参与团队数,
                                "格式化数值": str(参与团队数),
                                "趋势": "个团队",
                                "图标": "team",
                                "颜色": "#1890ff",
                            },
                            {
                                "标题": "团队成员",
                                "数值": 团队成员总数,
                                "格式化数值": str(团队成员总数),
                                "趋势": "总成员",
                                "图标": "user",
                                "颜色": "#52c41a",
                            },
                            {
                                "标题": "团队邀约",
                                "数值": 团队邀约总数,
                                "格式化数值": str(团队邀约总数),
                                "趋势": "总邀约",
                                "图标": "fire",
                                "颜色": "#fa541c",
                            },
                        ],
                    }
        except Exception as e:
            系统日志器.error(f"获取团队数据概览失败: {str(e)}")
            raise e

    async def 获取趋势数据(
        self,
        用户id: int,
        数据类型: str = "invitation",
        时间范围: str = "30d",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取趋势数据"""
        try:
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 调用数据访问层获取趋势数据
            趋势数据 = await self.数据访问层.获取_趋势_数据(用户id, 数据类型, 开始时间, 结束时间)

            if 数据类型 == "invitation":
                # 如果没有数据，生成默认的空数据结构
                if not 趋势数据:
                    # 生成时间范围内的日期列表，填充0值
                    from datetime import timedelta

                    当前日期 = 开始时间
                    数据点列表 = []
                    while 当前日期 <= 结束时间:
                        数据点列表.append(
                            {
                                "日期": str(当前日期.date()),
                                "数值": 0.0,
                                "标签": "0次邀约",
                            }
                        )
                        当前日期 += timedelta(days=1)
                else:
                    数据点列表 = [
                        {
                            "日期": str(row["日期"]),
                            "数值": float(row["数值"]),
                            "标签": f"{row['数值']}次邀约",
                        }
                        for row in 趋势数据
                    ]

                总计 = sum(point["数值"] for point in 数据点列表)
                平均值 = round(总计 / len(数据点列表), 1) if 数据点列表 else 0

                系统日志器.info(
                    f"邀约趋势数据查询完成 - 用户id: {用户id}, 数据点数量: {len(数据点列表)}, 总计: {总计}"
                )

                return {
                    "邀约趋势": {
                        "标题": "邀约趋势",
                        "类型": "line",
                        "数据": 数据点列表,
                        "总计": 总计,
                        "平均值": 平均值,
                        "最大值": max(
                            (point["数值"] for point in 数据点列表), default=0
                        ),
                        "最小值": min(
                            (point["数值"] for point in 数据点列表), default=0
                        ),
                    }
                }

            elif 数据类型 == "cooperation":
                # 如果没有数据，生成默认的空数据结构
                if not 趋势数据:
                    # 生成时间范围内的日期列表，填充0值
                    from datetime import timedelta

                    当前日期 = 开始时间
                    数据点列表 = []
                    while 当前日期 <= 结束时间:
                        数据点列表.append(
                            {
                                "日期": str(当前日期.date()),
                                "数值": 0.0,
                                "标签": "0次合作",
                            }
                        )
                        当前日期 += timedelta(days=1)
                else:
                    数据点列表 = [
                        {
                            "日期": str(row["日期"]),
                            "数值": float(row["数值"]),
                            "标签": f"{row['数值']}次合作",
                        }
                        for row in 趋势数据
                    ]

                总计 = sum(point["数值"] for point in 数据点列表)
                平均值 = round(总计 / len(数据点列表), 1) if 数据点列表 else 0

                系统日志器.info(
                    f"合作趋势数据查询完成 - 用户id: {用户id}, 数据点数量: {len(数据点列表)}, 总计: {总计}"
                )

                return {
                    "合作趋势": {
                        "标题": "合作趋势",
                        "类型": "bar",
                        "数据": 数据点列表,
                        "总计": 总计,
                        "平均值": 平均值,
                        "最大值": max(
                            (point["数值"] for point in 数据点列表), default=0
                        ),
                        "最小值": min(
                            (point["数值"] for point in 数据点列表), default=0
                        ),
                    }
                }

            else:
                return {"错误": "不支持的数据类型"}

        except Exception as e:
            系统日志器.error(f"获取趋势数据失败: {str(e)}")
            return {"错误": f"获取趋势数据失败: {str(e)}"}

    async def 获取多指标趋势数据(
        self,
        用户id: int,
        业务模块: str = "wechat",
        指标列表: Optional[List[str]] = None,
        时间维度: str = "week",
        时间范围: str = "本周",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
    ) -> Dict[str, Any]:
        """获取多指标趋势数据（带缓存）"""
        try:
            if 指标列表 is None:
                指标列表 = ["wechat_accounts"]

            # 生成缓存键
            缓存键 = self._生成缓存键(
                "multi_metric_trends",
                用户id=用户id,
                业务模块=业务模块,
                指标列表=指标列表,
                时间维度=时间维度,
                时间范围=时间范围,
                开始日期=开始日期,
                结束日期=结束日期,
            )

            # 尝试从缓存获取数据
            缓存数据 = self._获取缓存(缓存键)
            if 缓存数据:
                系统日志器.debug(f"从缓存获取多指标趋势数据: {缓存键}")
                return 缓存数据

            # 缓存未命中，查询数据库
            开始时间, 结束时间 = self._解析时间范围(时间范围, 开始日期, 结束日期)

            # 根据时间维度确定数据间隔
            数据间隔 = self._获取数据间隔(时间维度)

            # 生成日期列表
            日期列表 = self._生成日期列表(开始时间, 结束时间, 数据间隔)

            # 获取指标配置
            指标配置 = self._获取指标配置(业务模块)

            # 获取各指标的数据（修复指标键名映射问题）
            指标系列 = []
            for 指标键 in 指标列表:
                # 映射前端生成的指标键名到后端标准键名
                标准指标键 = self._映射指标键名(业务模块, 指标键)
                指标配置项 = next(
                    (config for config in 指标配置 if config["key"] == 标准指标键), None
                )
                if 指标配置项:
                    指标数据 = await self._获取指标数据(
                        用户id, 业务模块, 标准指标键, 日期列表
                    )
                    指标系列.append(
                        {
                            "name": 指标配置项["label"],
                            "key": 标准指标键,
                            "data": 指标数据,
                            "color": 指标配置项["color"],
                        }
                    )

            # 计算统计信息
            统计信息 = self._计算多指标统计信息(指标系列)

            结果数据 = {
                "业务模块": 业务模块,
                "图表数据": {
                    "标题": f"{self._获取业务模块名称(业务模块)}趋势分析",
                    "时间维度": 时间维度,
                    "数据间隔": f"{数据间隔}天",
                    "日期列表": [date.strftime("%m-%d") for date in 日期列表],
                    "指标系列": 指标系列,
                    "统计信息": 统计信息,
                },
                "指标配置": 指标配置,
            }

            # 将结果存入缓存
            self._设置缓存(缓存键, 结果数据)
            系统日志器.debug(f"多指标趋势数据已缓存: {缓存键}")

            # 定期清理过期缓存
            if len(self._缓存) % 100 == 0:  # 每100次请求清理一次
                self._清理过期缓存()

            return 结果数据

        except Exception as e:
            系统日志器.error(f"获取多指标趋势数据失败: {str(e)}")
            raise e

    def _获取数据间隔(self, 时间维度: str) -> int:
        """根据时间维度获取数据间隔（天数）- 移除日维度支持"""
        间隔映射 = {
            "week": 1,  # 周维度：1天间隔
            "month": 3,  # 月维度：3天间隔
            "quarter": 7,  # 季度维度：7天间隔
        }
        # 默认使用周维度，不再支持日维度
        return 间隔映射.get(时间维度, 1)

    def _生成日期列表(
        self, 开始时间: datetime, 结束时间: datetime, 间隔天数: int
    ) -> List[date]:
        """生成日期列表"""
        日期列表 = []
        当前日期 = 开始时间.date()
        结束日期 = 结束时间.date()

        while 当前日期 <= 结束日期:
            日期列表.append(当前日期)
            当前日期 += timedelta(days=间隔天数)

        return 日期列表

    def _获取指标配置(self, 业务模块: str) -> List[Dict[str, Any]]:
        """获取业务模块的指标配置 - 优化版本，支持时间依赖性标识"""
        配置映射 = {
            "wechat": [
                {
                    "key": "wechat_accounts",
                    "label": "微信账号数",
                    "color": "#1890ff",
                    "time_dependent": False,  # 不随时间变化
                    "description": "绑定的有效微信账号总数",
                },
                {
                    "key": "total_friends",
                    "label": "好友总数",
                    "color": "#52c41a",
                    "time_dependent": False,  # 不随时间变化
                    "description": "所有微信账号的好友总数量",
                },
                {
                    "key": "daily_new",
                    "label": "新增好友",
                    "color": "#fa8c16",
                    "time_dependent": True,  # 随时间变化
                    "description": "对应时间范围的新增好友数",
                },
                {
                    "key": "friend_requests",
                    "label": "发送请求",
                    "color": "#722ed1",
                    "time_dependent": True,  # 随时间变化
                    "description": "发送的好友请求数量",
                },
                {
                    "key": "communication_friends",
                    "label": "沟通好友",
                    "color": "#eb2f96",
                    "time_dependent": True,  # 随时间变化
                    "description": "有沟通交流的好友数量",
                },
                {
                    "key": "interaction_friends",
                    "label": "互动好友",
                    "color": "#f5222d",
                    "time_dependent": True,  # 随时间变化
                    "description": "双方都有消息记录的好友数量",
                },
            ],
            "invitation": [
                {
                    "key": "invitation_count",
                    "label": "每日新增邀约",
                    "color": "#52c41a",
                    "time_dependent": True,  # 随时间变化
                    "description": "每日新增的邀约数量",
                },
                {
                    "key": "success_rate",
                    "label": "每日成功邀约",
                    "color": "#1890ff",
                    "time_dependent": True,  # 随时间变化
                    "description": "每日成功的邀约数量",
                },
            ],
            "talent": [
                {
                    "key": "invitation_count",
                    "label": "总邀约数",
                    "color": "#1890ff",
                    "time_dependent": True,  # 随时间变化
                    "description": "发起的达人邀约总数量",
                },
                {
                    "key": "total_talent_count",
                    "label": "总认领达人",
                    "color": "#52c41a",
                    "time_dependent": False,  # 不随时间变化
                    "description": "所有平台认领的达人总数",
                },
                {
                    "key": "wechat_talent_count",
                    "label": "微信认领达人",
                    "color": "#52c41a",
                    "time_dependent": True,  # 随时间变化
                    "description": "微信平台认领的达人数量",
                },
                {
                    "key": "douyin_talent_count",
                    "label": "抖音认领达人",
                    "color": "#fa8c16",
                    "time_dependent": True,  # 随时间变化
                    "description": "抖音平台认领的达人数量",
                },
                {
                    "key": "contact_acquired",
                    "label": "联系方式获取",
                    "color": "#722ed1",
                    "time_dependent": True,  # 随时间变化
                    "description": "已获取商务微信联系方式的数量",
                },
                {
                    "key": "friend_conversion",
                    "label": "好友转化",
                    "color": "#13c2c2",
                    "time_dependent": True,  # 随时间变化
                    "description": "已成功添加为微信好友的数量",
                },
            ],
            "sample": [
                {
                    "key": "approval_count",
                    "label": "申请通过数量",
                    "color": "#52c41a",
                    "time_dependent": True,  # 随时间变化
                    "description": "已通过审核的样品申请",
                },
                {
                    "key": "actual_sample_count",
                    "label": "实际寄样数量",
                    "color": "#1890ff",
                    "time_dependent": True,  # 随时间变化
                    "description": "已发货的样品数量",
                },
                {
                    "key": "delivered_count",
                    "label": "样品送达数量",
                    "color": "#fa8c16",
                    "time_dependent": True,  # 随时间变化
                    "description": "用户已签收的样品",
                },
            ],
        }
        return 配置映射.get(业务模块, [])

    def _获取业务模块名称(self, 业务模块: str) -> str:
        """获取业务模块的中文名称"""
        名称映射 = {
            "wechat": "微信运营核心指标",
            "invitation": "邀约业务",
            "talent": "达人管理",
            "sample": "寄样模块",
        }
        return 名称映射.get(业务模块, "业务")

    def _映射指标键名(self, 业务模块: str, 前端指标键: str) -> str:
        """映射前端生成的指标键名到后端标准键名 - 复用核心业务指标的标准键名"""
        系统日志器.info(
            f"🔍 _映射指标键名: 业务模块={业务模块}, 前端指标键={前端指标键}"
        )

        # 如果已经是标准键名，直接返回
        标准键名列表 = {
            "wechat": [
                "wechat_accounts",
                "total_friends",
                "daily_new",
                "friend_requests",
                "stored_friends",
                "communication_friends",
                "interaction_friends",
            ],
            "invitation": ["invitation_count", "success_rate"],
            "talent": [
                "invitation_count",
                "cooperation_count",
                "connection_count",
                "cooperation_active",
                "wechat_talent_count",
                "douyin_talent_count",
                "contact_acquired",
                "friend_conversion",
                "total_talent_count",
            ],
            "sample": ["approval_count", "actual_sample_count", "delivered_count"],
        }

        if 前端指标键 in 标准键名列表.get(业务模块, []):
            系统日志器.info(f"✅ 直接匹配标准键名: {前端指标键}")
            return 前端指标键

        # 映射前端生成的键名（如 talent_metric_0, talent_metric_1）到标准键名
        # 同时支持中文指标标题的直接映射
        键名映射 = {
            "wechat": {
                "wechat_metric_0": "wechat_accounts",
                "wechat_metric_1": "total_friends",
                "wechat_metric_2": "daily_new",
                "wechat_metric_3": "friend_requests",
                "wechat_metric_4": "stored_friends",
                "wechat_metric_5": "communication_friends",
                "wechat_metric_6": "interaction_friends",
                # 支持中文指标标题的直接映射
                "微信账号": "wechat_accounts",
                "微信账号数量": "wechat_accounts",
                "好友总数": "total_friends",
                "今日新增": "daily_new",
                "昨日新增": "daily_new",
                "本周新增": "daily_new",
                "上周新增": "daily_new",
                "本月新增": "daily_new",
                "上月新增": "daily_new",
                "本季度新增": "daily_new",
                "上季度新增": "daily_new",
                "期间新增": "daily_new",
                "新增好友": "daily_new",
                "今日新增好友": "daily_new",
                "发送请求": "friend_requests",
                "发送好友请求数": "friend_requests",
                "入库好友": "stored_friends",
                "入库好友数": "stored_friends",
                "沟通好友": "communication_friends",
                "沟通好友数": "communication_friends",
                "互动好友": "interaction_friends",
                "互动好友数": "interaction_friends",
            },
            "invitation": {
                "invitation_metric_0": "invitation_count",
                "invitation_metric_1": "success_rate",
            },
            "talent": {
                "talent_metric_0": "invitation_count",
                "talent_metric_1": "total_talent_count",
                "talent_metric_2": "wechat_talent_count",
                "talent_metric_3": "douyin_talent_count",
                "talent_metric_4": "contact_acquired",
                "talent_metric_5": "friend_conversion",
                # 保持向后兼容的映射
                "talent_count": "total_talent_count",
                "cooperation_count": "invitation_count",
                "connection_count": "contact_acquired",
                "cooperation_active": "invitation_count",
                # 支持中文指标标题的直接映射
                "总邀约数": "invitation_count",
                "邀约总数": "invitation_count",
                "邀约数量": "invitation_count",
                "总认领达人": "total_talent_count",
                "总达人数": "total_talent_count",
                "认领达人数": "total_talent_count",
                "微信认领达人": "wechat_talent_count",
                "微信达人数": "wechat_talent_count",
                "抖音认领达人": "douyin_talent_count",
                "抖音达人数": "douyin_talent_count",
                "联系方式获取": "contact_acquired",
                "联系方式数": "contact_acquired",
                "好友转化": "friend_conversion",
                "好友转化数": "friend_conversion",
                # 兼容旧的指标名称
                "意向合作": "invitation_count",
                "已建联": "contact_acquired",
                "合作中": "invitation_count",
                "达人数量": "total_talent_count",
                "合作数量": "invitation_count",
            },
            "sample": {
                "sample_metric_0": "approval_count",
                "sample_metric_1": "actual_sample_count",
                "sample_metric_2": "delivered_count",
                # 支持中文指标标题的直接映射
                "审批数量": "approval_count",
                "申请通过数量": "approval_count",
                "实际寄样数": "actual_sample_count",
                "实际寄样数量": "actual_sample_count",
                "已寄出数": "delivered_count",
                "样品送达数量": "delivered_count",
                "寄样数量": "sample_count",
                "审核通过率": "approval_rate",
                "成功率": "success_rate",
            },
        }

        模块映射 = 键名映射.get(业务模块, {})
        映射结果 = 模块映射.get(前端指标键, 前端指标键)

        if 映射结果 != 前端指标键:
            系统日志器.info(f"✅ 指标键名映射成功: {前端指标键} -> {映射结果}")
        else:
            系统日志器.warning(f"⚠️ 未找到映射，使用原始键名: {前端指标键}")

        return 映射结果

    async def _获取指标数据(
        self, 用户id: int, 业务模块: str, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取指定指标的数据 - 优化版本，支持时间依赖性判断"""
        try:
            系统日志器.info(
                f"🔍 获取指标数据: 用户id={用户id}, 业务模块={业务模块}, 指标键={指标键}, 日期数量={len(日期列表)}"
            )

            # 获取指标配置，检查是否随时间变化
            指标配置列表 = self._获取指标配置(业务模块)
            指标配置 = next(
                (config for config in 指标配置列表 if config["key"] == 指标键), None
            )

            if not 指标配置:
                系统日志器.warning(f"⚠️ 未找到指标配置: {业务模块}.{指标键}")
                return [0.0 for _ in 日期列表]

            # 检查指标是否随时间变化
            时间依赖 = 指标配置.get("time_dependent", True)

            if not 时间依赖:
                # 不随时间变化的指标，只查询一次然后复制到所有日期
                系统日志器.info(f"📊 指标 {指标键} 不随时间变化，执行单次查询优化")
                单次数值 = await self._获取单次指标数据(用户id, 业务模块, 指标键)
                结果 = [单次数值 for _ in 日期列表]
                系统日志器.info(
                    f"✅ 单次查询优化完成: {业务模块}.{指标键} = {单次数值} (复制到{len(日期列表)}个日期)"
                )
                return 结果
            else:
                # 随时间变化的指标，按原有逻辑逐日查询
                系统日志器.info(f"📊 指标 {指标键} 随时间变化，执行逐日查询")

                # 优先复用核心业务指标的查询逻辑
                if 业务模块 == "wechat":
                    结果 = await self._获取微信指标数据_复用核心指标(
                        用户id, 指标键, 日期列表
                    )
                elif 业务模块 == "talent":
                    结果 = await self._获取达人指标数据_复用核心指标(
                        用户id, 指标键, 日期列表
                    )
                elif 业务模块 == "invitation":
                    结果 = await self._获取邀约指标数据_复用核心指标(
                        用户id, 指标键, 日期列表
                    )
                elif 业务模块 == "sample":
                    结果 = await self._获取寄样指标数据_复用核心指标(
                        用户id, 指标键, 日期列表
                    )
                else:
                    系统日志器.warning(f"⚠️ 不支持的业务模块: {业务模块}")
                    结果 = [0.0 for _ in 日期列表]

            系统日志器.info(f"✅ 指标数据获取成功: {业务模块}.{指标键} = {结果}")
            return 结果

        except Exception as e:
            系统日志器.error(
                f"❌ 获取指标数据失败: {业务模块}.{指标键}, 错误: {str(e)}"
            )
            import traceback

            系统日志器.error(f"错误详情: {traceback.format_exc()}")
            return [0.0 for _ in 日期列表]

    async def _获取单次指标数据(self, 用户id: int, 业务模块: str, 指标键: str) -> float:
        """获取单次指标数据 - 用于不随时间变化的指标"""
        try:
            系统日志器.info(f"🔍 获取单次指标数据: {业务模块}.{指标键}")

            if 业务模块 == "wechat":
                if 指标键 == "wechat_accounts":
                    # 获取微信账号数 - 不随时间变化
                    核心指标数据 = await self.获取微信运营核心指标(
                        用户id=用户id, 时间范围="今日"
                    )
                    return float(核心指标数据.get("微信账号数量", 0))
                elif 指标键 == "total_friends":
                    # 获取好友总数 - 不随时间变化
                    核心指标数据 = await self.获取微信运营核心指标(
                        用户id=用户id, 时间范围="今日"
                    )
                    return float(核心指标数据.get("好友总数", 0))

            elif 业务模块 == "talent":
                if 指标键 == "total_talent_count":
                    # 获取总认领达人数 - 不随时间变化
                    达人数据 = await self.获取分平台达人管理统计(
                        用户id=用户id, 时间范围="今日"
                    )
                    汇总数据 = 达人数据.get("汇总数据", {})
                    return float(汇总数据.get("总认领达人数", 0))

            # 其他业务模块暂时没有不随时间变化的指标
            系统日志器.warning(f"⚠️ 未实现的单次指标查询: {业务模块}.{指标键}")
            return 0.0

        except Exception as e:
            系统日志器.error(
                f"❌ 获取单次指标数据失败: {业务模块}.{指标键}, 错误: {str(e)}"
            )
            return 0.0

    async def _获取微信指标数据_复用核心指标(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取微信指标数据 - 复用核心业务指标的查询逻辑"""
        数据列表 = []

        系统日志器.info(
            f"🔍 _获取微信指标数据_复用核心指标: 用户id={用户id}, 指标键={指标键}"
        )

        # 为每个日期获取对应的微信运营核心指标数据
        for 日期 in 日期列表:
            try:
                # 构造对应日期的时间范围
                时间范围 = "自定义"
                开始日期 = 日期
                结束日期 = 日期

                # 调用核心业务指标方法获取数据
                核心指标数据 = await self.获取微信运营核心指标(
                    用户id=用户id,
                    时间范围=时间范围,
                    开始日期=开始日期,
                    结束日期=结束日期,
                )

                # 根据指标键提取对应的数值
                if 指标键 == "wechat_accounts":
                    数值 = 核心指标数据.get("微信账号数量", 0)
                elif 指标键 == "total_friends":
                    数值 = 核心指标数据.get("好友总数", 0)
                elif 指标键 == "daily_new":
                    数值 = 核心指标数据.get("时间范围新增", 0)
                elif 指标键 == "friend_requests":
                    数值 = 核心指标数据.get("发送好友请求数", 0)
                elif 指标键 == "stored_friends":
                    # 入库好友数暂时使用时间范围新增
                    数值 = 核心指标数据.get("时间范围新增", 0)
                elif 指标键 == "communication_friends":
                    数值 = 核心指标数据.get("沟通好友数", 0)
                elif 指标键 == "interaction_friends":
                    数值 = 核心指标数据.get("互动好友数", 0)
                else:
                    数值 = 0

                数据列表.append(float(数值))
                系统日志器.info(f"✅ 日期 {日期} 的 {指标键} 数据: {数值}")

            except Exception as e:
                系统日志器.error(f"❌ 获取日期 {日期} 的微信指标数据失败: {str(e)}")
                数据列表.append(0.0)

        return 数据列表

    async def _获取达人指标数据_复用核心指标(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取达人指标数据 - 复用核心业务指标的查询逻辑"""
        数据列表 = []

        系统日志器.info(
            f"🔍 _获取达人指标数据_复用核心指标: 用户id={用户id}, 指标键={指标键}"
        )

        # 为每个日期获取对应的达人管理指标数据
        for 日期 in 日期列表:
            try:
                # 构造对应日期的时间范围
                时间范围 = "自定义"
                开始日期 = 日期
                结束日期 = 日期

                # 根据指标类型选择合适的数据源
                if 指标键 == "invitation_count":
                    # 邀约数据 - 调用邀约业务数据方法
                    邀约数据 = await self.获取邀约业务数据(
                        用户id=用户id,
                        时间范围=时间范围,
                        开始日期=开始日期,
                        结束日期=结束日期,
                    )
                    数值 = 邀约数据.get("时间范围邀约数", 0)

                elif 指标键 in [
                    "wechat_talent_count",
                    "douyin_talent_count",
                    "contact_acquired",
                    "friend_conversion",
                ]:
                    # 达人管理数据 - 调用分平台达人管理统计
                    达人数据 = await self.获取分平台达人管理统计(
                        用户id=用户id,
                        时间范围=时间范围,
                        开始日期=开始日期,
                        结束日期=结束日期,
                    )

                    if 指标键 == "wechat_talent_count":
                        # 微信认领达人数量
                        微信平台 = 达人数据.get("微信平台", {})
                        数值 = 微信平台.get("新增达人数", 0)
                    elif 指标键 == "douyin_talent_count":
                        # 抖音认领达人数量
                        抖音平台 = 达人数据.get("抖音平台", {})
                        数值 = 抖音平台.get("新增达人数", 0)
                    elif 指标键 == "contact_acquired":
                        # 联系方式获取数量
                        汇总数据 = 达人数据.get("汇总数据", {})
                        数值 = 汇总数据.get("总联系方式达人数", 0)
                    elif 指标键 == "friend_conversion":
                        # 好友转化数量
                        汇总数据 = 达人数据.get("汇总数据", {})
                        数值 = 汇总数据.get("总好友达人数", 0)
                    else:
                        数值 = 0

                else:
                    # 其他指标暂时返回0
                    数值 = 0

                数据列表.append(float(数值))
                系统日志器.info(f"✅ 日期 {日期} 的 {指标键} 数据: {数值}")

            except Exception as e:
                系统日志器.error(f"❌ 获取日期 {日期} 的达人指标数据失败: {str(e)}")
                数据列表.append(0.0)

        return 数据列表

    async def _获取邀约指标数据_复用核心指标(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取邀约指标数据 - 复用核心业务指标的查询逻辑"""
        # 邀约指标与达人指标复用同样的逻辑
        return await self._获取达人指标数据_复用核心指标(用户id, 指标键, 日期列表)

    async def _获取寄样指标数据_复用核心指标(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取寄样指标数据 - 复用核心业务指标的查询逻辑"""
        数据列表 = []

        系统日志器.info(
            f"🔍 _获取寄样指标数据_复用核心指标: 用户id={用户id}, 指标键={指标键}"
        )

        # 为每个日期获取对应的寄样统计指标数据
        for 日期 in 日期列表:
            try:
                # 构造对应日期的时间范围
                时间范围 = "自定义"
                开始日期 = 日期
                结束日期 = 日期

                # 直接调用寄样统计数据服务
                from 路由.仪表板路由 import 获取寄样统计数据_服务

                寄样数据 = await 获取寄样统计数据_服务(
                    用户id=用户id,
                    时间范围=时间范围,
                    开始日期=开始日期,
                    结束日期=结束日期,
                )

                # 根据指标键提取对应的数值
                if 指标键 == "approval_count":
                    数值 = 寄样数据.get("用户申请样品数量", 0)
                elif 指标键 == "actual_sample_count":
                    数值 = 寄样数据.get("用户实际寄样数量", 0)
                elif 指标键 == "delivered_count":
                    数值 = 寄样数据.get("用户样品送达数量", 0)
                else:
                    数值 = 0

                数据列表.append(float(数值))
                系统日志器.info(f"✅ 日期 {日期} 的 {指标键} 数据: {数值}")

            except Exception as e:
                系统日志器.error(f"❌ 获取日期 {日期} 的寄样指标数据失败: {str(e)}")
                数据列表.append(0.0)

        return 数据列表

    async def _获取微信指标数据(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取微信运营指标数据"""
        数据列表 = []

        # 调试信息
        系统日志器.info(f"🔍 _获取微信指标数据: 用户id={用户id}, 指标键={指标键}")
        系统日志器.info(f"🔍 日期列表: {日期列表}")

        # 检查数据库中的数据范围
        时间范围结果 = await self.数据访问层.获取_微信时间范围_数据(用户id)
        if 时间范围结果:
            系统日志器.info(
                f"🔍 用户 {用户id} 的微信绑定时间范围: {时间范围结果['最早绑定时间']} 到 {时间范围结果['最晚绑定时间']}"
            )
        else:
            系统日志器.info(f"🔍 用户 {用户id} 没有微信绑定数据")

        for 日期 in 日期列表:
            日期开始 = datetime.combine(日期, datetime.min.time())
            日期结束 = datetime.combine(日期, datetime.max.time())

            系统日志器.info(f"🔍 查询日期范围: {日期开始} 到 {日期结束}")

            # 调用数据访问层获取微信指标数据
            数量 = await self.数据访问层.获取_微信指标_数据(用户id, 指标键, 日期开始, 日期结束)

            系统日志器.info(f"🔍 查询结果: 日期={日期}, 数量={数量}")
            数据列表.append(float(数量))

        return 数据列表

    async def _获取邀约指标数据(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取邀约业务指标数据"""
        数据列表 = []

        for 日期 in 日期列表:
            日期开始 = datetime.combine(日期, datetime.min.time())
            日期结束 = datetime.combine(日期, datetime.max.time())

            # 调用数据访问层获取邀约指标数据
            数量 = await self.数据访问层.获取_邀约指标_数据(用户id, 指标键, 日期开始, 日期结束)
            数据列表.append(float(数量))

        return 数据列表

    async def _获取达人指标数据(
        self, 用户id: int, 指标键: str, 日期列表: List[date]
    ) -> List[float]:
        """获取达人管理指标数据 - 修复字段名和查询逻辑"""
        数据列表 = []

        for 日期 in 日期列表:
            日期开始 = datetime.combine(日期, datetime.min.time())
            日期结束 = datetime.combine(日期, datetime.max.time())

            # 调用数据访问层获取达人指标数据
            数量 = await self.数据访问层.获取_达人指标_数据(用户id, 指标键, 日期开始, 日期结束)
            数据列表.append(float(数量))

            # 添加详细日志记录
            系统日志器.debug(
                f"📊 达人指标数据: 日期={日期}, 指标={指标键}, 数量={数量}"
            )

        系统日志器.info(
            f"✅ 达人指标数据获取完成: 指标={指标键}, 数据点={len(数据列表)}, 总计={sum(数据列表)}"
        )
        return 数据列表



    def _计算多指标统计信息(self, 指标系列: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算多指标的统计信息"""
        if not 指标系列:
            return {"总计": 0, "平均值": 0, "最大值": 0, "最小值": 0}

        所有数值 = []
        for 系列 in 指标系列:
            所有数值.extend(系列["data"])

        if not 所有数值:
            return {"总计": 0, "平均值": 0, "最大值": 0, "最小值": 0}

        总计 = sum(所有数值)
        平均值 = round(总计 / len(所有数值), 1)
        最大值 = max(所有数值)
        最小值 = min(所有数值)

        return {"总计": 总计, "平均值": 平均值, "最大值": 最大值, "最小值": 最小值}

    async def 获取待办事项列表(
        self, 用户id: int, 分类: Optional[str] = None, 条数限制: int = 20
    ) -> List[Dict[str, Any]]:
        """获取待办事项列表"""
        try:
            # 分类参数暂时不使用，为后续扩展预留
            _ = 分类

            # 调用数据访问层获取待办事项数据
            待办事项数据 = await self.数据访问层.获取_待办事项_数据(用户id)

            # 转换为前端需要的格式
            待办事项列表 = []
            for 事项 in 待办事项数据:
                if 事项["类型"] == "样品发货":
                    待办事项列表.append({
                        "ID": f"sample_{事项['关联id']}",
                        "标题": 事项["标题"],
                        "描述": 事项["描述"],
                        "截止时间": "今天 18:00",
                        "优先级": 事项["优先级"].lower(),
                        "状态": "pending",
                        "标签": "样品发货",
                        "标签颜色": "red",
                        "关联数据": {
                            "类型": "sample_shipment",
                            "样品id": 事项["关联id"],
                        },
                    })
                elif 事项["类型"] == "邀约跟进":
                    待办事项列表.append({
                        "ID": f"invitation_{事项['关联id']}",
                        "标题": 事项["标题"],
                        "描述": 事项["描述"],
                        "截止时间": "明天 18:00",
                        "优先级": 事项["优先级"].lower(),
                        "状态": "pending",
                        "标签": "邀约跟进",
                        "标签颜色": "orange",
                        "关联数据": {
                            "类型": "invitation_follow",
                            "邀约id": 事项["关联id"],
                        },
                    })

            return 待办事项列表[:条数限制]

        except Exception as e:
            系统日志器.error(f"获取待办事项列表失败: {str(e)}")
            return []  # 返回空列表而不是抛出异常

    async def 批量更新邀约状态(
        self, 用户id: int, 邀约IDs: List[int], 新状态: str, 备注: str = ""
    ) -> Dict[str, Any]:
        """
        批量更新邀约状态

        参数:
            用户id: 用户id
            邀约IDs: 邀约记录id列表
            新状态: 新的邀约状态码
            备注: 更新备注

        返回:
            更新结果统计
        """
        try:
            # 标记未使用的参数
            _ = 备注

            # 调用数据访问层批量更新邀约状态
            更新结果 = await self.数据访问层.批量更新_邀约状态_数据(邀约IDs, 新状态, 用户id)

            有效IDs = 更新结果.get("有效IDs", [])
            更新数量 = 更新结果.get("成功数量", 0)

            # 为每个成功更新的邀约创建业务通知
            if 更新数量 > 0:
                try:
                    from 服务.异步用户通知服务 import 异步创建业务通知

                    # 获取状态显示名称
                    状态映射 = {
                        "已接受": "已接受",
                        "已拒绝": "已拒绝",
                        "待处理": "待处理",
                        "已完成": "已完成",
                    }
                    状态显示 = 状态映射.get(新状态, 新状态)

                    # 为每个更新的邀约创建通知
                    for 邀约ID in 有效IDs[:更新数量]:  # 只为成功更新的记录创建通知
                        await 异步创建业务通知(
                            用户id=用户id,
                            业务类型="invitation_status",
                            业务关联id=str(邀约ID),
                            模板变量={
                                "达人名称": "达人",  # 这里可以根据需要查询具体达人名称
                                "状态": 状态显示,
                            },
                        )

                    应用日志器.info(
                        f"为用户 {用户id} 的 {更新数量} 个邀约状态变更创建了业务通知"
                    )

                except Exception as 通知异常:
                    # 通知创建失败不影响主业务流程
                    应用日志器.warning(f"创建邀约状态变更通知失败: {通知异常}")

            return {
                "成功数量": 更新数量,
                "失败数量": len(邀约IDs) - 更新数量,
                "有效记录数": len(有效IDs),
                "总请求数": len(邀约IDs),
            }

        except Exception as e:
            应用日志器.error(f"批量更新邀约状态失败: {e}")
            raise e

    async def 删除邀约记录(
        self, 用户id: int, 邀约ID: int, 删除原因: str = ""
    ) -> Dict[str, Any]:
        """
        删除邀约记录

        参数:
            用户id: 用户id
            邀约ID: 邀约记录id
            删除原因: 删除原因

        返回:
            删除结果
        """
        try:
            # 调用数据访问层验证并删除邀约记录
            删除结果 = await self.数据访问层.验证并删除_邀约记录_数据(邀约ID, 用户id)

            if 删除结果["成功"]:
                应用日志器.info(
                    f"用户 {用户id} 删除邀约记录 {邀约ID}，达人: {删除结果['达人昵称']}，原因: {删除原因}"
                )
                return {"成功": True, "删除的达人": 删除结果["达人昵称"]}
            else:
                return 删除结果

        except Exception as e:
            应用日志器.error(f"删除邀约记录失败: {e}")
            raise e

    async def 批量更新达人联系方式(
        self,
        用户id: int,
        达人关联ids: List[int],
        联系方式: str,
        联系方式类型: str = "微信",
        备注: str = "",
    ) -> Dict[str, Any]:
        """
        批量更新达人联系方式

        参数:
            用户id: 用户id
            达人关联ids: 达人关联记录id列表
            联系方式: 新的联系方式
            联系方式类型: 联系方式类型
            备注: 更新备注

        返回:
            更新结果统计
        """
        try:
            # 调用数据访问层批量更新达人联系方式
            更新结果 = await self.数据访问层.批量更新_达人联系方式_数据(
                达人关联ids, 联系方式, 联系方式类型, 备注, 用户id
            )

            应用日志器.info(
                f"用户 {用户id} 批量更新达人联系方式完成 - 成功: {更新结果.get('成功数量', 0)}, 失败: {更新结果.get('失败数量', 0)}"
            )

            return 更新结果

        except Exception as e:
            应用日志器.error(f"批量更新达人联系方式失败: {e}")
            raise e

    async def 解除达人关联(
        self, 用户id: int, 关联id: int, 解除原因: str = ""
    ) -> Dict[str, Any]:
        """
        解除达人关联

        参数:
            用户id: 用户id
            关联id: 达人关联记录id
            解除原因: 解除原因

        返回:
            解除结果
        """
        try:
            # 调用数据访问层解除达人关联
            解除结果 = await self.数据访问层.解除_达人关联_数据(关联id, 解除原因, 用户id)

            if 解除结果["成功"]:
                应用日志器.info(
                    f"用户 {用户id} 解除达人关联 {关联id}，平台: {解除结果['平台']}，原因: {解除原因}"
                )
                return {"成功": True, "平台": 解除结果["平台"]}
            else:
                return 解除结果

        except Exception as e:
            应用日志器.error(f"解除达人关联失败: {e}")
            raise e

    async def 获取工作台完整数据(
        self,
        用户id: int,
        时间范围: str = "30d",
        开始日期: Optional[date] = None,
        结束日期: Optional[date] = None,
        包含模块: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """获取工作台完整数据（聚合接口）"""
        try:
            if 包含模块 is None:
                包含模块 = ["personal", "team", "trends", "todos"]

            # 构建任务列表
            任务列表 = self._构建工作台任务列表(用户id, 时间范围, 开始日期, 结束日期, 包含模块)

            # 执行所有任务
            结果列表 = await asyncio.gather(*任务列表, return_exceptions=True)

            # 组装结果数据
            完整数据 = self._组装工作台结果数据(结果列表, 包含模块)

            # 添加快捷操作和更新时间
            完整数据.update(self._获取工作台快捷操作())
            完整数据["更新时间"] = datetime.now()

            return 完整数据

        except Exception as e:
            系统日志器.error(f"获取工作台完整数据失败: {str(e)}")
            raise e

    def _构建工作台任务列表(
        self,
        用户id: int,
        时间范围: str,
        开始日期: Optional[date],
        结束日期: Optional[date],
        包含模块: List[str]
    ) -> List:
        """构建工作台任务列表"""
        任务列表 = []

        if "personal" in 包含模块:
            任务列表.extend([
                self.获取个人微信运营数据(用户id, 时间范围, 开始日期, 结束日期),
                self.获取邀约业务数据(用户id, 时间范围, 开始日期, 结束日期),
                self.获取达人管理数据(用户id, 时间范围, 开始日期, 结束日期),
                self.获取合作项目数据(用户id, 时间范围, 开始日期, 结束日期),
            ])

        if "team" in 包含模块:
            任务列表.append(
                self.获取团队数据概览(用户id, 时间范围, 开始日期, 结束日期)
            )

        if "trends" in 包含模块:
            任务列表.extend([
                self.获取趋势数据(用户id, "invitation", 时间范围, 开始日期, 结束日期),
                self.获取趋势数据(用户id, "cooperation", 时间范围, 开始日期, 结束日期),
            ])

        if "todos" in 包含模块:
            任务列表.append(self.获取待办事项列表(用户id))

        return 任务列表

    def _组装工作台结果数据(self, 结果列表: List, 包含模块: List[str]) -> Dict[str, Any]:
        """组装工作台结果数据"""
        完整数据 = {}
        结果索引 = 0

        if "personal" in 包含模块:
            完整数据["微信运营"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1
            完整数据["邀约业务"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1
            完整数据["达人管理"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1
            完整数据["合作项目"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1

        if "team" in 包含模块:
            完整数据["团队数据"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1

        if "trends" in 包含模块:
            邀约趋势数据 = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1
            合作趋势数据 = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else {}
            结果索引 += 1

            # 组织趋势数据结构，确保前端能正确识别
            完整数据["邀约趋势"] = 邀约趋势数据
            完整数据["合作趋势"] = 合作趋势数据

            # 添加趋势数据字段，供前端业务趋势分析模块使用
            完整数据["趋势数据"] = {
                "邀约趋势": 邀约趋势数据.get("邀约趋势") if isinstance(邀约趋势数据, dict) and 邀约趋势数据 else None,
                "合作趋势": 合作趋势数据.get("合作趋势") if isinstance(合作趋势数据, dict) and 合作趋势数据 else None,
            }

            系统日志器.info(f"趋势数据组装完成 - 邀约趋势: {bool(邀约趋势数据)}, 合作趋势: {bool(合作趋势数据)}")

        if "todos" in 包含模块:
            完整数据["待办事项"] = 结果列表[结果索引] if not isinstance(结果列表[结果索引], Exception) else []
            结果索引 += 1

        return 完整数据

    def _获取工作台快捷操作(self) -> Dict[str, List[Dict[str, str]]]:
        """获取工作台快捷操作"""
        return {
            "快捷操作": [
                {
                    "操作ID": "new_invitation",
                    "标题": "发起邀约",
                    "描述": "邀请新的达人合作",
                    "图标": "plus",
                    "链接": "/invitation/create",
                    "颜色": "#1890ff",
                },
                {
                    "操作ID": "manage_talents",
                    "标题": "管理达人",
                    "描述": "查看和管理认领的达人",
                    "图标": "team",
                    "链接": "/talents/manage",
                    "颜色": "#52c41a",
                },
                {
                    "操作ID": "view_reports",
                    "标题": "查看报表",
                    "描述": "查看详细的业务报表",
                    "图标": "bar-chart",
                    "链接": "/reports",
                    "颜色": "#fa8c16",
                },
            ]
        }

    async def _获取用户平台达人统计(
        self, 用户id: int, 平台: str, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, Any]:
        """获取用户指定平台的达人统计数据"""
        try:
            # 获取基础统计数据
            基础数据 = await self._获取平台达人基础统计(用户id, 平台, 开始时间, 结束时间)
            if not 基础数据:
                return self._构建默认达人统计数据()

            # 获取联系方式统计
            有联系方式数 = await self._获取平台达人联系方式统计(用户id, 平台, 基础数据["总数"])

            # 构建返回数据
            return self._构建达人统计结果(基础数据, 有联系方式数, 用户id, 平台)

        except Exception as e:
            系统日志器.error(f"获取用户{用户id} {平台}平台达人统计失败: {str(e)}")
            return self._构建默认达人统计数据()

    async def _获取平台达人基础统计(
        self, 用户id: int, 平台: str, 开始时间: datetime, 结束时间: datetime
    ) -> Dict[str, int]:
        """获取平台达人基础统计数据"""
        # 调用数据访问层获取平台达人基础统计数据
        基础统计结果 = await self.数据访问层.获取_平台达人基础统计_数据(用户id, 平台, 开始时间, 结束时间)

        if not 基础统计结果:
            return {}

        return 基础统计结果

    async def _获取平台达人联系方式统计(self, 用户id: int, 平台: str, 总数: int) -> int:
        """获取平台达人联系方式统计"""
        if 总数 == 0:
            return 0

        # 调用数据访问层获取平台达人联系方式统计数据
        return await self.数据访问层.获取_平台达人联系方式统计_数据(用户id, 平台)

    def _构建达人统计结果(self, 基础数据: Dict, 有联系方式数: int, 用户id: int, 平台: str) -> Dict[str, Any]:
        """构建达人统计结果"""
        总数 = 基础数据.get("总数", 0)
        时间范围新增 = 基础数据.get("时间范围新增", 0)
        七天新增 = 基础数据.get("七天新增", 0)

        系统日志器.info(
            f"[_获取用户平台达人统计] 用户{用户id} {平台}平台: 总数={总数}, 有联系方式={有联系方式数}, 时间范围新增={时间范围新增}, 七天新增={七天新增}"
        )

        return {
            "总数": 总数,
            "有联系方式": 有联系方式数,
            "时间范围新增": 时间范围新增,
            "七天新增": 七天新增,
            "metrics": {
                "total": 总数,
                "withContact": 有联系方式数,
                "new7d": 七天新增,
                "timeRangeNew": 时间范围新增,
            }
        }

    def _构建默认达人统计数据(self) -> Dict[str, Any]:
        """构建默认达人统计数据"""
        return {
            "总数": 0,
            "有联系方式": 0,
            "时间范围新增": 0,
            "七天新增": 0,
            "metrics": {
                "total": 0,
                "withContact": 0,
                "new7d": 0,
                "timeRangeNew": 0
            }
        }
