"""
用户联系人服务层 - 业务逻辑层
负责处理用户联系人相关的业务逻辑，调用数据访问层进行数据操作
基于三层分离架构设计
"""

from typing import Any, Dict, List, Optional
from uuid import UUID

from 数据.用户联系人数据访问层 import 用户联系人数据访问实例, 达人补充信息数据访问实例
from 服务.基础服务 import 数据服务基类


class 用户联系人服务(数据服务基类):
    """用户联系人业务服务"""

    def __init__(self):
        super().__init__("用户联系人服务", 用户联系人数据访问实例)
        self.用户联系人数据访问 = 用户联系人数据访问实例
        self.达人补充信息数据访问 = 达人补充信息数据访问实例

    def 清理寄样信息列表(self, 寄样信息: Optional[List]) -> Optional[List]:
        """
        清理寄样信息列表的公共方法

        Args:
            寄样信息: 原始寄样信息列表

        Returns:
            清理后的寄样信息列表
        """
        if 寄样信息 is None:
            return None

        清理后的寄样信息 = []
        for 信息 in 寄样信息:
            if isinstance(信息, dict):
                清理后的信息 = {
                    "收件人": self.清理字符串参数(信息.get("收件人", "")).strip(),
                    "地址": self.清理字符串参数(信息.get("地址", "")).strip(),
                    "电话": self.清理字符串参数(信息.get("电话", "")).strip(),
                }
                # 验证必填字段
                if (
                    清理后的信息["收件人"]
                    and 清理后的信息["地址"]
                    and 清理后的信息["电话"]
                ):
                    清理后的寄样信息.append(清理后的信息)

        return 清理后的寄样信息 if 清理后的寄样信息 else None

    async def 关联联系人到达人补充信息(
        self, 补充信息id: int, 用户联系人id: UUID
    ) -> Dict[str, Any]:
        """
        将用户联系人关联到达人补充信息

        参数:
            补充信息id: 用户达人补充信息表id
            用户联系人id: 用户联系人UUID

        返回:
            操作结果字典
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"补充信息id": 补充信息id, "用户联系人id": 用户联系人id},
            ["补充信息id", "用户联系人id"],
        )
        if 验证错误:
            return self.构建失败响应(验证错误)

        try:
            # 调用数据访问层关联联系人
            关联成功 = await self.达人补充信息数据访问.关联用户联系人(
                补充信息id, 用户联系人id
            )

            if 关联成功:
                self.记录信息(
                    "关联联系人成功", 补充信息id=补充信息id, 联系人id=用户联系人id
                )
                return self.构建成功响应(None, "关联联系人成功")
            else:
                return self.构建失败响应("未找到对应的补充信息记录")

        except Exception as e:
            self.记录错误(
                "关联联系人失败", e, 补充信息id=补充信息id, 联系人id=用户联系人id
            )
            return self.构建失败响应(f"关联联系人失败: {str(e)}")

    async def 创建联系人并关联到补充信息(
        self, 用户id: int, 姓名: str, 补充信息id: int, 寄样地址: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建用户联系人并关联到达人补充信息（事务操作）
        确保事务一致性：如果关联失败，则不创建联系人

        参数:
            用户id: 用户id
            姓名: 联系人姓名
            补充信息id: 用户达人补充信息表id
            寄样地址: 寄样地址（可选）

        返回:
            包含联系人UUID的结果字典
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"用户id": 用户id, "姓名": 姓名, "补充信息id": 补充信息id},
            ["用户id", "姓名", "补充信息id"],
        )
        if 验证错误:
            return self.构建失败响应(验证错误)

        # 清理姓名参数
        清理后的姓名 = self.清理字符串参数(姓名)
        if not 清理后的姓名:
            return self.构建失败响应("联系人姓名不能为空")

        # 清理寄样地址参数
        清理后的寄样地址 = None
        if 寄样地址 is not None:
            清理后的寄样地址 = self.清理字符串参数(寄样地址)

        try:
            # 调用数据访问层创建联系人并关联
            联系人数据 = await self.达人补充信息数据访问.创建联系人并关联到补充信息(
                用户id, 清理后的姓名, 补充信息id, 清理后的寄样地址
            )

            if 联系人数据:
                self.记录信息(
                    "创建联系人并关联成功",
                    用户id=用户id,
                    姓名=清理后的姓名,
                    补充信息id=补充信息id,
                )
                return self.构建成功响应(
                    {
                        "用户联系人id": str(联系人数据["用户联系人id"]),
                        "姓名": 联系人数据["姓名"],
                        "用户表id": 联系人数据["用户表id"],
                    },
                    "创建联系人并关联成功",
                )
            else:
                return self.构建失败响应("创建联系人并关联失败")

        except Exception as e:
            self.记录错误(
                "创建联系人并关联失败",
                e,
                用户id=用户id,
                姓名=清理后的姓名,
                补充信息id=补充信息id,
            )
            return self.构建失败响应(f"创建联系人并关联失败: {str(e)}")

    async def 查询用户联系人列表(
        self, 用户id: int, 关键词: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询用户的联系人列表

        参数:
            用户id: 用户id
            关键词: 搜索关键词（姓名或联系方式）

        返回:
            联系人列表
        """
        # 参数验证
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 调用数据访问层查询联系人列表
            结果 = await self.用户联系人数据访问.查询用户联系人_通过用户id(
                用户id, 关键词
            )

            return self.构建成功响应(结果, "查询联系人列表成功")

        except Exception as e:
            self.记录错误("查询联系人列表失败", e, 用户id=用户id)
            return self.构建失败响应(f"查询联系人列表失败: {str(e)}")

    async def 查询单个联系人(self, 联系人id: UUID, 用户id: int) -> Dict[str, Any]:
        """
        查询单个联系人详情

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）

        返回:
            联系人详情
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 查询联系人信息（包含关联的达人信息）
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_包含达人信息(
                联系人id
            )

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            # 验证联系人是否属于当前用户
            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限访问此联系人")

            return self.构建成功响应(联系人信息, "查询联系人详情成功")

        except Exception as e:
            self.记录错误("查询联系人详情失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"查询联系人详情失败: {str(e)}")

    async def 更新联系人(
        self,
        联系人id: UUID,
        用户id: int,
        姓名: Optional[str] = None,
        寄样信息: Optional[List] = None,
    ) -> Dict[str, Any]:
        """
        更新联系人信息

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）
            姓名: 新的姓名（可选）
            寄样信息: 新的寄样信息列表（可选）

        返回:
            更新结果
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        # 检查是否有要更新的字段
        if 姓名 is None and 寄样信息 is None:
            return self.构建失败响应("请提供要更新的字段")

        try:
            # 首先验证联系人是否存在且属于当前用户
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_通过ID(联系人id)

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限修改此联系人")

            # 清理字符串参数
            清理后的姓名 = None
            if 姓名 is not None:
                清理后的姓名 = self.清理字符串参数(姓名)
                if 清理后的姓名 == "":
                    return self.构建失败响应("联系人姓名不能为空")

            # 处理寄样信息列表
            清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

            # 执行更新
            更新成功 = await self.用户联系人数据访问.更新用户联系人(
                联系人id, 清理后的姓名, 清理后的寄样信息
            )

            if 更新成功:
                self.记录信息("更新联系人成功", 联系人id=联系人id, 用户id=用户id)
                return self.构建成功响应(None, "更新联系人成功")
            else:
                return self.构建失败响应("更新联系人失败")

        except Exception as e:
            self.记录错误("更新联系人失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"更新联系人失败: {str(e)}")

    async def 创建联系人并关联达人(
        self, 用户id: int, 请求数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        创建联系人并关联达人（支持两种关联方式）

        参数:
            用户id: 用户id
            请求数据: 包含联系人信息和关联方式的请求数据

        返回:
            创建结果
        """
        try:
            姓名 = 请求数据.get("姓名")
            寄样信息 = 请求数据.get("寄样信息")
            关联方式 = 请求数据.get("关联方式")

            if not 姓名 or not 姓名.strip():
                return self.构建失败响应("联系人姓名不能为空")

            if 关联方式 not in ["existing", "create"]:
                return self.构建失败响应("关联方式必须是 existing 或 create")

            # 清理姓名
            清理后的姓名 = self.清理字符串参数(姓名)

            # 处理寄样信息列表
            清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

            if 关联方式 == "existing":
                # 方式一：关联已有达人联系方式
                补充信息id = 请求数据.get("补充信息id")
                if not 补充信息id:
                    return self.构建失败响应("选择已有关联方式时，补充信息id不能为空")

                return await self._创建联系人并关联已有达人信息(
                    用户id, 清理后的姓名, 清理后的寄样信息, 补充信息id
                )
            else:
                # 方式二：创建新的达人联系方式
                达人联系方式信息 = 请求数据.get("达人联系方式信息")
                if not 达人联系方式信息:
                    return self.构建失败响应(
                        "创建新关联方式时，达人联系方式信息不能为空"
                    )

                return await self._创建联系人并创建达人信息(
                    用户id, 清理后的姓名, 清理后的寄样信息, 达人联系方式信息
                )

        except Exception as e:
            self.记录错误("创建联系人并关联达人失败", e, 用户id=用户id)
            return self.构建失败响应(f"创建联系人并关联达人失败: {str(e)}")

    async def _创建联系人并关联已有达人信息(
        self, 用户id: int, 姓名: str, 寄样信息: Optional[List], 补充信息id: int
    ) -> Dict[str, Any]:
        """
        创建联系人并关联到已有达人补充信息
        """
        try:
            # 1. 验证补充信息是否属于当前用户
            from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

            关联记录 = await 达人补充信息数据访问.查询关联记录_通过补充信息id(
                补充信息id
            )
            if not 关联记录 or 关联记录.get("用户id") != 用户id:
                return self.构建失败响应("补充信息不存在或无权限访问")

            # 2. 创建联系人
            联系人数据 = await self.用户联系人数据访问.创建用户联系人(
                用户id, 姓名, 寄样信息
            )

            if not 联系人数据:
                return self.构建失败响应("创建联系人失败")

            # 3. 关联到达人补充信息
            联系人id = 联系人数据["用户联系人id"]
            关联成功 = await 达人补充信息数据访问.关联用户联系人(补充信息id, 联系人id)

            if not 关联成功:
                # 如果关联失败，需要删除已创建的联系人（回滚）
                await self.用户联系人数据访问.删除用户联系人(联系人id)
                return self.构建失败响应("关联达人信息失败")

            self.记录信息(
                "创建联系人并关联已有达人信息成功",
                联系人id=联系人id,
                补充信息id=补充信息id,
            )
            return self.构建成功响应(联系人数据, "创建联系人并关联成功")

        except Exception as e:
            self.记录错误("创建联系人并关联已有达人信息失败", e)
            raise

    async def _创建联系人并创建达人信息(
        self,
        用户id: int,
        姓名: str,
        寄样信息: Optional[List],
        达人联系方式信息: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        创建联系人并创建新的达人补充信息
        """
        try:
            # 1. 创建联系人
            联系人数据 = await self.用户联系人数据访问.创建用户联系人(
                用户id, 姓名, 寄样信息
            )

            if not 联系人数据:
                return self.构建失败响应("创建联系人失败")

            联系人id = 联系人数据["用户联系人id"]

            # 2. 通过数据访问层创建完整的达人信息和关联
            from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

            # 调用数据访问层的事务方法创建完整的达人信息链
            补充信息id = await 达人补充信息数据访问.创建完整达人信息链(
                用户id=用户id,
                联系方式=达人联系方式信息.get("联系方式"),
                联系方式类型=达人联系方式信息.get("联系方式类型"),
                个人备注=达人联系方式信息.get("个人备注"),
                个人标签=达人联系方式信息.get("个人标签"),
                补充信息=达人联系方式信息.get("补充信息"),
                联系人id=联系人id,
            )

            if not 补充信息id:
                # 回滚：删除已创建的联系人
                await self.用户联系人数据访问.删除用户联系人(联系人id)
                return self.构建失败响应("创建达人信息失败")

            self.记录信息(
                "创建联系人并创建达人信息成功", 联系人id=联系人id, 补充信息id=补充信息id
            )
            return self.构建成功响应(联系人数据, "创建联系人并关联成功")

        except Exception as e:
            self.记录错误("创建联系人并创建达人信息失败", e)
            raise

    async def 搜索达人补充信息_用于关联(
        self,
        用户id: int,
        关键词: Optional[str] = None,
        页码: int = 1,
        每页数量: int = 20,
    ) -> Dict[str, Any]:
        """
        搜索达人补充信息用于联系人关联

        参数:
            用户id: 用户id
            关键词: 搜索关键词
            页码: 页码
            每页数量: 每页数量

        返回:
            搜索结果
        """
        try:
            from 数据.达人补充信息数据访问层 import 达人补充信息数据访问

            结果 = await 达人补充信息数据访问.搜索达人补充信息_用于关联(
                用户id, 关键词, 页码, 每页数量
            )

            self.记录信息(
                "搜索达人补充信息成功", 用户id=用户id, 总数=结果.get("总数", 0)
            )
            return self.构建成功响应(结果, "搜索成功")

        except Exception as e:
            self.记录错误("搜索达人补充信息失败", e, 用户id=用户id)
            return self.构建失败响应(f"搜索失败: {str(e)}")

    async def 删除联系人(self, 联系人id: UUID, 用户id: int) -> Dict[str, Any]:
        """
        删除联系人

        参数:
            联系人id: 联系人UUID
            用户id: 用户id（用于权限验证）

        返回:
            删除结果
        """
        # 参数验证
        if not 联系人id:
            return self.构建失败响应("联系人ID无效")
        if not 用户id or 用户id <= 0:
            return self.构建失败响应("用户id无效")

        try:
            # 首先验证联系人是否存在且属于当前用户
            联系人信息 = await self.用户联系人数据访问.查询用户联系人_通过ID(联系人id)

            if not 联系人信息:
                return self.构建失败响应("联系人不存在")

            if 联系人信息["用户表id"] != 用户id:
                return self.构建失败响应("无权限删除此联系人")

            # 执行删除
            删除成功 = await self.用户联系人数据访问.删除用户联系人(联系人id)

            if 删除成功:
                self.记录信息("删除联系人成功", 联系人id=联系人id, 用户id=用户id)
                return self.构建成功响应(None, "删除联系人成功")
            else:
                return self.构建失败响应("删除联系人失败")

        except Exception as e:
            self.记录错误("删除联系人失败", e, 联系人id=联系人id, 用户id=用户id)
            return self.构建失败响应(f"删除联系人失败: {str(e)}")

    async def 创建联系人(
        self,
        用户id: int,
        姓名: str,
        寄样信息: Optional[List] = None,
    ) -> Dict[str, Any]:
        """
        创建联系人（不关联到达人补充信息）

        参数:
            用户id: 用户id
            姓名: 联系人姓名
            寄样信息: 寄样信息列表（可选）

        返回:
            创建结果
        """
        # 参数验证
        验证错误 = self.验证必需参数(
            {"用户id": 用户id, "姓名": 姓名}, ["用户id", "姓名"]
        )
        if 验证错误:
            return self.构建失败响应(验证错误)

        # 清理参数
        清理后的姓名 = self.清理字符串参数(姓名)
        if not 清理后的姓名:
            return self.构建失败响应("联系人姓名不能为空")

        # 处理寄样信息列表
        清理后的寄样信息 = self.清理寄样信息列表(寄样信息)

        try:
            # 调用数据访问层创建联系人
            联系人数据 = await self.用户联系人数据访问.创建用户联系人(
                用户id, 清理后的姓名, 清理后的寄样信息
            )

            if 联系人数据:
                self.记录信息("创建联系人成功", 用户id=用户id, 姓名=清理后的姓名)
                return self.构建成功响应(
                    {
                        "用户联系人id": str(联系人数据["用户联系人id"]),
                        "姓名": 联系人数据["姓名"],
                        "用户表id": 联系人数据["用户表id"],
                        "寄样地址": 联系人数据.get("寄样地址"),
                    },
                    "创建联系人成功",
                )
            else:
                return self.构建失败响应("创建联系人失败")

        except Exception as e:
            self.记录错误("创建联系人失败", e, 用户id=用户id, 姓名=清理后的姓名)
            return self.构建失败响应(f"创建联系人失败: {str(e)}")


# 创建服务实例
用户联系人服务实例 = 用户联系人服务()
