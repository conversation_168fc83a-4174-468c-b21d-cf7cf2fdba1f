import api from './api'

/**
 * 订单管理服务
 * Order Management Service
 */
class OrderService {
  constructor() {
    this.baseUrl = '/store'
  }

  /**
   * 获取订单列表
   * Get order list
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} Order list data
   */
  async getOrderList(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/list`, params)
      return response
    } catch (error) {
      console.error('Failed to fetch order list:', error)
      throw error
    }
  }

  /**
   * 导入订单Excel文件
   * Import orders from Excel file
   * @param {File} file - Excel文件
   * @returns {Promise<Object>} Import result
   */
  async importOrders(file) {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await api.post(`${this.baseUrl}/orders/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 60000 // 1分钟超时，异步导入模式
      })

      return response
    } catch (error) {
      console.error('Failed to import orders:', error)
      throw error
    }
  }

  /**
   * 获取订单详情
   * Get order detail
   * @param {string} orderId - 订单ID
   * @returns {Promise<Object>} Order detail data
   */
  async getOrderDetail(orderId) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/detail`, {
        订单id: orderId
      })
      return response
    } catch (error) {
      console.error('Failed to fetch order detail:', error)
      throw error
    }
  }

  /**
   * 获取订单状态选项（从后端获取数据库中实际存在的状态）
   * Get order status options from backend database
   * @returns {Promise<Object>} Order status options
   */
  async getStatusOptions() {
    try {
      const response = await api.get(`${this.baseUrl}/orders/status-options`)

      // 将后端返回的字符串数组转换为前端需要的格式
      if (response.status === 100 && Array.isArray(response.data)) {
        const formattedData = response.data.map(status => ({
          label: status,
          value: status,
          color: this.getStatusColor(status)
        }))

        return {
          status: 100,
          message: '获取订单状态选项成功',
          data: formattedData
        }
      }

      return response
    } catch (error) {
      console.error('Failed to fetch order status options:', error)
      throw error
    }
  }

  /**
   * 获取订单统计数据
   * Get order statistics
   * @param {Object} params - 统计参数
   * @returns {Promise<Object>} Order statistics
   */
  async getOrderStats(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/stats`, params)
      return response
    } catch (error) {
      console.error('Failed to fetch order stats:', error)
      throw error
    }
  }

  /**
   * 导出订单数据
   * Export order data
   * @param {Object} params - 导出参数
   * @returns {Promise<Blob>} Excel file blob
   */
  async exportOrders(params = {}) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/export`, params, {
        responseType: 'blob'
      })
      return response
    } catch (error) {
      console.error('Failed to export orders:', error)
      throw error
    }
  }

  /**
   * 批量删除订单
   * Batch delete orders
   * @param {Array} orderIds - 订单ID数组
   * @returns {Promise<Object>} Delete result
   */
  async batchDeleteOrders(orderIds) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/batch-delete`, {
        订单ids: orderIds
      })
      return response
    } catch (error) {
      console.error('Failed to batch delete orders:', error)
      throw error
    }
  }

  /**
   * 更新订单状态
   * Update order status
   * @param {string} orderId - 订单ID
   * @param {string} status - 新状态
   * @returns {Promise<Object>} Update result
   */
  async updateOrderStatus(orderId, status) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/update-status`, {
        订单id: orderId,
        订单状态: status
      })
      return response
    } catch (error) {
      console.error('Failed to update order status:', error)
      throw error
    }
  }



  /**
   * 获取订单状态对应的颜色
   * Get color for order status
   * @param {string} status - 订单状态
   * @returns {string} Color name
   */
  getStatusColor(status) {
    const colorMap = {
      '订单付款': 'blue',
      '订单收货': 'green',
      '订单退货退款': 'red'
    }
    return colorMap[status] || 'default'
  }

  /**
   * 获取订单详情
   * Get order detail
   * @param {string|number} orderId - 订单ID
   * @returns {Promise<Object>} Order detail data
   */
  async getOrderDetail(orderId) {
    try {
      const requestBody = {
        订单id: String(orderId)  // 确保作为字符串传递，避免大数精度问题
      }
      const response = await api.post(`${this.baseUrl}/orders/detail`, requestBody)
      return response
    } catch (error) {
      console.error('Failed to fetch order detail:', error)
      throw error
    }
  }

  /**
   * 格式化金额
   * Format amount
   * @param {number} amount - 金额
   * @returns {string} Formatted amount
   */
  formatAmount(amount) {
    if (typeof amount !== 'number') return '¥0.00'
    return `¥${amount.toFixed(2)}`
  }

  /**
   * 格式化百分比
   * Format percentage
   * @param {number} rate - 比率
   * @returns {string} Formatted percentage
   */
  formatPercentage(rate) {
    if (typeof rate !== 'number') return '0.00%'
    return `${(rate * 100).toFixed(2)}%`
  }

  /**
   * 验证订单数据
   * 获取导入记录列表
   * Get import records list
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} Import records list
   */
  async getImportRecords(params = {}) {
    try {
      const { 页码 = 1, 每页数量 = 20, 状态筛选 } = params

      const requestBody = {
        页码,
        每页数量,
        状态筛选
      }

      const response = await api.post(`${this.baseUrl}/orders/import/records`, requestBody)
      return response
    } catch (error) {
      console.error('Failed to fetch import records:', error)
      throw error
    }
  }

  /**
   * 查询导入进度
   * Get import progress
   * @param {string} taskId - 任务ID
   * @returns {Promise<Object>} Import progress data
   */
  async getImportProgress(taskId) {
    try {
      const requestBody = {
        任务ID: taskId
      }
      const response = await api.post(`${this.baseUrl}/orders/import/progress`, requestBody)
      return response
    } catch (error) {
      console.error('Failed to fetch import progress:', error)
      throw error
    }
  }

  /**
   * 导入订单文件
   * Import orders file
   * @param {File} file - Excel文件
   * @returns {Promise<Object>} Import task info
   */
  async importOrders(file) {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await api.post(`${this.baseUrl}/orders/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response
    } catch (error) {
      console.error('Failed to import orders:', error)
      throw error
    }
  }

  /**
   * 续传导入任务
   * Continue import task
   * @param {number} importRecordId - 导入记录id
   * @returns {Promise<Object>} Continue result
   */
  async continueImport(importRecordId) {
    try {
      const response = await api.post(`${this.baseUrl}/orders/continue-import`, {
        导入记录id: importRecordId
      })
      return response
    } catch (error) {
      console.error('Failed to continue import:', error)
      throw error
    }
  }

  /**
   * Validate order data
   * @param {Object} orderData - 订单数据
   * @returns {Object} Validation result
   */
  validateOrderData(orderData) {
    const errors = []

    if (!orderData.订单id) {
      errors.push('订单ID不能为空')
    }

    if (!orderData.商品名称) {
      errors.push('商品名称不能为空')
    }

    if (orderData.支付金额 && orderData.支付金额 < 0) {
      errors.push('支付金额不能为负数')
    }

    if (orderData.佣金率 && (orderData.佣金率 < 0 || orderData.佣金率 > 1)) {
      errors.push('佣金率必须在0-1之间')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// 创建并导出服务实例
const orderService = new OrderService()
export default orderService
