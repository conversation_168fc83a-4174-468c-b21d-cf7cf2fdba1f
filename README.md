# 灵邀AI达人管家 - 后端服务

[![FastAPI](https://img.shields.io/badge/FastAPI-Latest-green.svg)](https://fastapi.tiangolo.com)
[![Python](https://img.shields.io/badge/Python-3.8%2B-blue.svg)](https://python.org)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-12%2B-blue.svg)](https://postgresql.org)

基于 FastAPI + PostgreSQL 的现代化后端API服务，集成AI智能体、知识库管理、图片OCR等功能。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- PostgreSQL 12+
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd invitation-system-backend
```

### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate
```

### 3. 安装依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或使用国内镜像加速
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
# 配置数据库连接、api密钥等
```

### 5. 启动服务
```bash
# 开发模式启动
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 常用命令

### 虚拟环境管理
```bash
# 进入虚拟环境
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # macOS/Linux

# 退出虚拟环境
deactivate

# 重新安装依赖
pip install -r requirements.txt --force-reinstall

# 更新依赖
pip install --upgrade -r requirements.txt
```

### 开发工具
```bash
# 检查依赖冲突
pip check

# 查看已安装包
pip list

# 查看过时包
pip list --outdated

# 生成依赖文件
pip freeze > requirements.txt
```

## 📁 项目结构

```
invitation-system-backend/
├── main.py                 # 应用入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖列表
├── .env                   # 环境变量
├── 路由/                  # API路由
├── 服务/                  # 业务逻辑
├── 数据/                  # 数据访问层
├── 工具/                  # 工具函数
├── 日志/                  # 日志系统
└── 静态/                  # 静态文件
```

## 🛠 技术栈

| 组件 | 技术 | 版本 |
|------|------|------|
| **Web框架** | FastAPI | Latest |
| **数据库** | PostgreSQL | 12+ |
| **ORM** | AsyncPG | Latest |
| **认证** | JWT | Latest |
| **文档处理** | Unstructured | 0.18.11 |
| **图片OCR** | RapidOCR | 1.2.3 |
| **AI集成** | LangChain | Latest |

## 🔑 核心功能

- **用户认证** - JWT令牌认证
- **达人管理** - 达人信息CRUD
- **AI智能体** - LangChain集成
- **知识库** - 文档向量化存储
- **图片OCR** - 图片文字识别
- **文件处理** - 多格式文档解析
- **数据分析** - 业务数据统计

## 🐛 故障排除

### 常见问题

**1. 依赖安装失败**
```bash
# 清理pip缓存
pip cache purge

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

**2. 数据库连接失败**
```bash
# 检查PostgreSQL服务状态
# 确认config.py中的数据库配置
# 检查防火墙设置
```

**3. 端口被占用**
```bash
# 查看端口占用
netstat -ano | findstr :8000  # Windows
lsof -i :8000  # macOS/Linux

# 更换端口启动
uvicorn main:app --port 8001
```

## 📝 开发规范

### 代码风格
- 使用中文命名（函数、变量、类）
- 遵循PEP8规范
- 添加类型注解
- 编写文档字符串

### 提交规范
```bash
git commit -m "feat: 新增用户认证功能"
git commit -m "fix: 修复数据库连接问题"
git commit -m "docs: 更新API文档"
```

## 📞 技术支持

如遇问题，请检查：
1. Python版本是否符合要求
2. 虚拟环境是否正确激活
3. 依赖是否完整安装
4. 环境变量是否正确配置
5. 数据库服务是否正常运行
