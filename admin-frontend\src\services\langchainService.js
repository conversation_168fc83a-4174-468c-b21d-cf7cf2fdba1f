/**
 * LangChain智能体管理服务
 * 提供智能体创建、管理、对话等功能的API调用
 */

import apiClient from './apiClient'

class LangChainService {
  // ==================== 智能体管理 ====================

  /**
   * 创建智能体
   * @param {Object} 智能体数据 - 智能体配置数据
   * @returns {Promise} API响应
   */
  async 创建智能体(智能体数据) {
    try {
      const response = await apiClient.post('/langchain/agents/create', 智能体数据)
      return response
    } catch (error) {
      console.error('创建智能体失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体列表
   * @param {Object} 查询参数 - 分页和过滤参数
   * @returns {Promise} API响应
   */
  async 获取智能体列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        智能体类型: null,
        状态: null,
        搜索关键词: null,
        是否公开: null
      }

      const 最终参数 = { ...默认参数, ...查询参数 }

      // 清理null值，避免后端验证问题
      Object.keys(最终参数).forEach(key => {
        if (最终参数[key] === null || 最终参数[key] === undefined) {
          delete 最终参数[key]
        }
      })

      console.log('发送智能体列表请求参数:', 最终参数)
      const response = await apiClient.post('/admin/langchain/agents/list', 最终参数)
      return response
    } catch (error) {
      console.error('获取智能体列表失败:', error)
      throw error
    }
  }

  /**
   * 获取智能体详情
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 获取智能体详情(智能体id) {
    try {
      const response = await apiClient.post('/admin/langchain/agents/detail', { 智能体id })
      return response
    } catch (error) {
      console.error('获取智能体详情失败:', error)
      throw error
    }
  }

  /**
   * 更新智能体配置
   * @param {string} 智能体id - 智能体id
   * @param {Object} 更新数据 - 要更新的配置
   * @returns {Promise} API响应
   */
  async 更新智能体配置(智能体id, 更新数据) {
    try {
      const 请求数据 = { 智能体id, ...更新数据 }
      const response = await apiClient.post('/admin/langchain/agents/update', 请求数据)
      return response
    } catch (error) {
      console.error('更新智能体配置失败:', error)
      throw error
    }
  }

  /**
   * 删除智能体
   * @param {string} 智能体id - 智能体id
   * @returns {Promise} API响应
   */
  async 删除智能体(智能体id) {
    try {
      const response = await apiClient.post('/langchain/agents/delete', { 智能体id })
      return response
    } catch (error) {
      console.error('删除智能体失败:', error)
      throw error
    }
  }

  /**
   * 智能体对话
   * @param {string} 智能体id - 智能体id
   * @param {Object} 消息数据 - 消息内容和配置
   * @returns {Promise} API响应
   */
  async 智能体对话(智能体id, 消息数据) {
    try {
      const 请求数据 = { 智能体id, ...消息数据 }
      const response = await apiClient.post('/user/langchain/agents/chat', 请求数据)
      return response
    } catch (error) {
      console.error('智能体对话失败:', error)
      throw error
    }
  }

  /**
   * 清空智能体记忆
   * @param {string} 智能体id - 智能体id
   * @param {string} 会话id - 可选的会话id
   * @returns {Promise} API响应
   */
  async 清空智能体记忆(智能体id, 会话id = null) {
    try {
      const url = `/user/langchain/agents/${智能体id}/clear-memory`
      const params = 会话id ? { 会话id } : {}
      const response = await apiClient.post(url, {}, { params })
      return response
    } catch (error) {
      console.error('清空智能体记忆失败:', error)
      throw error
    }
  }

  // ==================== 知识库管理 ====================

  /**
   * 创建知识库
   * @param {Object} 知识库数据 - 知识库配置数据
   * @returns {Promise} API响应
   */
  async 创建知识库(知识库数据) {
    try {
      const response = await apiClient.post('/langchain/knowledge/create', 知识库数据)
      return response
    } catch (error) {
      console.error('创建知识库失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库列表
   * @param {Object} 查询参数 - 分页和过滤参数
   * @returns {Promise} API响应
   */
  async 获取知识库列表(查询参数 = {}) {
    try {
      const 默认参数 = {
        页码: 1,
        每页数量: 20,
        状态: null,
        搜索关键词: null
      }

      const 最终参数 = { ...默认参数, ...查询参数 }
      const response = await apiClient.post('/langchain/knowledge/list', 最终参数)
      return response
    } catch (error) {
      console.error('获取知识库列表失败:', error)
      throw error
    }
  }

  /**
   * 获取知识库详情
   * @param {number} 知识id - 知识id
   * @returns {Promise} API响应
   */
  async 获取知识库详情(知识id) {
    try {
      const response = await apiClient.post(`/langchain/knowledge/${知识id}/detail`)
      return response
    } catch (error) {
      console.error('获取知识库详情失败:', error)
      throw error
    }
  }

  /**
   * 上传文档到知识库
   * @param {number} 知识id - 知识id
   * @param {File} 文件 - 要上传的文件
   * @param {Object} 元数据 - 可选的文档元数据
   * @returns {Promise} API响应
   */
  async 上传文档(知识id, 文件, 元数据 = {}) {
    try {
      const formData = new FormData()
      formData.append('知识id', 知识id)
      formData.append('文件', 文件)
      formData.append('元数据', JSON.stringify(元数据))

      const response = await apiClient.post('/langchain/knowledge/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response
    } catch (error) {
      console.error('上传文档失败:', error)
      throw error
    }
  }

  /**
   * 检索知识库
   * @param {Object} 检索参数 - 检索配置
   * @returns {Promise} API响应
   */
  async 检索知识库(检索参数) {
    try {
      const response = await apiClient.post('/langchain/knowledge/search', 检索参数)
      return response
    } catch (error) {
      console.error('检索知识库失败:', error)
      throw error
    }
  }

  /**
   * 删除知识库
   * @param {number} 知识id - 知识id
   * @returns {Promise} API响应
   */
  async 删除知识库(知识id) {
    try {
      const response = await apiClient.post(`/langchain/knowledge/${知识id}/delete`)
      return response
    } catch (error) {
      console.error('删除知识库失败:', error)
      throw error
    }
  }

  // ==================== 系统管理 ====================

  /**
   * 获取系统统计
   * @returns {Promise} API响应
   */
  async 获取系统统计() {
    try {
      const response = await apiClient.post('/langchain/system/statistics')
      return response
    } catch (error) {
      console.error('获取系统统计失败:', error)
      throw error
    }
  }

  // ==================== 使用统计相关接口 ====================

  /**
   * 获取智能体使用统计
   * @param {Object} 查询参数 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取使用统计数据(查询参数) {
    try {
      const response = await apiClient.post('/admin/langchain/statistics/usage', 查询参数)
      return response
    } catch (error) {
      console.error('获取使用统计数据失败:', error)
      throw error
    }
  }

  /**
   * 获取对话记录列表
   * @param {Object} 查询参数 - 查询条件
   * @returns {Promise} API响应
   */
  async 获取对话记录列表(查询参数) {
    try {
      const response = await apiClient.post('/admin/langchain/statistics/conversation-records', 查询参数)
      return response
    } catch (error) {
      console.error('获取对话记录列表失败:', error)
      throw error
    }
  }

  /**
   * 获取统计仪表盘数据
   * @returns {Promise} API响应
   */
  async 获取统计仪表盘数据() {
    try {
      console.log('🔍 发送仪表盘数据请求')
      const response = await apiClient.get('/admin/langchain/statistics/dashboard')
      console.log('📊 仪表盘数据响应:', response)
      return response
    } catch (error) {
      console.error('获取统计仪表盘数据失败:', error)
      throw error
    }
  }

  // ==================== 工具方法 ====================
  // 注意：工具方法已迁移到 langchainUtils.js 统一管理，避免重复代码
  // 可通过 LangChainUtils.格式化智能体类型() 等方式调用
}

// 创建并导出服务实例
const langchainService = new LangChainService()
export default langchainService
