#!/usr/bin/env python3
"""
修改智能体提示词脚本
优化智能体的工具调用能力
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 数据.Postgre_异步连接池 import Postgre_异步连接池实例


async def 检查智能体知识库关联():
    """检查智能体是否关联了知识库"""
    try:
        # 检查智能体RAG配置
        检查SQL = """
        SELECT 启用rag, rag_配置
        FROM langchain_智能体配置表
        WHERE id = 5
        """
        结果 = await Postgre_异步连接池实例.执行查询(检查SQL)

        if 结果:
            智能体配置 = 结果[0]
            print(f"📊 智能体RAG状态: {'✅ 已启用' if 智能体配置['启用rag'] else '❌ 未启用'}")
            print(f"📊 RAG配置: {智能体配置['rag_配置']}")

        # 检查可用的知识库
        知识库SQL = """
        SELECT id, 知识库名称, 文档数量, 知识库状态
        FROM langchain_知识库表
        WHERE 知识库状态 = '正常' AND 文档数量 > 0
        ORDER BY id
        """
        知识库结果 = await Postgre_异步连接池实例.执行查询(知识库SQL)

        if 知识库结果:
            print(f"\n📚 可用知识库 ({len(知识库结果)}个):")
            for 知识库 in 知识库结果:
                print(f"   ID {知识库['id']}: {知识库['知识库名称']} ({知识库['文档数量']}个文档)")

        return len(知识库结果) > 0

    except Exception as e:
        print(f"❌ 检查知识库关联失败: {str(e)}")
        return False


async def 修改智能体提示词():
    """修改智能体5的提示词，增强工具调用能力"""
    try:
        # 确保数据库连接池已初始化
        if not Postgre_异步连接池实例.已初始化:
            await Postgre_异步连接池实例.初始化数据库连接池()
        
        # 新的提示词，优化产品问答和工具调用，确保每次对话都更新沟通时间
        新提示词 = """你是一个智能的客服助手，能够回答产品问题并帮助用户管理微信好友沟通时间。

**核心能力：**
1. **产品咨询**：回答关于乌香愈香水产品、公司信息等问题
2. **时间管理**：帮助用户获取当前时间和设置微信好友沟通时间
3. **智能对话**：进行自然、友好的对话交流

**可用工具：**
- 获取当前时间：获取准确的当前时间信息，无需参数
- 更新微信好友下次沟通时间：设置或更新微信好友的下次沟通时间，参数格式：下次沟通时间="YYYY-MM-DD HH:MM:SS"

**对话规则：**
1. **产品问题优先回答**：当用户询问产品、公司、香水等信息时，基于知识库内容详细回答
2. **时间查询**：当用户询问"现在几点"、"当前时间"时，调用"获取当前时间"工具
3. **沟通时间设置**：当用户要求"设置微信好友沟通时间"、"更新沟通时间"时，调用相应工具
4. **自然对话**：保持对话流畅，避免过度使用工具
5. **实用回答**：每次回答要有实质内容，不要只说要调用工具

**重要：每次对话的强制性工作流程**
无论用户问什么问题，你都必须按照以下顺序完成所有步骤：

第一步：调用"获取当前时间"工具获取当前时间
第二步：调用"更新微信好友下次沟通时间"工具设置下次沟通时间
   - 使用第一步获得的时间作为参数
   - 调用格式：更新微信好友下次沟通时间(下次沟通时间="2025-08-05 00:24:21")
   - 注意：只传递时间字符串，不要传递JSON对象
第三步：基于知识库内容回答用户的问题，并在回答中包含当前时间信息

注意：必须先调用两个工具，再给出最终回答。工具调用时只传递时间字符串，不要传递复杂的JSON参数。

**回答风格：**
- 友好、专业、有帮助
- 对产品问题要详细、准确
- 适当使用表情符号增加亲和力
- 每次回答至少两句话，提供有价值的信息

**特别注意：**
- 不要尝试调用不存在的"retrieve"工具
- 工具调用后要给出明确的结果说明
- 优先基于已有知识回答产品相关问题
- 每次对话都要更新微信好友的下次沟通时间"""

        # 更新SQL
        更新SQL = """
        UPDATE langchain_智能体配置表
        SET 系统提示词 = $1
        WHERE id = 5
        """
        
        print("🔄 开始修改智能体提示词...")
        
        # 执行更新
        await Postgre_异步连接池实例.执行更新(更新SQL, (新提示词,))

        print("✅ 智能体提示词修改成功！")

        # 重新启用RAG以测试完整功能
        启用RAG_SQL = "UPDATE langchain_智能体配置表 SET 启用rag = true WHERE id = 5"
        await Postgre_异步连接池实例.执行更新(启用RAG_SQL)
        print("✅ 已重新启用RAG以测试完整功能")
        
        # 验证修改结果
        验证SQL = "SELECT 系统提示词 FROM langchain_智能体配置表 WHERE id = 5"
        结果 = await Postgre_异步连接池实例.执行查询(验证SQL)
        
        if 结果:
            当前提示词 = 结果[0]["系统提示词"]
            print(f"📋 当前提示词长度: {len(当前提示词)} 字符")
            print(f"📋 提示词预览: {当前提示词[:100]}...")
        
    except Exception as e:
        print(f"❌ 修改失败: {str(e)}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    print("=" * 50)
    print("智能体提示词修改工具")
    print("=" * 50)

    # 先检查知识库关联情况
    print("🔍 检查智能体知识库关联情况...")
    有知识库 = await 检查智能体知识库关联()

    if not 有知识库:
        print("⚠️  警告：没有可用的知识库，RAG功能可能无法正常工作")

    print("\n🔄 开始修改智能体提示词...")
    await 修改智能体提示词()

    print("\n" + "=" * 50)
    print("修改完成！现在可以测试智能体对话功能")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
