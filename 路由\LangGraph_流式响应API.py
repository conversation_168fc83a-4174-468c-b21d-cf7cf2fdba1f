"""
LangGraph流式响应API
支持实时流式对话和工具调用监控
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from 依赖项.认证 import 获取当前用户
from 数据.LangChain_状态持久化数据层 import LangChain状态持久化数据层实例
from 服务.LangChain_RAG引擎 import RAG引擎实例 as LangChain_RAG引擎实例
from 服务.LangChain_智能体服务 import LangChain智能体服务实例
from 服务.LangChain_模型管理器 import LangChain模型管理器实例
from 服务.LangChain_现代化工具注册系统 import 现代化工具注册系统实例
from 服务.LangGraph_智能体核心 import 创建LangGraph智能体核心
from 服务.React_Agent集成 import 创建React_Agent集成

# 日志配置
流式API日志器 = logging.getLogger("LangGraph流式响应API")

# 创建路由器
router = APIRouter(prefix="/api/v1/langgraph", tags=["LangGraph流式响应"])


# 请求模型
class 流式对话请求(BaseModel):
    message: str = Field(..., description="用户消息")
    agent_id: int = Field(..., description="智能体id")
    session_id: Optional[str] = Field(None, description="会话id")
    thread_id: Optional[str] = Field(None, description="线程ID")
    agent_type: str = Field(
        "langgraph", description="智能体实现类型: langgraph, react_agent"
    )
    stream: bool = Field(True, description="是否启用流式响应")


class 工具调用监控请求(BaseModel):
    thread_id: str = Field(..., description="线程ID")
    limit: int = Field(20, description="返回记录数量")


class 状态查询请求(BaseModel):
    thread_id: str = Field(..., description="线程ID")
    include_history: bool = Field(False, description="是否包含历史记录")


# 全局智能体缓存
智能体缓存 = {}


async def 获取或创建智能体(智能体id: int, 智能体类型: str = "langgraph"):
    """获取或创建智能体实例"""
    try:
        缓存键 = f"{智能体类型}_{智能体id}"

        if 缓存键 in 智能体缓存:
            return 智能体缓存[缓存键]

        # 获取智能体配置
        智能体配置 = await LangChain智能体服务实例.获取智能体详情(智能体id)
        if not 智能体配置:
            raise HTTPException(status_code=404, detail=f"智能体 {智能体id} 不存在")

        # 创建智能体实例
        if 智能体类型 == "langgraph":
            智能体实例 = 创建LangGraph智能体核心(
                智能体配置,
                LangChain模型管理器实例,
                LangChain_RAG引擎实例,
                现代化工具注册系统实例,
            )
        elif 智能体类型 == "react_agent":
            智能体实例 = 创建React_Agent集成(智能体配置, LangChain模型管理器实例)
        else:
            raise HTTPException(
                status_code=400, detail=f"不支持的智能体实现类型: {智能体类型}"
            )

        # 初始化智能体
        初始化成功 = await 智能体实例.初始化()
        if not 初始化成功:
            raise HTTPException(status_code=500, detail="智能体初始化失败")

        # 缓存智能体
        智能体缓存[缓存键] = 智能体实例

        流式API日志器.info(f"✅ 创建智能体成功: {智能体类型}_{智能体id}")
        return 智能体实例

    except Exception as e:
        流式API日志器.error(f"❌ 获取或创建智能体失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"智能体创建失败: {str(e)}")


@router.post("/chat/stream")
async def 流式对话(请求: 流式对话请求, 当前用户: dict = Depends(获取当前用户)):
    """流式对话接口"""
    try:
        流式API日志器.info(
            f"🚀 开始流式对话: 用户{当前用户['id']}, 智能体{请求.agent_id}"
        )

        # 获取智能体实例
        智能体实例 = await 获取或创建智能体(请求.agent_id, 请求.agent_type)

        if 请求.stream:
            # 流式响应
            return StreamingResponse(
                流式对话生成器(智能体实例, 请求, 当前用户["id"]),
                media_type="text/plain; charset=utf-8",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",
                },
            )
        else:
            # 非流式响应
            结果 = await 智能体实例.对话(
                用户输入=请求.message,
                用户id=当前用户["id"],
                会话id=请求.session_id,
                线程ID=请求.thread_id,
            )
            return 结果

    except Exception as e:
        流式API日志器.error(f"❌ 流式对话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"对话失败: {str(e)}")


async def 流式对话生成器(智能体实例, 请求: 流式对话请求, 用户id: int):
    """流式对话生成器"""
    try:
        # 发送开始事件
        yield f"data: {json.dumps({'type': 'start', 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"

        # 检查智能体是否支持流式
        if hasattr(智能体实例, "流式对话"):
            # 使用智能体的流式方法
            async for chunk in 智能体实例.流式对话(
                用户输入=请求.message,
                用户id=用户id,
                会话id=请求.session_id,
                线程ID=请求.thread_id,
            ):
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        else:
            # 模拟流式响应
            结果 = await 智能体实例.对话(
                用户输入=请求.message,
                用户id=用户id,
                会话id=请求.session_id,
                线程ID=请求.thread_id,
            )

            if 结果["success"]:
                响应内容 = 结果["response"]

                # 分块发送响应
                chunk_size = 10
                for i in range(0, len(响应内容), chunk_size):
                    chunk = 响应内容[i : i + chunk_size]
                    yield f"data: {json.dumps({'type': 'chunk', 'content': chunk}, ensure_ascii=False)}\n\n"
                    await asyncio.sleep(0.05)  # 模拟流式延迟

                # 发送完成事件
                yield f"data: {json.dumps({'type': 'complete', 'result': 结果}, ensure_ascii=False)}\n\n"
            else:
                # 发送错误事件
                yield f"data: {json.dumps({'type': 'error', 'error': 结果.get('error', '未知错误')}, ensure_ascii=False)}\n\n"

        # 发送结束事件
        yield f"data: {json.dumps({'type': 'end', 'timestamp': datetime.now().isoformat()}, ensure_ascii=False)}\n\n"

    except Exception as e:
        流式API日志器.error(f"❌ 流式生成器异常: {str(e)}")
        yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"


@router.post("/tools/monitor")
async def 工具调用监控(请求: 工具调用监控请求, 当前用户: dict = Depends(获取当前用户)):
    """工具调用监控接口"""
    try:
        流式API日志器.info(f"📊 工具调用监控: 线程{请求.thread_id}")

        # 获取工具调用历史
        工具历史 = await LangChain状态持久化数据层实例.获取工具调用历史(
            请求.thread_id, 请求.limit
        )

        # 获取执行步骤历史
        步骤历史 = await LangChain状态持久化数据层实例.获取执行步骤历史(
            请求.thread_id, 请求.limit
        )

        return {
            "success": True,
            "thread_id": 请求.thread_id,
            "tool_calls": 工具历史,
            "execution_steps": 步骤历史,
            "summary": {
                "total_tool_calls": len(工具历史),
                "total_steps": len(步骤历史),
                "success_rate": _计算成功率(工具历史),
            },
        }

    except Exception as e:
        流式API日志器.error(f"❌ 工具调用监控失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"监控失败: {str(e)}")


@router.post("/state/query")
async def 状态查询(请求: 状态查询请求, 当前用户: dict = Depends(获取当前用户)):
    """状态查询接口"""
    try:
        流式API日志器.info(f"🔍 状态查询: 线程{请求.thread_id}")

        # 获取线程信息
        线程信息 = await LangChain状态持久化数据层实例.获取对话线程(请求.thread_id)

        if not 线程信息:
            raise HTTPException(status_code=404, detail="线程不存在")

        # 获取最新检查点
        最新检查点 = await LangChain状态持久化数据层实例.获取检查点(请求.thread_id)

        响应数据 = {
            "success": True,
            "thread_info": 线程信息,
            "latest_checkpoint": 最新检查点,
            "timestamp": datetime.now().isoformat(),
        }

        # 如果需要历史记录
        if 请求.include_history:
            检查点历史 = await LangChain状态持久化数据层实例.获取检查点历史(
                请求.thread_id, 10
            )
            响应数据["checkpoint_history"] = 检查点历史

        return 响应数据

    except Exception as e:
        流式API日志器.error(f"❌ 状态查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@router.get("/agents/stats")
async def 智能体统计(当前用户: dict = Depends(获取当前用户)):
    """智能体统计信息"""
    try:
        流式API日志器.info(f"📈 智能体统计查询: 用户{当前用户['id']}")

        # 获取工具统计
        工具统计 = 现代化工具注册系统实例.获取工具统计()

        # 获取缓存的智能体信息
        缓存统计 = {"cached_agents": len(智能体缓存), "agent_types": {}}

        for 缓存键 in 智能体缓存.keys():
            智能体类型 = 缓存键.split("_")[0]
            缓存统计["agent_types"][智能体类型] = (
                缓存统计["agent_types"].get(智能体类型, 0) + 1
            )

        return {
            "success": True,
            "tool_stats": 工具统计,
            "cache_stats": 缓存统计,
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        流式API日志器.error(f"❌ 智能体统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"统计失败: {str(e)}")


@router.delete("/cache/clear")
async def 清理缓存(当前用户: dict = Depends(获取当前用户)):
    """清理智能体缓存"""
    try:
        流式API日志器.info(f"🧹 清理智能体缓存: 用户{当前用户['id']}")

        清理前数量 = len(智能体缓存)
        智能体缓存.clear()

        return {
            "success": True,
            "message": f"已清理 {清理前数量} 个缓存的智能体",
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        流式API日志器.error(f"❌ 清理缓存失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")


def _计算成功率(工具历史: list) -> float:
    """计算工具调用成功率"""
    if not 工具历史:
        return 0.0

    成功数 = sum(1 for 记录 in 工具历史 if 记录.get("执行状态") == "success")
    return (成功数 / len(工具历史)) * 100


# 后台任务：定期清理过期缓存
async def 定期清理缓存():
    """定期清理过期的智能体缓存"""
    while True:
        try:
            await asyncio.sleep(3600)  # 每小时执行一次

            # 简单的缓存清理策略：清理所有缓存
            if len(智能体缓存) > 10:  # 如果缓存过多
                智能体缓存.clear()
                流式API日志器.info("🧹 定期清理智能体缓存完成")

        except Exception as e:
            流式API日志器.error(f"❌ 定期清理缓存失败: {str(e)}")


# 启动后台任务
@router.on_event("startup")
async def 启动后台任务():
    """启动后台任务"""
    asyncio.create_task(定期清理缓存())
