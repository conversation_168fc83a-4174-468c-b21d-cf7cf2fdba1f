"""
微信达人管理数据模型

功能概述：
- 定义微信达人相关的所有数据模型
- 支持微信达人基本信息、联系方式、统计数据等
- 与抖音达人模型保持一致的结构设计
- 支持认领、编辑、搜索等核心功能

作者: CRM系统开发团队
创建时间: 2024-06-25
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, validator
from datetime import datetime
from enum import Enum


class 微信达人状态枚举(str, Enum):
    """微信达人账号状态枚举"""
    正常 = "正常"
    封禁 = "封禁"
    注销 = "注销"
    未知 = "未知"


class 合作状态枚举(str, Enum):
    """合作状态枚举"""
    未联系 = "未联系"
    已联系 = "已联系"
    洽谈中 = "洽谈中"
    合作中 = "合作中"
    已完成 = "已完成"
    已拒绝 = "已拒绝"


class 微信达人基本信息模型(BaseModel):
    """微信达人基本信息模型"""
    id: Optional[int] = Field(None, description="达人id")
    微信号: Optional[str] = Field(None, description="微信号")
    昵称: Optional[str] = Field(None, description="微信昵称")
    头像: Optional[str] = Field(None, description="头像URL")
    个人简介: Optional[str] = Field(None, description="个人简介")
    地区: Optional[str] = Field(None, description="所在地区")
    性别: Optional[str] = Field(None, description="性别")
    账号状态: 微信达人状态枚举 = Field(微信达人状态枚举.正常, description="账号状态")
    
    # 统计数据
    好友数: Optional[int] = Field(0, description="好友数量")
    朋友圈发布数: Optional[int] = Field(0, description="朋友圈发布数")
    
    # 系统字段
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    更新时间: Optional[datetime] = Field(None, description="更新时间")
    
    @validator('微信号')
    def validate_wechat_id(cls, v):
        """验证微信号格式"""
        if v and len(v) > 20:
            raise ValueError('微信号长度不能超过20个字符')
        return v
    
    @validator('昵称')
    def validate_昵称(cls, v):
        """验证昵称格式"""
        if v and len(v) > 30:
            raise ValueError('昵称长度不能超过30个字符')
        return v


class 微信达人联系方式模型(BaseModel):
    """微信达人联系方式模型"""
    id: Optional[int] = Field(None, description="联系方式ID")
    达人id: int = Field(..., description="关联的达人id")
    联系类型: str = Field(..., description="联系方式类型：手机、邮箱、QQ等")
    联系内容: str = Field(..., description="具体的联系方式内容")
    是否验证: bool = Field(False, description="是否已验证")
    备注: Optional[str] = Field(None, description="备注信息")
    创建时间: Optional[datetime] = Field(None, description="创建时间")
    
    @validator('联系内容')
    def validate_contact_content(cls, v):
        """验证联系方式内容"""
        if not v or not v.strip():
            raise ValueError('联系方式内容不能为空')
        return v.strip()


class 微信达人认领信息模型(BaseModel):
    """微信达人认领信息模型"""
    id: Optional[int] = Field(None, description="认领记录id")
    达人id: int = Field(..., description="达人id")
    用户id: int = Field(..., description="认领用户id")
    团队id: Optional[int] = Field(None, description="团队id")
    认领时间: Optional[datetime] = Field(None, description="认领时间")
    合作状态: 合作状态枚举 = Field(合作状态枚举.未联系, description="合作状态")
    个人备注: Optional[str] = Field(None, description="个人备注")
    个人标签: Optional[List[str]] = Field([], description="个人标签")
    
    @validator('个人备注')
    def validate_personal_note(cls, v):
        """验证个人备注长度"""
        if v and len(v) > 500:
            raise ValueError('个人备注不能超过500个字符')
        return v
    
    @validator('个人标签')
    def validate_personal_tags(cls, v):
        """验证个人标签"""
        if v and len(v) > 10:
            raise ValueError('个人标签最多10个')
        return v


class 微信达人查询请求模型(BaseModel):
    """微信达人查询请求模型"""
    页码: int = Field(1, ge=1, description="页码")
    每页数量: int = Field(20, ge=1, le=100, description="每页数量")
    最后id: Optional[int] = Field(0, description="流式分页最后id")

    # 搜索条件
    关键词: Optional[str] = Field(None, description="搜索关键词（微信号、昵称）")
    地区: Optional[str] = Field(None, description="地区筛选")
    性别: Optional[str] = Field(None, description="性别筛选")
    账号状态: Optional[微信达人状态枚举] = Field(None, description="账号状态筛选")
    有联系方式: Optional[bool] = Field(None, description="是否有联系方式")

    # 排序条件
    排序字段: Optional[str] = Field("更新时间", description="排序字段")
    排序方式: Optional[str] = Field("desc", description="排序方式：asc/desc")

    # 兼容性字段：与抖音达人保持一致
    筛选条件: Optional[Dict[str, Any]] = Field(None, description="额外的筛选条件（兼容性字段）")


class 微信达人详情响应模型(BaseModel):
    """微信达人详情响应模型"""
    基本信息: 微信达人基本信息模型
    联系方式列表: List[微信达人联系方式模型] = []
    认领信息: Optional[微信达人认领信息模型] = None
    当前用户认领状态: Dict[str, Any] = Field(default_factory=dict, description="当前用户认领状态")


class 微信达人认领请求模型(BaseModel):
    """微信达人认领请求模型"""
    达人id: int = Field(..., description="要认领的达人id")


class 微信达人编辑请求模型(BaseModel):
    """微信达人编辑请求模型"""
    达人id: int = Field(..., description="达人id")
    微信号: Optional[str] = Field(None, description="微信号")
    手机号: Optional[str] = Field(None, description="手机号")
    邮箱: Optional[str] = Field(None, description="邮箱")
    QQ号: Optional[str] = Field(None, description="QQ号")
    合作状态: Optional[合作状态枚举] = Field(None, description="合作状态")
    个人备注: Optional[str] = Field(None, description="个人备注")
    个人标签: Optional[List[str]] = Field(None, description="个人标签")
    
    @validator('手机号')
    def validate_phone(cls, v):
        """验证手机号格式"""
        if v and not v.match(r'^1[3-9]\d{9}$'):
            raise ValueError('手机号格式不正确')
        return v
    
    @validator('邮箱')
    def validate_email(cls, v):
        """验证邮箱格式"""
        if v and '@' not in v:
            raise ValueError('邮箱格式不正确')
        return v


class 微信达人创建请求模型(BaseModel):
    """微信达人创建请求模型"""
    微信号: str = Field(..., description="微信号")
    昵称: Optional[str] = Field(None, description="昵称")
    头像: Optional[str] = Field(None, description="头像URL")
    个人简介: Optional[str] = Field(None, description="个人简介")
    地区: Optional[str] = Field(None, description="地区")
    性别: Optional[str] = Field(None, description="性别")


class 微信达人统计响应模型(BaseModel):
    """微信达人统计响应模型"""
    总数: int = Field(0, description="总达人数")
    本周新增: int = Field(0, description="本周新增达人数")
    有联系方式: int = Field(0, description="有联系方式的达人数")
    已认领: int = Field(0, description="已认领的达人数")
    我的达人: int = Field(0, description="我认领的达人数")


class 微信达人批量操作请求模型(BaseModel):
    """微信达人批量操作请求模型"""
    达人id列表: List[int] = Field(..., description="要操作的达人id列表")
    操作类型: str = Field(..., description="操作类型：认领、取消认领、删除等")
    
    @validator('达人id列表')
    def validate_talent_ids(cls, v):
        """验证达人id列表"""
        if not v:
            raise ValueError('达人id列表不能为空')
        if len(v) > 100:
            raise ValueError('单次批量操作不能超过100个达人')
        return v
