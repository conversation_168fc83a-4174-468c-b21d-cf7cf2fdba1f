#!/usr/bin/env python3
"""
智能体自动参数注入测试脚本
使用HTTP接口测试智能体工具调用功能
"""

import requests


def 获取测试JWT():
    """通过登录接口获取真实的JWT令牌"""
    try:
        # 使用测试用户登录获取真实JWT
        登录数据 = {
            "手机号": "15257152395",
            "密码": "850483315",  # 测试用户密码
        }

        response = requests.post(
            "http://localhost:8000/user/login",
            json=登录数据,
            headers={"Content-Type": "application/json"},
            timeout=30,
        )

        print(f"📊 登录响应状态: {response.status_code}")
        print(f"📄 登录响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get("status") == 100:
                token = result.get("data", {}).get("access_token")
                print("✅ 成功获取JWT令牌")
                return token

        print(f"❌ 登录失败: {response.status_code}")
        return None

    except Exception as e:
        print(f"❌ 获取JWT失败: {str(e)}")
        return None


def 测试自动参数注入():
    """测试智能体工具调用的自动参数注入功能"""
    try:
        print("🚀 开始测试智能体工具调用...")
        print("📋 测试目标: 更新微信好友下次沟通时间")
        print()

        # 获取JWT令牌
        token = 获取测试JWT()
        if not token:
            print("❌ 无法获取有效的JWT令牌")
            return

        # 准备请求数据（不需要传用户id，通过JWT获取）
        测试数据 = {
            "智能体id": 5,
            "用户消息": "请帮我设置微信好友的下次沟通时间为明天下午3点",
            "会话id": "test-tool-call",
            "自定义变量": {"我方微信号id": 6, "识别id": 1},
        }

        # 调用HTTP接口
        print("🔄 调用智能体测试接口...")

        response = requests.post(
            "http://localhost:8000/admin/langchain/agents/test",
            json=测试数据,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            },
            timeout=120,
        )

        print(f"📊 HTTP响应状态: {response.status_code}")

        if response.status_code != 200:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return

        对话结果 = response.json()

        if 对话结果.get("status") != 100:
            print(f"❌ 对话失败: {对话结果.get('message', 'N/A')}")
            return

        data = 对话结果.get("data", {})
        工具使用信息 = data.get("工具使用信息", [])

        print(f"✅ 智能体回复: {data.get('智能体回复', '')}")
        print(f"🔧 工具调用数量: {len(工具使用信息)}")

        # 查找目标工具调用
        目标工具 = next(
            (
                工具
                for 工具 in 工具使用信息
                if 工具.get("工具名称") == "更新微信好友下次沟通时间"
            ),
            None,
        )

        if not 目标工具:
            print("❌ 未找到目标工具调用")
            return

        工具参数 = 目标工具.get("调用参数", {})
        print(f"🎯 工具调用参数: {工具参数}")

        # 简化验证：检查关键参数
        检查项目 = [
            ("我方微信号id", 6),
            ("识别id", 1),
            ("用户id", 3),
            ("下次沟通时间", None),  # 只检查存在性
        ]

        成功数 = 0
        for 参数名, 期望值 in 检查项目:
            实际值 = 工具参数.get(参数名)
            if 参数名 == "下次沟通时间":
                if 实际值:
                    print(f"✅ {参数名}: {实际值}")
                    成功数 += 1
                else:
                    print(f"❌ {参数名}: 未找到")
            elif 实际值 == 期望值:
                print(f"✅ {参数名}: {实际值}")
                成功数 += 1
            else:
                print(f"❌ {参数名}: 期望 {期望值}, 实际 {实际值}")

        if 成功数 == len(检查项目):
            print("\n🎉 参数注入测试成功！")
        else:
            print(f"\n⚠️ 测试部分成功 ({成功数}/{len(检查项目)})")

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")


def main():
    """主函数"""
    print("=" * 50)
    print("智能体工具调用测试")
    print("=" * 50)
    测试自动参数注入()
    print("=" * 50)


if __name__ == "__main__":
    main()
