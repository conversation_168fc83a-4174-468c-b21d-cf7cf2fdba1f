"""
LangChain复合智能体核心
统一的智能体架构，整合所有功能为单一复合智能体

核心功能：
1. RAG检索功能 - 集成检索增强生成
2. 工具动态加载 - 支持从数据库动态加载和配置工具
3. 工具循环调用 - 支持智能体在对话过程中多次调用不同工具
4. JSON格式化输出 - 确保输出结果为标准JSON格式
5. 自定义变量支持 - 允许在对话过程中使用和管理自定义变量
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass

# 数据层导入
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据.LangChain_模型数据层 import LangChain模型数据层实例

# 服务层导入
from 服务.LangChain_模型管理器 import LangChain模型管理器实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例
from 服务.LangChain_工具管理器 import LangChain工具管理器实例
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例

# 日志导入
from 日志 import 应用日志器 as 复合智能体日志器
from 状态 import 状态

# 复合智能体常量定义
class 复合智能体常量:
    """复合智能体常量定义"""

    # RAG检索配置
    默认最大检索数量 = 5
    默认相似度阈值 = 0.7
    默认检索策略 = "similarity"

    # 模型配置
    默认温度 = 0.7
    默认最大令牌数 = 4000

    # 超时配置
    对话超时秒数 = 30
    检索超时秒数 = 10
    工具调用超时秒数 = 15

    # 缓存配置
    实例缓存大小 = 100

# LangChain组件导入
try:
    from langchain_core.messages import HumanMessage, AIMessage
    from langchain_core.tools import BaseTool
    from langchain.agents import AgentExecutor, create_react_agent
    from langchain_core.prompts import PromptTemplate
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    class BaseTool:
        pass
    class AgentExecutor:
        pass


@dataclass
class 复合智能体配置:
    """复合智能体配置数据类"""
    用户id: int
    智能体名称: str
    智能体id: Optional[int] = None
    模型名称: str = "qwen-turbo"
    系统提示词: str = ""
    温度参数: float = 0.7
    最大令牌数: int = 4000
    记忆窗口大小: int = 10
    启用rag: bool = False
    工具列表: Optional[List[str]] = None
    输出格式: str = "text"
    自定义回复格式: Optional[Dict[str, Any]] = None
    自定义变量: Optional[List[Dict[str, Any]]] = None

    def __post_init__(self):
        if self.工具列表 is None:
            self.工具列表 = []
        if self.自定义变量 is None:
            self.自定义变量 = []


class 复合智能体实例:
    """复合智能体运行时实例 - 整合所有功能"""

    def __init__(self, 配置: 复合智能体配置, 实例ID: str):
        self.配置 = 配置
        self.实例ID = 实例ID
        self.创建时间 = datetime.now()
        
        # 核心组件
        self.模型 = None
        self.工具列表 = []
        self.智能体执行器 = None
        self.已初始化 = False
        
        # RAG组件
        self.rag_启用 = 配置.启用rag
        
        # 记忆组件
        self.对话历史 = []
        
        复合智能体日志器.info(f"复合智能体实例创建: {实例ID}")

    async def 初始化(self):
        """异步初始化复合智能体实例"""
        try:
            # 1. 初始化模型
            await self._初始化模型()
            
            # 2. 加载工具
            await self._加载工具()
            
            # 3. 创建智能体执行器
            await self._创建智能体执行器()
            
            self.已初始化 = True
            复合智能体日志器.info(f"✅ 复合智能体实例初始化完成: {self.实例ID}")

        except Exception as e:
            复合智能体日志器.error(f"❌ 复合智能体实例初始化失败 {self.实例ID}: {str(e)}")
            raise

    async def _初始化模型(self):
        """初始化模型"""
        self.模型 = await LangChain模型管理器实例.获取模型(
            self.配置.模型名称,
            温度=self.配置.温度参数,
            最大令牌数=self.配置.最大令牌数,
            结构化输出模式=self.配置.自定义回复格式,
        )
        
        if not self.模型:
            raise Exception(f"模型初始化失败: {self.配置.模型名称}")

    async def _加载工具(self):
        """加载工具列表"""
        self.工具列表 = []
        
        if not self.配置.工具列表:
            return
            
        for 工具名称 in self.配置.工具列表:
            工具实例 = await self._获取工具实例(工具名称)
            if 工具实例:
                self.工具列表.append(工具实例)
                复合智能体日志器.info(f"✅ 加载工具: {工具名称}")
            else:
                复合智能体日志器.warning(f"⚠️ 工具加载失败: {工具名称}")

    async def _获取工具实例(self, 工具名称: str):
        """获取工具实例"""
        try:
            # 确保工具管理器已初始化
            if not LangChain工具管理器实例.已初始化:
                # 使用异步工厂方法创建实例
                await LangChain工具管理器实例._异步初始化()

            # 首先尝试从工具管理器的注册表获取
            if 工具名称 in LangChain工具管理器实例.工具注册表:
                工具实例 = LangChain工具管理器实例.工具注册表[工具名称]
                复合智能体日志器.debug(f"从工具管理器获取工具: {工具名称}")
                return 工具实例

            # 然后尝试从内部函数包装器获取
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            if 工具名称 in 内部函数包装器实例.已注册工具:
                内部工具 = 内部函数包装器实例.已注册工具[工具名称]
                复合智能体日志器.debug(f"从内部函数包装器获取工具: {工具名称}")
                return 内部工具

            复合智能体日志器.warning(f"工具未找到: {工具名称}")
            return None

        except Exception as e:
            复合智能体日志器.error(f"获取工具实例失败 {工具名称}: {str(e)}")
            return None

    async def _创建智能体执行器(self):
        """创建智能体执行器"""
        try:
            if not LANGCHAIN_AVAILABLE:
                复合智能体日志器.warning("LangChain不可用，使用简单模型")
                self.智能体执行器 = self.模型
                return

            if not self.工具列表:
                # 无工具的简单对话模型
                self.智能体执行器 = self.模型
                return

            # 创建ReAct提示词模板
            react_prompt = PromptTemplate.from_template("""
{system_prompt}

你有以下工具可以使用：
{tools}

使用以下格式：

Question: 用户的问题
Thought: 你应该思考要做什么
Action: 要采取的行动，应该是 [{tool_names}] 中的一个
Action Input: 行动的输入
Observation: 行动的结果
... (这个 Thought/Action/Action Input/Observation 可以重复N次)
Thought: 我现在知道最终答案了
Final Answer: 对原始问题的最终答案

开始！

Question: {input}
Thought: {agent_scratchpad}
""")

            # 创建ReAct智能体
            agent = create_react_agent(
                llm=self.模型,
                tools=self.工具列表,
                prompt=react_prompt
            )

            # 创建智能体执行器
            self.智能体执行器 = AgentExecutor(
                agent=agent,
                tools=self.工具列表,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=5,
                max_execution_time=30
            )

            复合智能体日志器.info(f"✅ 创建LangChain ReAct智能体: {len(self.工具列表)}个工具")

        except Exception as e:
            复合智能体日志器.error(f"创建智能体执行器失败: {str(e)}")
            # 回退到简单模型
            self.智能体执行器 = self.模型

    async def 处理对话(self, 用户消息: str, 会话id: str, 自定义变量: Optional[Dict[str, Any]] = None) -> str:
        """处理对话的核心方法 - 整合所有功能"""
        try:
            # 0. 设置线程上下文参数（用于工具调用）
            self._设置线程上下文(自定义变量)

            # 1. RAG检索（如果启用）
            rag_上下文 = ""
            if self.rag_启用:
                rag_上下文 = await self._执行RAG检索(用户消息)

            # 2. 构建增强提示词
            增强提示词 = self._构建增强提示词(用户消息, rag_上下文, 自定义变量)

            # 3. 执行智能体对话
            if isinstance(self.智能体执行器, AgentExecutor):
                # 使用工具智能体
                response = await self.智能体执行器.ainvoke({
                    "input": 增强提示词,
                    "system_prompt": self.配置.系统提示词
                })
                回复 = response.get("output", "")
            else:
                # 使用简单模型
                messages = [HumanMessage(content=增强提示词)]
                response = await self.智能体执行器.ainvoke(messages)
                回复 = response.content if hasattr(response, 'content') else str(response)

            # 4. 更新对话历史
            self._更新对话历史(用户消息, 回复)

            return 回复

        except Exception as e:
            return 复合智能体错误处理器.处理对话错误(e, self.配置.智能体id or 0, 用户消息)

    async def _执行RAG检索(self, 用户输入: str) -> str:
        """执行RAG检索获取知识库上下文"""
        try:
            if not self.配置.智能体id:
                return ""

            # 委托给知识库服务处理
            检索结果 = await LangChain知识库服务实例.智能体RAG检索(self.配置.智能体id, 用户输入)
            return 检索结果 or ""

        except Exception as e:
            return 复合智能体错误处理器.处理RAG检索错误(e, self.配置.智能体id or 0)

    def _构建增强提示词(self, 用户消息: str, rag_上下文: str, 自定义变量: Optional[Dict[str, Any]]) -> str:
        """构建增强的提示词"""
        try:
            基础提示词 = 用户消息

            # 添加RAG上下文
            if rag_上下文:
                基础提示词 = f"""
## 知识库上下文
以下是从知识库中检索到的相关信息，请基于这些信息回答用户问题：

{rag_上下文}

---

## 用户问题
{用户消息}
"""

            # 添加自定义变量
            if 自定义变量:
                变量部分 = "\n".join([f"{k}: {v}" for k, v in 自定义变量.items()])
                基础提示词 = f"""
## 自定义变量
{变量部分}

---

{基础提示词}
"""

            return 基础提示词

        except Exception as e:
            复合智能体日志器.error(f"构建增强提示词失败: {str(e)}")
            return 用户消息

    def _更新对话历史(self, 用户消息: str, ai回复: str):
        """更新对话历史"""
        try:
            self.对话历史.append({
                "type": "human",
                "content": 用户消息,
                "timestamp": datetime.now().isoformat()
            })
            self.对话历史.append({
                "type": "ai", 
                "content": ai回复,
                "timestamp": datetime.now().isoformat()
            })

            # 保持记忆窗口大小
            if len(self.对话历史) > self.配置.记忆窗口大小 * 2:
                self.对话历史 = self.对话历史[-self.配置.记忆窗口大小 * 2:]

        except Exception as e:
            复合智能体日志器.error(f"更新对话历史失败: {str(e)}")

    def _设置线程上下文(self, 自定义变量: Optional[Dict[str, Any]]):
        """设置线程上下文参数，用于工具调用时的参数注入"""
        try:
            # 导入内部函数包装器的上下文设置函数
            from 服务.LangChain_内部函数包装器 import 设置当前用户id, 设置当前智能体id, 设置当前自定义变量

            # 设置用户id
            设置当前用户id(self.配置.用户id)

            # 设置智能体id
            if self.配置.智能体id:
                设置当前智能体id(self.配置.智能体id)

            # 设置自定义变量
            if 自定义变量:
                设置当前自定义变量(自定义变量)
            else:
                # 设置默认的自定义变量（用于测试）
                默认变量 = {
                    "我方微信号id": 6,  # 默认微信号id
                    "识别id": 1,       # 默认识别id
                }
                设置当前自定义变量(默认变量)

            复合智能体日志器.debug(f"✅ 线程上下文设置完成: 用户id={self.配置.用户id}, 智能体id={self.配置.智能体id}")

        except Exception as e:
            复合智能体日志器.error(f"设置线程上下文失败: {str(e)}")


class 复合智能体错误处理器:
    """复合智能体错误处理器 - 简化版本"""

    @staticmethod
    def 处理对话错误(error: Exception, 智能体id: int, 用户消息: str) -> str:
        """处理对话错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"对话处理失败 - 智能体ID: {智能体id}, 错误: {error_message}")
            return f"对话处理失败: {error_message}"
        except Exception:
            return "对话处理失败: 未知错误"

    @staticmethod
    def 处理RAG检索错误(error: Exception, 智能体id: int) -> str:
        """处理RAG检索错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"RAG检索失败 - 智能体ID: {智能体id}, 错误: {error_message}")
            return ""  # RAG失败时返回空字符串，不影响对话
        except Exception:
            return ""

    @staticmethod
    def 处理工具调用错误(error: Exception, 工具名称: str) -> str:
        """处理工具调用错误"""
        try:
            error_message = str(error)
            复合智能体日志器.error(f"工具调用失败 - 工具: {工具名称}, 错误: {error_message}")
            return f"工具调用失败: {error_message}"
        except Exception:
            return "工具调用失败: 未知错误"


class LangChain复合智能体管理器:
    """复合智能体管理器 - 统一管理所有智能体实例"""

    def __init__(self):
        self.实例缓存: Dict[str, 复合智能体实例] = {}
        self.已初始化 = True
        复合智能体日志器.info("LangChain复合智能体管理器创建成功")

    async def 获取或创建实例(self, 智能体id: int, 用户id: int) -> 复合智能体实例:
        """获取或创建复合智能体实例"""
        try:
            实例ID = f"{智能体id}_{用户id}"
            
            # 检查缓存
            if 实例ID in self.实例缓存:
                return self.实例缓存[实例ID]

            # 获取智能体配置
            智能体数据 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)
            if not 智能体数据:
                raise Exception(f"智能体不存在: {智能体id}")

            # 创建配置对象
            配置 = 复合智能体配置(
                用户id=用户id,
                智能体名称=智能体数据.get("智能体名称", ""),
                智能体id=智能体id,
                模型名称=智能体数据.get("模型名称", "qwen-turbo"),
                系统提示词=智能体数据.get("系统提示词", ""),
                温度参数=智能体数据.get("温度参数", 0.7),
                最大令牌数=智能体数据.get("最大令牌数", 4000),
                记忆窗口大小=智能体数据.get("记忆窗口大小", 10),
                启用rag=智能体数据.get("启用rag", False),
                输出格式=智能体数据.get("输出格式", "text"),
                自定义回复格式=智能体数据.get("自定义回复格式") if isinstance(智能体数据.get("自定义回复格式"), dict) else None,
                自定义变量=智能体数据.get("自定义变量") if isinstance(智能体数据.get("自定义变量"), list) else []
            )

            # 获取工具列表
            工具关联 = await LangChain智能体数据层实例.获取智能体工具关联(智能体id)
            配置.工具列表 = [工具["工具名称"] for 工具 in 工具关联] if 工具关联 else []

            # 创建新实例
            实例 = 复合智能体实例(配置, 实例ID)
            await 实例.初始化()
            
            # 缓存实例
            self.实例缓存[实例ID] = 实例
            
            复合智能体日志器.info(f"✅ 创建新复合智能体实例: {实例ID}")
            return 实例

        except Exception as e:
            复合智能体日志器.error(f"获取或创建实例失败 {智能体id}_{用户id}: {str(e)}")
            raise

    def 清理实例缓存(self):
        """清理实例缓存"""
        try:
            清理前数量 = len(self.实例缓存)
            self.实例缓存.clear()
            复合智能体日志器.info(f"✅ 清理实例缓存完成: {清理前数量} -> 0")
        except Exception as e:
            复合智能体日志器.error(f"清理实例缓存失败: {str(e)}")


# 创建全局实例
LangChain复合智能体管理器实例 = LangChain复合智能体管理器()
