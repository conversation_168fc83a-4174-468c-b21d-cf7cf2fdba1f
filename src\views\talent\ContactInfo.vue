<template>
  <div class="contact-info-page">
    <a-card title="联系方式管理" class="custom-card">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="showImportModal">
            <CloudUploadOutlined />
            导入联系方式
          </a-button>
          <a-button
            v-if="importProgressData.total > 0"
            type="default"
            @click="showImportProgressModal"
          >
            <EyeOutlined />
            查看导入记录
          </a-button>
          <a-button type="default" @click="exportContacts" :loading="exportLoading">
            <ExportOutlined />
            导出联系方式
          </a-button>
          <a-button @click="refreshList">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 搜索筛选区域 -->
      <div class="search-filters">
        <a-row :gutter="[16, 16]">
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-input
              v-model:value="searchForm.关键词"
              placeholder="搜索达人昵称、联系方式、个人备注"
              allow-clear
              @pressEnter="handleSearch"
              @input="handleSearchDebounced"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </a-col>
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-select
              v-model:value="searchForm.平台"
              placeholder="选择平台"
              allow-clear
              @change="handleSearch"
            >
              <a-select-option value="抖音">抖音</a-select-option>
              <a-select-option value="微信">微信</a-select-option>
            </a-select>
          </a-col>
          <!-- 移除联系方式状态筛选器，因为此接口只返回有联系方式的记录 -->
          <a-col :xs="24" :sm="12" :md="8" :lg="6">
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 统计信息 -->
      <div class="stats-info">
        <a-row :gutter="[16, 16]">
          <a-col :xs="12" :sm="6">
            <a-statistic title="总达人数" :value="stats.总数" />
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-statistic title="有联系方式" :value="stats.有联系方式" />
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-statistic title="抖音达人" :value="stats.抖音达人" />
          </a-col>
          <a-col :xs="12" :sm="6">
            <a-statistic title="微信达人" :value="stats.微信达人" />
          </a-col>
        </a-row>
      </div>

      <!-- 联系方式列表 -->
      <div class="contact-list">


        <a-table
          :columns="columns"
          :data-source="contactList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          :row-key="record => record.补充信息id || record.关联id || `fallback_${Date.now()}_${Math.random()}`"
          :scroll="{ x: 1200 }"
        >
          <!-- 达人信息列 -->
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'talent'">
              <div class="talent-info">
                <!-- 
                  达人信息显示逻辑：
                  1. 当达人id为null或达人昵称为"未匹配达人"时，显示红色感叹号但同时显示平台账号等信息
                  2. 当达人id存在且有效时，显示正常的达人信息（头像、昵称、账号、平台标签）
                  
                  优化后的逻辑：
                  - 未关联达人也显示完整的信息卡片，但使用特殊的样式标识
                  - 保留红色感叹号作为未关联达人的标识
                  - 显示平台账号作为昵称，让用户能识别是哪个达人
                -->
                <!-- 当达人id为null或达人昵称为"未匹配达人"时，显示警告样式但包含完整信息 -->
                <template v-if="!record.达人id || record.达人昵称 === '未匹配达人'">
                  <div class="unlinked-talent-info" @click="handleBindTalent(record)">
                    <!-- 头像区域 - 未关联达人显示优雅的默认头像 -->
                    <div class="talent-avatar-container">
                      <a-avatar :size="40" class="unlinked-avatar">
                        <template #icon>
                          <UserOutlined class="unlinked-icon" />
                        </template>
                      </a-avatar>
                      <!-- 未关联标识 - 小圆点提示 -->
                      <div class="unlinked-badge">
                        <span class="badge-dot"></span>
                      </div>
                    </div>
                    
                    <!-- 达人详细信息 -->
                    <div class="talent-details unlinked-details">
                      <!-- 显示平台账号作为昵称 -->
                      <div class="talent-name unlinked-name">
                        {{ record.平台账号 || '未知账号' }}
                      </div>
                      <!-- 显示平台信息 -->
                      <div class="talent-account unlinked-account">
                        {{ record.平台 }}平台账号
                      </div>
                                             <!-- 平台标签，使用协调的颜色 -->
                       <div class="talent-tags">
                         <a-tag :color="record.平台 === '抖音' ? '#ff9c6e' : '#ff9c6e'" size="small">
                           {{ record.平台 }}
                         </a-tag>
                         <a-tag color="#ffa39e" size="small">
                           未关联
                         </a-tag>
                       </div>
                      <!-- 操作提示 -->
                      <div class="unlinked-tip">
                        点击关联达人
                      </div>
                    </div>
                  </div>
                </template>
                
                <!-- 当达人id存在且有效时，显示达人信息 -->
                <template v-else>
                  <!-- 达人头像，使用a-avatar组件 -->
                  <a-avatar :size="40" :src="record.头像">
                    <template #icon v-if="!record.头像">
                      <UserOutlined />
                    </template>
                  </a-avatar>
                  
                  <!-- 达人详细信息 -->
                  <div class="talent-details">
                    <!-- 达人昵称 -->
                    <div class="talent-name">{{ record.达人昵称 || '未知' }}</div>
                    <!-- 达人账号 -->
                    <div class="talent-account">{{ record.平台账号 || '暂无账号' }}</div>
                    <!-- 平台标签，根据平台类型显示不同颜色 -->
                    <a-tag :color="record.平台 === '抖音' ? 'blue' : 'green'" size="small">
                      {{ record.平台 }}
                    </a-tag>
                  </div>
                </template>
              </div>
            </template>

            <!-- 联系方式列 -->
            <template v-else-if="column.key === 'contact'">
              <div class="contact-info">
                <div v-if="record.联系方式" class="contact-item">
                  <a-tag color="blue">{{ record.联系方式类型 || '未知' }}</a-tag>
                  <span class="contact-value">{{ record.联系方式 }}</span>
                  <!-- 如果没有关联联系人，显示关联按钮 -->
                  <div v-if="!record.用户联系人表id" class="associate-contact-btn">
                    <a-button
                      type="link"
                      size="small"
                      @click="showAssociateContactModal(record)"
                      :icon="h(ContactsOutlined)"
                    >
                      关联联系人
                    </a-button>
                  </div>
                  <!-- 如果已关联联系人，显示关联状态 -->
                  <div v-else class="contact-linked">
                    <a-tag color="green" size="small">
                      <ContactsOutlined />
                      已关联联系人
                    </a-tag>
                  </div>
                </div>
                <div v-else class="no-contact">
                  <a-tag color="default">暂无联系方式</a-tag>
                </div>
              </div>
            </template>

            <!-- 补充信息列 -->
            <template v-else-if="column.key === 'additional'">
              <div class="additional-info-container">
                <div v-if="record.补充信息 && Object.keys(record.补充信息).length > 0" class="additional-info">
                  <!-- 显示前3个键值对 -->
                  <div class="additional-items">
                    <div
                      v-for="(value, key, index) in record.补充信息"
                      :key="key"
                      v-show="index < 3"
                      class="additional-item"
                    >
                      <a-tooltip :title="`${key}: ${value}`" placement="topLeft">
                        <a-tag color="processing" size="small" class="additional-tag">
                          <span class="tag-key">{{ truncateText(key, 8) }}</span>
                          <span class="tag-separator">:</span>
                          <span class="tag-value">{{ truncateText(value, 12) }}</span>
                        </a-tag>
                      </a-tooltip>
                    </div>
                  </div>
                  <!-- 更多指示器 -->
                  <div v-if="Object.keys(record.补充信息).length > 3" class="additional-more">
                    <a-tag color="blue" size="small" class="more-tag">
                      +{{ Object.keys(record.补充信息).length - 3 }}更多
                    </a-tag>
                  </div>
                </div>
                <span v-else class="no-additional">暂无补充信息</span>
              </div>
            </template>

            <!-- 个人标签列 -->
            <template v-else-if="column.key === 'tags'">
              <div class="tags-container">
                <a-tag
                  v-for="tag in record.个人标签"
                  :key="tag"
                  color="processing"
                  size="small"
                >
                  {{ tag }}
                </a-tag>
                <span v-if="!record.个人标签 || record.个人标签.length === 0" class="no-tags">
                  暂无标签
                </span>
              </div>
            </template>

            <!-- 个人备注列 -->
            <template v-else-if="column.key === 'note'">
              <div class="note-content">
                <a-tooltip v-if="record.个人备注" :title="record.个人备注">
                  <span class="note-text">{{ truncateText(record.个人备注, 50) }}</span>
                </a-tooltip>
                <span v-else class="no-note">暂无备注</span>
              </div>
            </template>

            <!-- 操作列 -->
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="editContact(record)">
                  <EditOutlined />
                  编辑
                </a-button>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  <EyeOutlined />
                  详情
                </a-button>
                <a-button type="link" size="small" @click="openSampleModal(record)">
                  <SendOutlined />
                  寄样
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-card>

    <!-- 编辑联系方式弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑联系方式"
      :width="600"
      :confirm-loading="editLoading"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="达人信息">
          <div class="talent-info">
            <a-avatar :size="40" :src="editForm.头像">
              <template #icon v-if="!editForm.头像">
                <UserOutlined />
              </template>
            </a-avatar>
            <div class="talent-details">
              <div class="talent-name">{{ editForm.达人昵称 }}</div>
              <div class="talent-account">{{ editForm.抖音号 }}</div>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="联系方式类型">
          <a-select v-model:value="editForm.联系方式类型" placeholder="选择联系方式类型" disabled>
            <a-select-option value="微信">微信</a-select-option>
            <a-select-option value="手机">手机</a-select-option>
            <a-select-option value="邮箱">邮箱</a-select-option>
          </a-select>
          <div class="form-help-text">
            联系方式类型一旦创建就不能修改，如需更改请删除后重新添加
          </div>
        </a-form-item>

        <a-form-item label="联系方式">
          <a-input v-model:value="editForm.联系方式" placeholder="请输入联系方式" disabled />
          <div class="form-help-text">
            联系方式一旦创建就不能修改，如需更改请删除后重新添加
          </div>
        </a-form-item>

        <a-form-item label="个人标签">
          <a-select
            v-model:value="editForm.个人标签"
            mode="tags"
            placeholder="添加个人标签"
            :max-tag-count="10"
          >
            <a-select-option value="专业">专业</a-select-option>
            <a-select-option value="回复快">回复快</a-select-option>
            <a-select-option value="配合度高">配合度高</a-select-option>
            <a-select-option value="价格合理">价格合理</a-select-option>
            <a-select-option value="长期合作">长期合作</a-select-option>
            <a-select-option value="内容优质">内容优质</a-select-option>
            <a-select-option value="粉丝活跃">粉丝活跃</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="个人备注">
          <a-textarea
            v-model:value="editForm.个人备注"
            placeholder="请输入个人备注"
            :rows="4"
            :max-length="500"
            show-count
          />
        </a-form-item>

        <a-form-item label="补充信息">
          <div class="additional-info-edit">
            <div v-if="Object.keys(editForm.补充信息).length > 0" class="additional-items-edit">
              <div
                v-for="(value, key) in editForm.补充信息"
                :key="key"
                class="additional-item-edit"
              >
                <a-input-group compact>
                  <a-input
                    :value="key"
                    @change="(e) => updateAdditionalKey(key, e.target.value)"
                    placeholder="键名"
                    style="width: 30%"
                    size="small"
                  />
                  <a-input
                    :value="value"
                    @change="(e) => updateAdditionalValue(key, e.target.value)"
                    placeholder="值"
                    style="width: 60%"
                    size="small"
                  />
                  <a-button
                    @click="removeAdditionalItem(key)"
                    size="small"
                    danger
                    style="width: 10%"
                    title="删除"
                  >
                    <DeleteOutlined />
                  </a-button>
                </a-input-group>
              </div>
            </div>
            <div class="additional-actions">
              <a-button @click="addAdditionalItem" type="dashed" size="small" block>
                <PlusOutlined /> 添加补充信息
              </a-button>
            </div>
          </div>
        </a-form-item>

        <!-- 删除操作 -->
        <a-form-item>
          <a-divider />
          <div class="delete-section">
            <a-popconfirm
              title="确定要删除这条补充信息吗？删除后将无法恢复。"
              ok-text="确定删除"
              cancel-text="取消"
              @confirm="deleteCurrentContact"
            >
              <a-button danger>
                <DeleteOutlined />
                删除这条补充信息
              </a-button>
            </a-popconfirm>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="联系方式详情"
      :width="800"
      :footer="null"
      class="detail-modal"
    >
      <div class="detail-content">
        <!-- 达人基本信息 -->
        <a-card title="达人信息" size="small" class="detail-card">
          <div class="talent-detail-info">
            <div class="talent-avatar-section">
              <a-avatar :size="80" :src="detailData.头像">
                <template #icon v-if="!detailData.头像">
                  <UserOutlined />
                </template>
              </a-avatar>
            </div>
            <div class="talent-info-section">
              <div class="info-row">
                <span class="info-label">达人昵称：</span>
                <span class="info-value">{{ detailData.达人昵称 || '未知' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">平台账号：</span>
                <span class="info-value">{{ detailData.平台账号 || '暂无账号' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">所属平台：</span>
                <a-tag :color="detailData.平台 === '抖音' ? 'blue' : 'green'">
                  {{ detailData.平台 }}
                </a-tag>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 联系方式信息 -->
        <a-card title="联系方式" size="small" class="detail-card">
          <div v-if="detailData.联系方式" class="contact-detail">
            <div class="info-row">
              <span class="info-label">联系方式：</span>
              <span class="info-value contact-value">{{ detailData.联系方式 }}</span>
            </div>
            <div class="info-row">
              <span class="info-label">联系类型：</span>
              <a-tag color="blue">{{ detailData.联系方式类型 || '未知' }}</a-tag>
            </div>
          </div>
          <div v-else class="no-contact-detail">
            <a-empty description="暂无联系方式" :image="false" />
          </div>
        </a-card>

        <!-- 补充信息 -->
        <a-card title="补充信息" size="small" class="detail-card">
          <div v-if="detailData.补充信息 && Object.keys(detailData.补充信息).length > 0" class="additional-detail">
            <div class="additional-grid">
              <div
                v-for="(value, key) in detailData.补充信息"
                :key="key"
                class="additional-detail-item"
              >
                <div class="detail-item-header">
                  <span class="detail-item-key">{{ key }}</span>
                </div>
                <div class="detail-item-content">
                  <span class="detail-item-value">{{ value }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="no-additional-detail">
            <a-empty description="暂无补充信息" :image="false" />
          </div>
        </a-card>

        <!-- 个人标签 -->
        <a-card title="个人标签" size="small" class="detail-card">
          <div v-if="detailData.个人标签 && detailData.个人标签.length > 0" class="tags-detail">
            <a-tag
              v-for="tag in detailData.个人标签"
              :key="tag"
              color="processing"
              class="detail-tag"
            >
              {{ tag }}
            </a-tag>
          </div>
          <div v-else class="no-tags-detail">
            <a-empty description="暂无个人标签" :image="false" />
          </div>
        </a-card>

        <!-- 个人备注 -->
        <a-card title="个人备注" size="small" class="detail-card">
          <div v-if="detailData.个人备注" class="note-detail">
            <p class="note-detail-text">{{ detailData.个人备注 }}</p>
          </div>
          <div v-else class="no-note-detail">
            <a-empty description="暂无个人备注" :image="false" />
          </div>
        </a-card>
      </div>
    </a-modal>

    <!-- 导入联系方式弹窗 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入联系方式"
      :width="1200"
      :body-style="{ padding: '24px' }"
      @ok="handleImportSubmit"
      @cancel="handleImportCancel"
      :confirm-loading="importLoading"
      class="import-modal"
      :footer="null"
    >
      <!-- 步骤指示器 -->
      <div class="import-steps">
        <a-steps :current="currentStep" size="small" class="mb-6">
          <a-step title="选择平台" description="选择导入数据的平台类型" />
          <a-step title="上传文件" description="上传CSV或Excel文件" />
          <a-step title="字段映射" description="配置数据字段对应关系" />
          <a-step title="预览确认" description="预览导入数据并确认" />
          <a-step title="导入完成" description="查看导入结果" />
        </a-steps>
      </div>
      <div class="import-container">
        <!-- 基础配置 -->
        <div class="import-settings">
          <a-row :gutter="20">
            <!-- 平台选择 -->
            <a-col :span="7">
              <div class="setting-card platform-card">
                <div class="card-header">
                  <UserOutlined class="card-icon" />
                  <span class="card-title">选择平台</span>
                </div>
                <div class="card-content">
                  <a-radio-group v-model:value="importForm.平台类型" class="platform-radio">
                    <a-radio-button value="抖音" class="platform-btn">抖音</a-radio-button>
                    <a-radio-button value="微信" class="platform-btn">微信</a-radio-button>
                  </a-radio-group>
                  <div class="mt-3">
                    <a-button
                      type="link"
                      size="small"
                      @click="downloadTemplate"
                      :icon="h(DownloadOutlined)"
                    >
                      下载导入模板
                    </a-button>
                  </div>
                </div>
              </div>
            </a-col>

            <!-- 文件上传 -->
            <a-col :span="17">
              <div class="setting-card upload-card">
                <div class="card-header">
                  <UploadOutlined class="card-icon" />
                  <span class="card-title">上传数据文件</span>
                  <span class="card-subtitle">支持 CSV、Excel 格式</span>
                </div>
                <div class="card-content">
                  <a-upload-dragger
                    v-model:file-list="fileList"
                    :before-upload="beforeUpload"
                    @remove="removeFile"
                    accept=".csv,.xlsx,.xls"
                    :max-count="1"
                    :show-upload-list="{ showPreviewIcon: false, showDownloadIcon: false }"
                    class="elegant-upload-dragger"
                  >
                    <div class="upload-content">
                      <InboxOutlined class="upload-icon" />
                      <div class="upload-text">点击或拖拽文件到此区域</div>
                      <div class="upload-hint">最大 10MB</div>
                    </div>
                  </a-upload-dragger>
                </div>
              </div>
            </a-col>
          </a-row>
        </div>

        <!-- 字段映射 -->
        <div class="field-mapping" v-if="importPreviewData.length > 0">
          <div class="field-mapping-header">
            <h4>信息所在列</h4>
            <p>请选择Excel中对应信息所在的列</p>
          </div>

          <!-- 联系方式列（必选） -->
          <div class="contact-field-section">
            <label class="compact-label">
              <span class="required-mark">*</span>
              联系方式列
              <span v-if="selectedContactColumns.length > 0" class="selected-count">
                (已选 {{ selectedContactColumns.length }} 列)
              </span>
            </label>
            <a-select
              v-model:value="selectedContactColumns"
              mode="multiple"
              placeholder="选择联系方式列（必选，可多选）"
              :max-tag-count="5"
              size="default"
              class="compact-select"
            >
              <a-select-option v-for="col in availableColumns" :key="col" :value="col">
                {{ col }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 其他字段映射 -->
          <div class="other-fields-section">
            <a-row :gutter="12">

              <a-col :span="8">
                <label class="compact-label">{{ platformAccountLabel }}</label>
                <a-select
                  v-model:value="fieldMapping.平台账号"
                  :placeholder="`选择${platformAccountLabel}`"
                  allow-clear
                  size="default"
                  class="compact-select"
                >
                  <a-select-option v-for="col in availableColumns" :key="col" :value="col">
                    {{ col }}
                  </a-select-option>
                </a-select>
              </a-col>

              <a-col :span="8">
                <label class="compact-label">个人备注</label>
                <a-select
                  v-model:value="fieldMapping.个人备注"
                  placeholder="选择列"
                  allow-clear
                  size="default"
                  class="compact-select"
                >
                  <a-select-option v-for="col in availableColumns" :key="col" :value="col">
                    {{ col }}
                  </a-select-option>
                </a-select>
              </a-col>

              <a-col :span="8">
                <label class="compact-label">个人标签</label>
                <a-select
                  v-model:value="fieldMapping.个人标签"
                  placeholder="选择列"
                  allow-clear
                  size="default"
                  class="compact-select"
                >
                  <a-select-option v-for="col in availableColumns" :key="col" :value="col">
                    {{ col }}
                  </a-select-option>
                </a-select>
              </a-col>
            </a-row>
          </div>

          <!-- 补充字段映射 -->
          <div class="additional-fields-section">
            <h5>补充字段</h5>
            <p class="field-description">选择要作为补充信息的列</p>

            <a-select
              v-model:value="selectedAdditionalFields"
              mode="multiple"
              placeholder="选择补充字段列"
              allow-clear
              size="default"
              style="width: 100%"
            >
              <a-select-option v-for="col in availableColumns" :key="col" :value="col">
                {{ col }}
              </a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 数据预览 -->
        <div class="data-preview" v-if="importPreviewData.length > 0">
          <h4 v-if="currentStep === 3">
            数据预览（共{{ importPreviewData.length }}行，已拆分联系方式）
          </h4>
          <h4 v-else>数据预览（前5行）</h4>

          <a-table
            :columns="previewColumns"
            :data-source="currentStep === 3 ? importPreviewData : importPreviewData.slice(0, 5)"
            :pagination="currentStep === 3 ? {
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条数据`
            } : false"
            size="small"
            :scroll="{ x: 800 }"
          />

          <div class="preview-stats" v-if="currentStep === 3">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="总行数" :value="importPreviewData.length" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="联系方式列" :value="selectedContactColumns.length" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="有效联系方式" :value="validContactCount" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="预计导入" :value="estimatedImportCount" />
              </a-col>
            </a-row>
          </div>

          <div class="preview-stats" v-else>
            <a-row :gutter="16">
              <a-col :span="24">
                <a-statistic title="总行数" :value="fullImportData.length" />
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 导入结果 -->
        <div class="import-result" v-if="importResult">
          <a-alert
            :type="importResult.失败 > 0 ? 'warning' : 'success'"
            :message="importResult.message"
            show-icon
          >
            <template #description>
              <div class="result-details">
                <p>总计: {{ importResult.总计 }}</p>
                <p>成功: {{ importResult.成功 }}</p>
                <p>失败: {{ importResult.失败 }}</p>
                <p>成功率: {{ importResult.成功率 }}</p>
                <div v-if="importResult.失败详情 && importResult.失败详情.length > 0">
                  <p>失败详情:</p>
                  <ul>
                    <li v-for="error in importResult.失败详情" :key="error">{{ error }}</li>
                  </ul>
                </div>
              </div>
            </template>
          </a-alert>
        </div>

        <!-- 步骤导航按钮 -->
        <div class="step-navigation" v-if="currentStep < 4">
          <a-row justify="space-between" class="mt-4">
            <a-col>
              <a-button v-if="currentStep > 0" @click="previousStep">
                上一步
              </a-button>
            </a-col>
            <a-col>
              <a-button
                v-if="canProceedToNextStep"
                type="primary"
                @click="nextStep"
              >
                {{ currentStep === 3 ? '开始导入' : '下一步' }}
              </a-button>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-modal>

    <!-- 导入进度弹窗 -->
    <a-modal
      v-model:open="showImportProgress"
      title="导入进度"
      width="800px"
      :footer="null"
      :closable="false"
      :maskClosable="false"
    >
      <div class="import-progress-container">
        <!-- 进度概览 -->
        <div class="progress-summary">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总计" :value="importProgressData.total" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="已处理" :value="importProgressData.processed" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="成功" :value="importProgressData.success" :value-style="{ color: '#52c41a' }" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="失败" :value="importProgressData.failure" :value-style="{ color: '#ff4d4f' }" />
            </a-col>
          </a-row>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
          <a-progress
            :percent="Math.round((importProgressData.processed / importProgressData.total) * 100)"
            :success="{ percent: Math.round((importProgressData.success / importProgressData.total) * 100) }"
            :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
          />
        </div>

        <!-- 详细记录 -->
        <div class="progress-details">
          <div class="details-header">
            <h4>导入详情</h4>
            <div class="details-stats">
              <span class="success-count">成功: {{ importProgressData.success }}</span>
              <span class="failure-count">失败: {{ importProgressData.failure }}</span>
              <span class="total-count">总计: {{ importProgressData.details.length }}</span>
            </div>
          </div>

          <!-- 详细记录列表 -->
          <div class="details-list-container">
            <div class="details-list">
              <div
                v-for="detail in importProgressData.details"
                :key="detail.index"
                class="detail-item"
                :class="detail.status"
              >
                <span class="index">{{ detail.index }}</span>
                <span class="contact">{{ detail.联系方式 }}</span>
                <span class="type">{{ detail.联系方式类型 }}</span>
                <span class="account">{{ detail.平台账号 }}</span>
                <span class="status">
                  <a-tag :color="detail.status === 'success' ? 'green' : 'red'">
                    {{ getShortMessage(detail.message) }}
                  </a-tag>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 完成按钮 -->
        <div class="progress-footer" v-if="importProgressData.processed === importProgressData.total">
          <a-space>
            <a-button
              v-if="importProgressData.failure > 0"
              type="default"
              @click="exportFailedRecords"
              :loading="exportFailedLoading"
            >
              导出失败记录
            </a-button>
            <a-button type="primary" @click="closeImportProgress">
              完成
            </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>

    <!-- 寄样申请弹窗 -->
    <SampleApplicationModal
      v-model:open="sampleModalVisible"
      :talent-info="selectedTalent"
      @success="handleSampleSuccess"
    />

    <!-- 关联达人弹窗 -->
    <a-modal
      v-model:open="bindTalentModalVisible"
      title="关联达人"
      :width="600"
      :confirm-loading="bindTalentLoading"
      @ok="handleBindTalentConfirm"
      @cancel="handleBindTalentCancel"
    >
      <div class="bind-talent-form">
        <!-- 平台账号显示 -->
        <div class="contact-info-display">
          <a-form-item label="联系方式信息">
            <div class="contact-display">
              <a-tag :color="currentContactRecord.联系方式类型 === '微信' ? 'green' : 'blue'">
                {{ currentContactRecord.联系方式类型 }}
              </a-tag>
              <span>{{ currentContactRecord.联系方式 }}</span>
            </div>
          </a-form-item>
        </div>

        <!-- 搜索输入框 -->
        <div class="search-section">
          <a-form-item label="平台账号" required>
            <a-input-search
              v-model:value="bindTalentForm.platformAccount"
              placeholder="请输入平台账号"
              size="large"
              enter-button="搜索"
              :loading="searchLoading"
              @search="handleSearchTalentForBind"
              allow-clear
            />
          </a-form-item>
          <p class="search-tip">
            <InfoCircleOutlined style="color: #1890ff; margin-right: 4px;" />
            输入达人的平台账号进行搜索，系统将自动匹配相关达人信息
          </p>
        </div>

        <!-- 搜索结果列表 -->
        <div v-if="searchResults.length > 0" class="search-results">
          <a-divider>搜索结果</a-divider>
          <div class="results-list">
            <div
              v-for="(talent, index) in searchResults"
              :key="index"
              class="talent-item"
              :class="{ 'selected': selectedSearchResult === index }"
              @click="selectSearchResult(index)"
            >
              <div class="talent-avatar">
                <a-avatar :size="48" :src="talent.头像">
                  <template #icon>
                    <UserOutlined />
                  </template>
                </a-avatar>
              </div>
              <div class="talent-info">
                <div class="talent-name">{{ talent.昵称 || talent.nickname || '未知昵称' }}</div>
                <div class="talent-account">{{ talent.抖音号 || talent.account_douyin || talent.微信号 || '账号未知' }}</div>
                <div class="talent-stats">
                  <a-tag color="blue">
                    粉丝: {{ talent.粉丝数 ? formatNumber(talent.粉丝数) : (talent.follower_count ? formatNumber(talent.follower_count) : '未知') }}
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索结果为空时的提示 -->
        <div v-else-if="searchExecuted && searchResults.length === 0" class="empty-results">
          <a-empty description="未找到匹配的达人" />
        </div>
      </div>
    </a-modal>

    <!-- 关联联系人弹窗 -->
    <a-modal
      v-model:open="associateContactModalVisible"
      title="关联联系人"
      width="600px"
      :footer="null"
      @cancel="handleAssociateContactCancel"
    >
      <div class="associate-contact-content">
        <!-- 联系方式信息展示 -->
        <div class="contact-info-display">
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="联系方式">
              {{ associateContactForm.联系方式 }}
            </a-descriptions-item>
            <a-descriptions-item label="类型">
              {{ associateContactForm.联系方式类型 }}
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 选项卡 -->
        <a-tabs v-model:activeKey="associateTabActiveKey" class="associate-tabs">
          <!-- 关联已有联系人 -->
          <a-tab-pane key="existing" tab="关联已有联系人">
            <div class="existing-contact-tab">
              <!-- 搜索框 -->
              <a-input-search
                v-model:value="contactSearchKeyword"
                placeholder="搜索联系人姓名或联系方式"
                @search="searchExistingContacts"
                @change="onContactSearchChange"
                style="margin-bottom: 16px"
              />

              <!-- 联系人列表 -->
              <div class="contact-list-container">
                <a-spin :spinning="existingContactsLoading">
                  <div v-if="existingContacts.length > 0" class="contact-list">
                    <div
                      v-for="contact in filteredExistingContacts"
                      :key="contact.用户联系人id"
                      class="contact-item"
                      :class="{ active: selectedExistingContactId === contact.用户联系人id }"
                      @click="selectExistingContact(contact)"
                    >
                      <div class="contact-name">{{ contact.姓名 }}</div>
                      <div class="contact-id">ID: {{ contact.用户联系人id.substring(0, 8) }}...</div>
                      <div class="contact-methods" v-if="hasContactMethods(contact.关联联系方式列表)">
                        <span class="contact-methods-label">联系方式:</span>
                        <span class="contact-methods-list">
                          {{ formatContactMethods(contact.关联联系方式列表) }}
                        </span>
                      </div>
                      <div class="contact-no-methods" v-else>
                        <span class="no-methods-text">暂无关联联系方式</span>
                      </div>
                    </div>
                  </div>
                  <a-empty v-else description="暂无联系人" />
                </a-spin>
              </div>

              <!-- 关联按钮 -->
              <div class="tab-actions">
                <a-button
                  type="primary"
                  :disabled="!selectedExistingContactId"
                  :loading="associateExistingLoading"
                  @click="handleAssociateExisting"
                >
                  关联选中联系人
                </a-button>
              </div>
            </div>
          </a-tab-pane>

          <!-- 创建新联系人 -->
          <a-tab-pane key="new" tab="创建新联系人">
            <div class="new-contact-tab">
              <a-form
                :model="newContactForm"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18 }"
              >
                <a-form-item label="联系人姓名" required>
                  <a-input
                    v-model:value="newContactForm.姓名"
                    placeholder="请输入联系人姓名"
                    :maxlength="50"
                  />
                </a-form-item>
              </a-form>

              <!-- 创建并关联按钮 -->
              <div class="tab-actions">
                <a-button
                  type="primary"
                  :disabled="!newContactForm.姓名.trim()"
                  :loading="createAndAssociateLoading"
                  @click="handleCreateAndAssociate"
                >
                  创建并关联
                </a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { contactApi, userContactApi } from '@/api/contact'
import SampleApplicationModal from '@/components/talent/SampleApplicationModal.vue'
import { exportToExcel } from '@/utils/export'
import {
    CloudUploadOutlined,
    ContactsOutlined,
    DeleteOutlined,
    DownloadOutlined,
    EditOutlined,
    ExportOutlined,
    EyeOutlined,
    InboxOutlined,
    InfoCircleOutlined,
    PlusOutlined,
    ReloadOutlined,
    SearchOutlined,
    SendOutlined,
    UploadOutlined,
    UserOutlined
} from '@ant-design/icons-vue'
import { message, notification } from 'ant-design-vue'
import { computed, h, onMounted, reactive, ref } from 'vue'
import * as XLSX from 'xlsx'

defineOptions({
  name: 'ContactInfo'
})

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const editLoading = ref(false)
const importLoading = ref(false)
const contactList = ref([])
const editModalVisible = ref(false)
const importModalVisible = ref(false)
const editFormRef = ref()

// 寄样相关数据
const sampleModalVisible = ref(false)
const selectedTalent = ref({})

// 关联达人相关数据
const bindTalentModalVisible = ref(false)
const bindTalentLoading = ref(false)
const searchLoading = ref(false)
const searchExecuted = ref(false)
const searchResults = ref([])
const selectedSearchResult = ref(null)
const currentContactRecord = ref({})
const bindTalentForm = reactive({
  platformAccount: ''
})

// 导入相关数据
const fileList = ref([])
const importPreviewData = ref([])
const fullImportData = ref([]) // 完整导入数据
const availableColumns = ref([])
const importResult = ref(null)
const currentStep = ref(0) // 当前步骤
const dataQualityIssues = ref([]) // 数据质量问题

// 搜索表单
const searchForm = reactive({
  关键词: '',
  平台: undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
})

// 编辑表单
const editForm = reactive({
  达人id: null,
  补充信息id: null,
  关联id: null,
  达人昵称: '',
  抖音号: '',
  头像: '',
  联系方式: '',
  联系方式类型: '',
  个人备注: '',
  个人标签: [],
  补充信息: {}
})

// 详情弹窗状态
const detailModalVisible = ref(false)
const detailData = ref({})

// 关联联系人弹窗状态
const associateContactModalVisible = ref(false)
const associateTabActiveKey = ref('existing')
const associateContactForm = reactive({
  补充信息id: null,
  联系方式: '',
  联系方式类型: ''
})

// 关联已有联系人相关状态
const existingContactsLoading = ref(false)
const existingContacts = ref([])
const contactSearchKeyword = ref('')
const selectedExistingContactId = ref(null)
const associateExistingLoading = ref(false)

// 联系人搜索防抖相关
let contactSearchTimeout = null

// 创建新联系人相关状态
const newContactForm = reactive({
  姓名: ''
})
const createAndAssociateLoading = ref(false)

// 导入表单
const importForm = reactive({
  平台类型: '抖音'
})

// 字段映射
const fieldMapping = reactive({
  平台账号: '',
  联系方式: '',
  个人备注: '',
  个人标签: ''
})

// 补充字段多选
const selectedAdditionalFields = ref([])

// 导入进度相关
const showImportProgress = ref(false)
const exportFailedLoading = ref(false)
const importProgressData = ref({
  total: 0,
  processed: 0,
  success: 0,
  failure: 0,
  details: []
})

// 联系方式多选列
const selectedContactColumns = ref([])

// 平台账号标签
const platformAccountLabel = computed(() => {
  return importForm.平台类型 === '抖音' ? '抖音号' : '微信小店账号'
})

// 统计信息
const stats = computed(() => {
  const total = contactList.value.length
  // 由于此接口只返回有联系方式的记录，所以有联系方式的数量等于总数
  const hasContact = total
  const douyin = contactList.value.filter(item => item.平台 === '抖音').length
  const wechat = contactList.value.filter(item => item.平台 === '微信').length

  return {
    总数: total,
    有联系方式: hasContact,
    抖音达人: douyin,
    微信达人: wechat
  }
})

// 有效数据统计
const validDataCount = computed(() => {
  if (!importPreviewData.value.length || selectedContactColumns.value.length === 0) return 0

  let totalValidRecords = 0

  importPreviewData.value.forEach(row => {
    selectedContactColumns.value.forEach(contactColumn => {
      const 联系方式 = row[contactColumn]
      if (联系方式 && String(联系方式).trim() !== '') {
        totalValidRecords++
      }
    })
  })

  return totalValidRecords
})

// 有效联系方式数量
const validContactCount = computed(() => {
  if (!importPreviewData.value.length) return 0

  return importPreviewData.value.filter(row =>
    row.联系方式类型 && row.联系方式类型 !== '未知' && row.联系方式类型 !== '无效'
  ).length
})

// 预计导入数量
const estimatedImportCount = computed(() => {
  return validContactCount.value
})

// 预览表格列
const previewColumns = computed(() => {
  if (currentStep.value !== 3) {
    // 非预览步骤，显示原始数据列
    if (!availableColumns.value.length) return []
    return availableColumns.value.map(col => ({
      title: col,
      dataIndex: col,
      key: col,
      width: 150,
      ellipsis: true
    }))
  }

  // 预览步骤，显示拆分后的数据结构
  const columns = [
    { title: '序号', dataIndex: '序号', key: '序号', width: 80 },
    {
      title: '联系方式',
      dataIndex: '联系方式',
      key: '联系方式',
      width: 180,
      ellipsis: true
    },
    {
      title: '类型',
      dataIndex: '联系方式类型',
      key: '联系方式类型',
      width: 100,
      customRender: ({ text }) => {
        const colorMap = {
          '手机号': 'blue',
          '邮箱': 'green',
          '微信号': 'orange',
          '未知': 'default',
          '无效': 'red'
        }
        return h('a-tag', { color: colorMap[text] || 'default' }, text)
      }
    }
  ]

  // 添加其他字段列
  if (fieldMapping.平台账号) {
    columns.push({
      title: platformAccountLabel.value,
      dataIndex: platformAccountLabel.value,
      key: platformAccountLabel.value,
      width: 150,
      ellipsis: true
    })
  }

  if (fieldMapping.个人备注) {
    columns.push({
      title: '个人备注',
      dataIndex: '个人备注',
      key: '个人备注',
      width: 200,
      ellipsis: true
    })
  }

  if (fieldMapping.个人标签) {
    columns.push({
      title: '个人标签',
      dataIndex: '个人标签',
      key: '个人标签',
      width: 150,
      ellipsis: true
    })
  }

  // 添加补充字段列
  selectedAdditionalFields.value.forEach(fieldName => {
    columns.push({
      title: fieldName,
      dataIndex: fieldName,
      key: fieldName,
      width: 150,
      ellipsis: true
    })
  })

  return columns
})

// 表格列配置
const columns = [
  {
    title: '达人信息',
    key: 'talent',
    width: 200,
    fixed: 'left'
  },
  {
    title: '联系方式',
    key: 'contact',
    width: 180
  },
  {
    title: '补充信息',
    key: 'additional',
    width: 200
  },
  {
    title: '个人标签',
    key: 'tags',
    width: 200
  },
  {
    title: '个人备注',
    key: 'note',
    width: 200
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 方法
const loadContactList = async () => {
  loading.value = true
  try {
    const params = {
      页码: pagination.current,
      每页数量: pagination.pageSize,
      ...searchForm
    }
    
    const response = await contactApi.getContactList(params)
    if (response.状态码 === 100) {
      contactList.value = response.数据.列表
      pagination.total = response.数据.总数
    } else {
      message.error(response.消息 || '获取联系方式列表失败')
    }
  } catch (error) {
    console.error('获取联系方式列表失败:', error)
    message.error('获取联系方式列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadContactList()
}

// 防抖搜索函数 - 优化搜索响应时间
let searchTimeout = null
const handleSearchDebounced = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    handleSearch()
  }, 300) // 减少防抖时间，提高搜索响应速度
}

const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadContactList()
}

const refreshList = () => {
  loadContactList()
}

const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

// 关联达人相关方法

/**
 * 处理关联达人的点击事件
 * 当用户点击红色感叹号时，打开关联达人弹窗
 * @param {Object} record - 当前联系方式记录
 */
const handleBindTalent = (record) => {
  // 保存当前联系方式记录，用于后续关联操作
  currentContactRecord.value = record
  
  // 打开关联达人弹窗
  bindTalentModalVisible.value = true
  
  // 重置表单和搜索结果
  // 如果记录中有平台账号，则预填入搜索框
  bindTalentForm.platformAccount = record.平台账号 || ''
  searchResults.value = []
  selectedSearchResult.value = null
  searchExecuted.value = false
}

/**
 * 处理关联达人弹窗的取消操作
 * 关闭弹窗并重置所有相关数据
 */
const handleBindTalentCancel = () => {
  // 关闭关联达人弹窗
  bindTalentModalVisible.value = false
  
  // 重置表单和搜索结果，清空所有输入和选择
  bindTalentForm.platformAccount = ''
  searchResults.value = []
  selectedSearchResult.value = null
  searchExecuted.value = false
  currentContactRecord.value = {}
}

/**
 * 处理关联达人弹窗的确认操作
 * 将选中的达人与当前联系方式进行关联
 */
const handleBindTalentConfirm = async () => {
  // 验证用户是否已选择达人
  if (selectedSearchResult.value === null) {
    message.warning('请先搜索并选择一个达人')
    return
  }

  // 获取用户选择的达人信息
  const selectedTalent = searchResults.value[selectedSearchResult.value]

  // 开始关联操作，显示加载状态
  bindTalentLoading.value = true

  try {
    // 调用后端关联达人接口，传递必要的参数
    const response = await contactApi.bindTalent({
      补充信息id: currentContactRecord.value.补充信息id,
      平台账号: bindTalentForm.platformAccount.trim(),
      达人UID: selectedTalent.UID || selectedTalent.uid
    })

    // 处理接口响应
    if (response.状态码 === 100) {
      message.success('关联达人成功！')
      // 关闭弹窗并重置数据
      handleBindTalentCancel()
      // 刷新联系方式列表，显示最新的关联状态
      await loadContactList()
    } else {
      message.error(response.消息 || '关联达人失败')
    }
  } catch (error) {
    console.error('关联达人失败:', error)
    message.error('关联达人失败，请重试')
  } finally {
    // 无论成功还是失败，都要停止加载状态
    bindTalentLoading.value = false
  }
}

const handleSearchTalentForBind = async () => {
  if (!bindTalentForm.platformAccount.trim()) {
    message.warning('请输入平台账号')
    return
  }

  searchLoading.value = true
  searchExecuted.value = true

  try {
    // 调用后端搜索达人接口
    const response = await contactApi.searchTalent({
      抖音号: bindTalentForm.platformAccount.trim()
    })

    if (response.状态码 === 100) {
      const data = response.数据

      // 适配新的API返回格式
      if (data.达人信息) {
        // 有达人信息（单个达人或已存在）
        searchResults.value = [data.达人信息]
        selectedSearchResult.value = 0
        
        if (data.已存在) {
          message.success('找到已存在的达人')
        } else {
          message.success('找到匹配的达人')
        }
      } else if (data.达人列表) {
        // 多个达人列表
        searchResults.value = data.达人列表
        
        if (data.需要确认 && data.达人列表.length > 1) {
          selectedSearchResult.value = null
          message.success(`找到 ${data.达人列表.length} 个相关达人，请选择`)
        } else if (data.达人列表.length === 0) {
          selectedSearchResult.value = null
          message.info(`未找到与"${bindTalentForm.platformAccount}"相关的达人，请检查平台账号是否正确`)
        } else {
          selectedSearchResult.value = 0
          message.success('找到匹配的达人')
        }
      } else {
        // 无结果
        searchResults.value = []
        selectedSearchResult.value = null
        message.info(`未找到与"${bindTalentForm.platformAccount}"相关的达人，请检查平台账号是否正确`)
      }
    } else {
      searchResults.value = []
      selectedSearchResult.value = null
      message.error(response.消息 || '搜索失败，请重试')
    }
  } catch (error) {
    console.error('搜索达人失败:', error)
    searchResults.value = []
    selectedSearchResult.value = null
    message.error('搜索失败，请检查网络连接后重试')
  } finally {
    searchLoading.value = false
  }
}

/**
 * 选择搜索结果中的达人
 * 用户点击达人列表中的某个达人时调用
 * @param {number} index - 选中的达人在搜索结果中的索引
 */
const selectSearchResult = (index) => {
  selectedSearchResult.value = index
}

/**
 * 格式化数字显示
 * 将数字转换为带千位分隔符的格式
 * @param {number} num - 要格式化的数字
 * @returns {string} 格式化后的数字字符串
 */
const formatNumber = (num) => {
  if (!num) return '0'
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const editContact = (record) => {
  Object.assign(editForm, {
    达人id: record.达人id,
    补充信息id: record.补充信息id,
    关联id: record.关联id,
    达人昵称: record.达人昵称,
    抖音号: record.抖音号,
    头像: record.头像,
    联系方式: record.联系方式 || '',
    联系方式类型: record.联系方式类型 || '',
    个人备注: record.个人备注 || '',
    个人标签: record.个人标签 || [],
    补充信息: record.补充信息 ? { ...record.补充信息 } : {}
  })
  editModalVisible.value = true
}

const handleEditSubmit = async () => {
  editLoading.value = true
  try {
    const params = {
      补充信息id: editForm.补充信息id,
      个人备注: editForm.个人备注,
      个人标签: editForm.个人标签,
      补充信息: editForm.补充信息
    }
    
    const response = await contactApi.updateContact(params)
    if (response.状态码 === 100) {
      message.success('联系方式更新成功')
      editModalVisible.value = false
      loadContactList()
    } else {
      message.error(response.消息 || '更新联系方式失败')
    }
  } catch (error) {
    console.error('更新联系方式失败:', error)
    message.error('更新联系方式失败')
  } finally {
    editLoading.value = false
  }
}

const handleEditCancel = () => {
  editModalVisible.value = false
  Object.assign(editForm, {
    达人id: null,
    补充信息id: null,
    关联id: null,
    达人昵称: '',
    抖音号: '',
    头像: '',
    联系方式: '',
    联系方式类型: '',
    个人备注: '',
    个人标签: [],
    补充信息: {}
  })
}

const viewDetail = (record) => {
  detailData.value = { ...record }
  detailModalVisible.value = true
}

// 寄样相关方法
const openSampleModal = (record) => {
  // 构建达人信息对象
  selectedTalent.value = {
    id: record.达人id || record.关联id,
    nickname: record.达人昵称 || record.昵称,
    platform: record.平台,
    follower_count: record.粉丝数,
    contact: record.联系方式,
    avatar: record.头像,
    uid: record.达人UID || record.UID
  }

  sampleModalVisible.value = true
}

const handleSampleSuccess = () => {
  message.success('寄样申请提交成功！')
  // 可以在这里添加其他成功后的处理逻辑，比如刷新列表等
}

const deleteCurrentContact = async () => {
  try {
    const response = await contactApi.deleteContact({
      补充信息id: editForm.补充信息id
    })
    if (response.状态码 === 100) {
      message.success('补充信息删除成功')
      editModalVisible.value = false
      loadContactList()
    } else {
      message.error(response.消息 || '删除补充信息失败')
    }
  } catch (error) {
    console.error('删除补充信息失败:', error)
    message.error('删除补充信息失败')
  }
}

// 补充信息编辑相关方法
const addAdditionalItem = () => {
  const newKey = `新字段${Object.keys(editForm.补充信息).length + 1}`
  editForm.补充信息[newKey] = ''
}

const removeAdditionalItem = (key) => {
  delete editForm.补充信息[key]
}

const updateAdditionalKey = (oldKey, newKey) => {
  if (newKey && newKey !== oldKey && !editForm.补充信息.hasOwnProperty(newKey)) {
    const value = editForm.补充信息[oldKey]
    delete editForm.补充信息[oldKey]
    editForm.补充信息[newKey] = value
  }
}

const updateAdditionalValue = (key, value) => {
  editForm.补充信息[key] = value
}

const exportContacts = async () => {
  exportLoading.value = true
  try {
    // 获取所有数据用于导出
    const params = {
      页码: 1,
      每页数量: 10000, // 获取所有数据
      ...searchForm
    }

    const response = await contactApi.getContactList(params)
    if (response.状态码 === 100) {
      const exportData = response.数据.列表.map(item => ({
        '达人昵称': item.达人昵称 || '',
        '抖音号': item.抖音号 || '',
        '平台': item.平台 || '',
        '联系方式类型': item.联系方式类型 || '',
        '联系方式': item.联系方式 || '',
        '个人标签': (item.个人标签 || []).join(', '),
        '个人备注': item.个人备注 || '',
        '认领时间': item.认领时间 ? new Date(item.认领时间).toLocaleString() : '',
        '更新时间': item.联系方式更新时间 ? new Date(item.联系方式更新时间).toLocaleString() : ''
      }))

      exportToExcel(exportData, '达人联系方式列表')
      message.success('导出成功')
    } else {
      message.error(response.消息 || '导出失败')
    }
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

// 显示关联联系人弹窗
const showAssociateContactModal = async (record) => {
  associateContactForm.补充信息id = record.补充信息id
  associateContactForm.联系方式 = record.联系方式
  associateContactForm.联系方式类型 = record.联系方式类型

  // 重置状态
  associateTabActiveKey.value = 'existing'
  selectedExistingContactId.value = null
  contactSearchKeyword.value = ''
  newContactForm.姓名 = ''

  // 加载已有联系人列表
  await loadExistingContacts()

  associateContactModalVisible.value = true
}

// 加载已有联系人列表
const loadExistingContacts = async (关键词 = '') => {
  existingContactsLoading.value = true
  try {
    const response = await userContactApi.getContactList({ 关键词 })
    if (response.状态码 === 100) {
      existingContacts.value = response.数据 || []
    } else {
      message.error(response.消息 || '获取联系人列表失败')
      existingContacts.value = []
    }
  } catch (error) {
    console.error('获取联系人列表失败:', error)
    message.error('获取联系人列表失败')
    existingContacts.value = []
  } finally {
    existingContactsLoading.value = false
  }
}

// 使用后端搜索，不需要前端过滤
const filteredExistingContacts = computed(() => {
  return existingContacts.value
})

// 搜索联系人（立即搜索）
const searchExistingContacts = async () => {
  // 清除防抖定时器，立即搜索
  if (contactSearchTimeout) {
    clearTimeout(contactSearchTimeout)
    contactSearchTimeout = null
  }
  await loadExistingContacts(contactSearchKeyword.value)
  selectedExistingContactId.value = null
}

// 搜索输入变化（防抖搜索）
const onContactSearchChange = () => {
  selectedExistingContactId.value = null

  // 清除之前的定时器
  if (contactSearchTimeout) {
    clearTimeout(contactSearchTimeout)
  }

  // 设置新的防抖定时器
  contactSearchTimeout = setTimeout(async () => {
    await loadExistingContacts(contactSearchKeyword.value)
  }, 500) // 500ms延迟
}

// 格式化联系方式显示
const formatContactMethods = (contactMethods) => {
  // 处理不同的数据类型
  let methods = contactMethods

  // 如果是字符串，尝试解析为JSON
  if (typeof contactMethods === 'string') {
    try {
      methods = JSON.parse(contactMethods)
    } catch (error) {
      console.warn('解析联系方式JSON失败:', error)
      return '联系方式格式错误'
    }
  }

  // 检查是否为数组
  if (!Array.isArray(methods)) {
    console.warn('联系方式数据不是数组:', methods)
    return '暂无联系方式'
  }

  // 检查数组是否为空
  if (methods.length === 0) {
    return '暂无联系方式'
  }

  return methods.map(method => `${method.联系方式} (${method.联系方式类型})`).join(', ')
}

// 检查是否有联系方式
const hasContactMethods = (contactMethods) => {
  if (!contactMethods) return false

  // 如果是字符串，尝试解析
  if (typeof contactMethods === 'string') {
    try {
      const methods = JSON.parse(contactMethods)
      return Array.isArray(methods) && methods.length > 0
    } catch (error) {
      return false
    }
  }

  // 如果是数组，检查长度
  return Array.isArray(contactMethods) && contactMethods.length > 0
}

// 选择已有联系人
const selectExistingContact = (contact) => {
  selectedExistingContactId.value = contact.用户联系人id
}

// 关联已有联系人
const handleAssociateExisting = async () => {
  if (!selectedExistingContactId.value) {
    message.error('请选择要关联的联系人')
    return
  }

  associateExistingLoading.value = true
  try {
    const response = await userContactApi.associateContact({
      补充信息id: associateContactForm.补充信息id,
      用户联系人id: selectedExistingContactId.value
    })

    if (response.状态码 === 100) {
      message.success('关联联系人成功')
      associateContactModalVisible.value = false
      // 刷新列表
      loadContactList()
    } else {
      message.error(response.消息 || '关联联系人失败')
    }
  } catch (error) {
    console.error('关联联系人失败:', error)
    message.error('关联联系人失败')
  } finally {
    associateExistingLoading.value = false
  }
}

// 创建新联系人并关联
const handleCreateAndAssociate = async () => {
  if (!newContactForm.姓名.trim()) {
    message.error('请输入联系人姓名')
    return
  }

  createAndAssociateLoading.value = true
  try {
    const response = await userContactApi.createAndAssociateContact({
      姓名: newContactForm.姓名.trim(),
      补充信息id: associateContactForm.补充信息id
    })

    if (response.状态码 === 100) {
      message.success('创建联系人并关联成功')
      associateContactModalVisible.value = false
      // 刷新列表
      loadContactList()
    } else {
      message.error(response.消息 || '创建联系人并关联失败')
    }
  } catch (error) {
    console.error('创建联系人并关联失败:', error)
    message.error('创建联系人并关联失败')
  } finally {
    createAndAssociateLoading.value = false
  }
}

// 取消关联联系人
const handleAssociateContactCancel = () => {
  // 清除搜索防抖定时器
  if (contactSearchTimeout) {
    clearTimeout(contactSearchTimeout)
    contactSearchTimeout = null
  }

  associateContactModalVisible.value = false
  associateContactForm.补充信息id = null
  associateContactForm.联系方式 = ''
  associateContactForm.联系方式类型 = ''
  selectedExistingContactId.value = null
  contactSearchKeyword.value = ''
  newContactForm.姓名 = ''
}

// 导入相关方法
const showImportModal = () => {
  importModalVisible.value = true
  // 重置导入状态
  fileList.value = []
  importPreviewData.value = []
  availableColumns.value = []
  importResult.value = null
  Object.assign(fieldMapping, {
    平台账号: '',
    联系方式: '',
    个人备注: '',
    个人标签: ''
  })
}

const beforeUpload = (file) => {
  const isValidType = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                       'application/vnd.ms-excel',
                       'text/csv'].includes(file.type)

  if (!isValidType) {
    message.error('只支持 Excel 和 CSV 文件格式')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB')
    return false
  }

  // 解析文件
  parseFile(file)
  return false // 阻止自动上传
}

const removeFile = () => {
  importPreviewData.value = []
  availableColumns.value = []
  importResult.value = null
}

const parseFile = async (file) => {
  try {
    const fileName = file.name.toLowerCase()
    let data = []

    // 文件大小检查
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      message.error(`文件大小超过限制（${formatFileSize(maxSize)}），请选择较小的文件`)
      return
    }

    if (fileName.endsWith('.csv')) {
      // 解析CSV文件
      const text = await readFileAsText(file)
      data = parseCSV(text)

    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
      // 解析Excel文件
      data = await parseExcel(file)

    } else {
      message.error({
        content: '不支持的文件格式',
        description: '请上传CSV (.csv) 或 Excel (.xlsx/.xls) 格式的文件',
        duration: 5
      })
      return
    }

    if (data.length === 0) {
      message.error({
        content: '文件内容为空',
        description: '请确保文件包含有效数据且格式正确',
        duration: 5
      })
      return
    }

    // 数据质量检查
    const qualityCheck = validateDataQuality(data)
    if (!qualityCheck.isValid) {
      notification.warning({
        message: '数据质量提醒',
        description: `发现以下问题：${qualityCheck.message}。建议检查数据后重新上传，或继续导入（系统会自动跳过问题数据）。`,
        duration: 10,
        placement: 'topRight'
      })
    }

    // 获取表头作为可用列
    const headers = Object.keys(data[0])
    availableColumns.value = headers

    // 设置预览数据（前10行）
    importPreviewData.value = data.slice(0, 10)

    // 保存完整数据
    fullImportData.value = data

    // 应用智能字段映射
    autoMapFields()

    // 进入字段映射步骤
    currentStep.value = 2

    message.success({
      content: `文件解析成功`,
      description: `共解析 ${data.length} 行数据，已自动匹配字段`,
      duration: 3
    })

  } catch (error) {
    console.error('文件解析失败:', error)
    const errorMessage = getDetailedErrorMessage(error)
    message.error({
      content: '文件解析失败',
      description: errorMessage,
      duration: 8
    })
  }
}

const readFileAsBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      // 移除Base64前缀
      const base64 = e.target.result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

const readFileAsText = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => resolve(e.target.result)
    reader.onerror = reject
    reader.readAsText(file, 'UTF-8')
  })
}

const parseCSV = (text) => {
  const lines = text.split('\n').filter(line => line.trim())
  if (lines.length < 2) return []

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  const data = []

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''))
    const row = {}
    headers.forEach((header, index) => {
      row[header] = values[index] || ''
    })
    data.push(row)
  }

  return data
}

const parseExcel = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })

        // 获取第一个工作表
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]

        // 转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length < 2) {
          reject(new Error('Excel文件内容为空或只有表头'))
          return
        }

        // 获取表头
        const headers = jsonData[0].map(h => String(h || '').trim())
        const parsedData = []

        // 转换数据行
        for (let i = 1; i < jsonData.length; i++) {
          const row = {}
          headers.forEach((header, index) => {
            row[header] = String(jsonData[i][index] || '').trim()
          })
          // 过滤空行
          if (Object.values(row).some(value => value !== '')) {
            parsedData.push(row)
          }
        }

        resolve(parsedData)

      } catch (error) {
        console.error('Excel解析错误:', error)
        reject(new Error('Excel文件解析失败，请检查文件格式'))
      }
    }

    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }

    reader.readAsArrayBuffer(file)
  })
}

const autoMapFields = () => {
  const columns = availableColumns.value
  const platform = importForm.平台类型

  // 根据平台类型定义字段映射规则
  const getFieldMappings = (platformType) => {
    const baseMapping = {
      联系方式: ['联系方式', '手机号', '微信号', '电话', '微信', 'contact', 'phone', 'wechat', 'mobile'],
      个人备注: ['个人备注', '备注', '说明', 'note', 'remark'],
      个人标签: ['个人标签', '标签', '分类', 'tags', 'category']
    }

    if (platformType === '抖音') {
      baseMapping.平台账号 = ['抖音号', '平台账号', '账号', 'douyin', 'account', 'username']
    } else if (platformType === '微信') {
      baseMapping.平台账号 = ['微信小店账号', '平台账号', '账号', 'account', 'username']
    } else {
      baseMapping.平台账号 = ['平台账号', '抖音号', '账号', 'account', 'username']
    }

    return baseMapping
  }

  const fieldMappings = getFieldMappings(platform)

  // 重置字段映射
  Object.keys(fieldMapping).forEach(key => {
    fieldMapping[key] = ''
  })
  selectedContactColumns.value = []

  // 智能匹配字段
  Object.keys(fieldMappings).forEach(field => {
    const possibleNames = fieldMappings[field]

    if (field === '联系方式') {
      // 排除的关键词（状态字段，不是实际联系方式）
      const 排除关键词 = ['是否有', '有无', '是否', '状态', 'status', 'has', 'is']

      // 联系方式支持多选，匹配所有符合条件的列
      const matchedColumns = columns.filter(col => {
        const colLower = col.toLowerCase()

        // 检查是否包含排除关键词
        const 包含排除关键词 = 排除关键词.some(excludeWord =>
          colLower.includes(excludeWord.toLowerCase())
        )

        if (包含排除关键词) {
          return false // 排除状态字段
        }

        return possibleNames.some(name => {
          const nameLower = name.toLowerCase()
          return colLower.includes(nameLower) || nameLower.includes(colLower)
        })
      })
      selectedContactColumns.value = matchedColumns
      // 保持原有的单选逻辑，选择第一个匹配的列
      if (matchedColumns.length > 0) {
        fieldMapping[field] = matchedColumns[0]
      }
    } else {
      // 其他字段保持单选
      const matchedColumn = columns.find(col => {
        const colLower = col.toLowerCase()
        return possibleNames.some(name => {
          const nameLower = name.toLowerCase()
          return colLower.includes(nameLower) || nameLower.includes(colLower)
        })
      })

      if (matchedColumn) {
        fieldMapping[field] = matchedColumn
      }
    }
  })
}

// 新的文件上传导入方式
const handleImportSubmit = async () => {
  if (!fileList.value.length) {
    message.error('请先选择文件')
    return
  }

  if (!selectedContactColumns.value.length) {
    message.error('请至少选择一个联系方式字段')
    return
  }

  importLoading.value = true
  const progressKey = 'import-progress'

  try {
    // 显示进度提示
    message.loading({ content: '正在处理导入数据...', key: progressKey, duration: 0 })

    // 初始化进度数据
    importProgressData.value = {
      total: 0,
      processed: 0,
      success: 0,
      failure: 0,
      details: []
    }

    // 构建导入数据
    const importDataList = []

    // 遍历预览数据，构建导入数据
    importPreviewData.value.forEach(row => {
      // 预览数据中联系方式已经处理好了，直接使用
      const 联系方式值 = row['联系方式'] || ''
      if (联系方式值.trim() !== '') {
        const item = {
          平台账号: row[platformAccountLabel.value] || '',
          联系方式: 联系方式值.trim(),
          个人备注: row['个人备注'] || '',
          个人标签: row['个人标签'] ?
            row['个人标签'].split(',').map(tag => tag.trim()).filter(tag => tag) :
            [],
          补充信息JSON: row['补充信息JSON'] || ''
        }

        importDataList.push(item)
      }
    })

    if (importDataList.length === 0) {
      message.error({ content: '没有有效的导入数据', key: progressKey })
      return
    }

    // 更新总数
    importProgressData.value.total = importDataList.length

    // 显示进度弹窗
    showImportProgress.value = true
    message.destroy(progressKey)

    // 分批处理数据
    const batchSize = 50
    const batches = []
    for (let i = 0; i < importDataList.length; i += batchSize) {
      batches.push(importDataList.slice(i, i + batchSize))
    }

    console.log(`开始导入 ${importDataList.length} 条数据，分 ${batches.length} 批处理`)

    // 逐批处理
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex]

      try {
        const batchResponse = await contactApi.importContacts({
          平台类型: importForm.平台类型,
          导入数据: batch
        })

        console.log(`第 ${batchIndex + 1} 批导入响应:`, batchResponse)

        // 处理批次响应 - 注意响应拦截器转换了字段名
        const batchData = batchResponse.数据 || {}

        // 更新进度
        importProgressData.value.processed += batch.length
        importProgressData.value.success += batchData.成功 || 0
        importProgressData.value.failure += batchData.失败 || 0

        // 优先使用后端返回的详细结果
        if (batchData.详细结果 && Array.isArray(batchData.详细结果)) {
          console.log('添加后端详细结果:', batchData.详细结果)
          importProgressData.value.details.push(...batchData.详细结果)
          console.log('当前详细结果总数:', importProgressData.value.details.length)
        } else {
          console.log('没有后端详细结果，生成默认结果')
          // 如果没有详细结果，根据响应状态生成默认结果
          batch.forEach((item, index) => {
            importProgressData.value.details.push({
              index: importProgressData.value.details.length + 1,
              联系方式: item.联系方式,
              联系方式类型: '',
              平台账号: item.平台账号,
              status: batchResponse.状态码 === 100 ? 'success' : 'error',
              message: batchResponse.状态码 === 100 ? '导入成功' : (batchResponse.消息 || '批次导入失败')
            })
          })
        }

      } catch (batchError) {
        console.error(`第 ${batchIndex + 1} 批导入失败:`, batchError)

        // 批次异常，记录失败信息
        importProgressData.value.processed += batch.length
        importProgressData.value.failure += batch.length

        batch.forEach((item, index) => {
          importProgressData.value.details.push({
            index: importProgressData.value.details.length + 1,
            联系方式: item.联系方式,
            联系方式类型: '',
            平台账号: item.平台账号,
            status: 'error',
            message: batchError.message || '网络错误'
          })
        })
      }
    }

    // 显示最终结果
    const successCount = importProgressData.value.success
    const failureCount = importProgressData.value.failure

    if (failureCount === 0) {
      message.success(`导入完成！成功处理 ${successCount} 条联系方式`)
    } else if (successCount === 0) {
      message.error(`导入失败，${failureCount} 条数据处理失败`)
    } else {
      message.warning(`导入部分成功！成功 ${successCount} 条，失败 ${failureCount} 条`)
    }

    // 刷新联系方式列表
    if (successCount > 0) {
      await loadContactList()
    }

  } catch (error) {
    console.error('导入失败:', error)
    message.error('导入失败: ' + (error.message || '未知错误'))
  } finally {
    importLoading.value = false
  }
}

const handleImportCancel = () => {
  importModalVisible.value = false
  // 清理导入状态
  fileList.value = []
  importPreviewData.value = []
  availableColumns.value = []
  importResult.value = null
  currentStep.value = 0 // 重置步骤
  // 重置补充字段
  selectedAdditionalFields.value = []
}

// 步骤导航逻辑
const canProceedToNextStep = computed(() => {
  switch (currentStep.value) {
    case 0: // 选择平台
      return importForm.平台类型
    case 1: // 上传文件
      return fileList.value.length > 0 && fullImportData.value.length > 0
    case 2: // 字段映射
      return selectedContactColumns.value.length > 0
    case 3: // 预览确认
      return importPreviewData.value.length > 0
    default:
      return false
  }
})

const nextStep = () => {
  if (currentStep.value === 3) {
    // 第4步：开始导入
    handleImportSubmit()
  } else {
    currentStep.value++
    if (currentStep.value === 3) {
      // 进入预览步骤，生成预览数据
      generatePreviewData()
    }
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 生成预览数据
const generatePreviewData = () => {
  if (fullImportData.value.length === 0 || selectedContactColumns.value.length === 0) return

  // 联系方式有效性验证函数
  const isValidContact = (value) => {
    if (!value || typeof value !== 'string') return false
    const cleanValue = value.trim()
    // 不能包含*等无效字符
    if (cleanValue.includes('*') || cleanValue.includes('？') || cleanValue.includes('?')) return false
    if (cleanValue.length < 3) return false
    return true
  }

  // 联系方式类型检测函数
  const detectContactType = (value) => {
    if (!isValidContact(value)) return '无效'
    const cleanValue = value.trim().replace(/\s+/g, '')
    const 手机号正则 = /^1[3-9]\d{9}$/
    const 邮箱正则 = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const 微信号正则 = /^[a-zA-Z][a-zA-Z0-9_-]{4,19}$/

    if (手机号正则.test(cleanValue)) return '手机号'
    if (邮箱正则.test(value)) return '邮箱'
    if (微信号正则.test(cleanValue)) return '微信号'
    return '未知'
  }

  // 拆分数据：一行多个联系方式变成多条数据
  const previewData = []
  let rowIndex = 1

  fullImportData.value.forEach((row) => {
    // 收集所有有效的联系方式
    const validContacts = []
    selectedContactColumns.value.forEach((col) => {
      const contactValue = row[col] || ''
      if (contactValue.trim()) {
        const contactType = detectContactType(contactValue)
        if (contactType !== '无效') {
          validContacts.push({
            value: contactValue.trim(),
            type: contactType
          })
        }
      }
    })

    // 如果有有效联系方式，为每个联系方式创建一条数据
    if (validContacts.length > 0) {
      validContacts.forEach((contact) => {
        const previewRow = {
          序号: rowIndex++,
          联系方式: contact.value,
          联系方式类型: contact.type
        }

        // 添加其他字段
        if (fieldMapping.平台账号) {
          previewRow[platformAccountLabel.value] = row[fieldMapping.平台账号] || ''
        }
        if (fieldMapping.个人备注) {
          previewRow['个人备注'] = row[fieldMapping.个人备注] || ''
        }
        if (fieldMapping.个人标签) {
          previewRow['个人标签'] = row[fieldMapping.个人标签] || ''
        }

        // 添加补充字段
        const 补充信息 = {}
        selectedAdditionalFields.value.forEach(fieldName => {
          const value = row[fieldName] || ''
          if (value.trim()) {
            补充信息[fieldName] = value.trim()
            previewRow[fieldName] = value.trim()
          }
        })
        previewRow['补充信息JSON'] = JSON.stringify(补充信息)

        previewData.push(previewRow)
      })
    }
  })

  importPreviewData.value = previewData
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 数据质量检查
const validateDataQuality = (data) => {
  const issues = []
  const totalRows = data.length

  // 检查空行比例
  const emptyRows = data.filter(row =>
    Object.values(row).every(value => !value || value.toString().trim() === '')
  ).length

  if (emptyRows > totalRows * 0.3) {
    issues.push(`${emptyRows}行完全空白`)
  }

  // 检查重复数据
  const uniqueRows = new Set(data.map(row => JSON.stringify(row)))
  const duplicateCount = totalRows - uniqueRows.size
  if (duplicateCount > 0) {
    issues.push(`${duplicateCount}行重复数据`)
  }

  // 检查数据有效性
  let invalidRows = 0
  data.forEach(row => {
    const hasValidData = Object.values(row).some(value =>
      value && value.toString().trim() !== ''
    )
    if (!hasValidData) {
      invalidRows++
    }
  })

  if (invalidRows > 0) {
    issues.push(`${invalidRows}行无有效联系方式`)
  }

  return {
    isValid: issues.length === 0,
    message: issues.length > 0 ? issues.join('、') : '数据质量良好',
    issues
  }
}

// 获取详细错误信息
const getDetailedErrorMessage = (error) => {
  if (error.message.includes('JSON')) {
    return '文件格式错误，请确保使用正确的CSV或Excel格式'
  }
  if (error.message.includes('read')) {
    return '文件读取失败，请检查文件是否损坏或被占用'
  }
  if (error.message.includes('parse')) {
    return '数据解析失败，请检查文件内容格式是否正确'
  }
  return error.message || '未知错误，请重试或联系技术支持'
}

// 优化的数据构建函数
const buildImportDataWithProgress = async (rawData) => {
  const 导入数据 = []
  const chunkSize = 1000 // 每次处理1000行

  for (let i = 0; i < rawData.length; i += chunkSize) {
    const chunk = rawData.slice(i, i + chunkSize)

    // 处理当前块
    chunk.forEach(row => {
      selectedContactColumns.value.forEach(contactColumn => {
        const 联系方式值 = row[contactColumn] || ''
        if (联系方式值.trim() !== '') {
          const item = {
            平台账号: fieldMapping.平台账号 ? (row[fieldMapping.平台账号] || '').trim() : '',
            联系方式: 联系方式值.trim(),
            个人备注: fieldMapping.个人备注 ? (row[fieldMapping.个人备注] || '').trim() : ''
          }

          // 智能识别联系方式类型
          item.联系方式类型 = detectContactType(联系方式值)

          // 处理个人标签
          if (fieldMapping.个人标签) {
            const 标签文本 = row[fieldMapping.个人标签] || ''
            if (标签文本) {
              item.个人标签 = 标签文本.split(/[,，;；|｜]/).map(tag => tag.trim()).filter(tag => tag)
            } else {
              item.个人标签 = []
            }
          }

          // 处理补充信息
          const 补充信息 = {}
          selectedAdditionalFields.value.forEach(fieldName => {
            const value = row[fieldName] || ''
            if (value.trim()) {
              补充信息[fieldName] = value.trim()
            }
          })

          // 如果有补充信息，添加到导入数据中
          if (Object.keys(补充信息).length > 0) {
            item.补充信息JSON = JSON.stringify(补充信息, null, 0)
          }

          导入数据.push(item)
        }
      })
    })

    // 让出控制权，避免阻塞UI
    if (i + chunkSize < rawData.length) {
      await new Promise(resolve => setTimeout(resolve, 0))
    }
  }

  return 导入数据
}

// 从预览数据构建导入数据
const buildImportDataFromPreview = (previewData) => {
  const 导入数据 = []

  previewData.forEach(row => {
    // 只处理有效的联系方式
    if (row.联系方式 && row.联系方式类型 && row.联系方式类型 !== '无效') {
      const item = {
        平台账号: row[platformAccountLabel.value] || '',
        联系方式: row.联系方式,
        联系方式类型: row.联系方式类型,
        个人备注: row.个人备注 || '',
        个人标签: []
      }

      // 处理个人标签
      if (row.个人标签) {
        const 标签文本 = row.个人标签
        if (标签文本) {
          item.个人标签 = 标签文本.split(/[,，;；|｜]/).map(tag => tag.trim()).filter(tag => tag)
        }
      }

      // 处理补充信息
      if (row.补充信息JSON) {
        item.补充信息JSON = row.补充信息JSON
      }

      导入数据.push(item)
    }
  })

  return 导入数据
}

// 联系方式类型检测
const detectContactType = (value) => {
  const cleanValue = value.trim().replace(/\s+/g, '')

  // 手机号检测（中国大陆手机号）
  const 手机号正则 = /^1[3-9]\d{9}$/
  if (手机号正则.test(cleanValue)) {
    return '手机'
  }

  // 邮箱检测
  const 邮箱正则 = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (邮箱正则.test(value)) {
    return '邮箱'
  }

  // 微信号检测（字母开头，包含字母数字下划线，5-20位）
  const 微信号正则 = /^[a-zA-Z][a-zA-Z0-9_-]{4,19}$/
  if (微信号正则.test(cleanValue)) {
    return '微信'
  }

  // 如果都不匹配，默认返回微信（因为只支持这三种类型）
  return '微信'
}

// 下载导入模板
const downloadTemplate = () => {
  const platform = importForm.平台类型 || '抖音'

  // 根据平台生成不同的模板数据
  const templateData = generateTemplateData(platform)

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  const ws = XLSX.utils.json_to_sheet(templateData)

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 平台账号
    { wch: 15 }, // 联系方式
    { wch: 30 }, // 个人备注
    { wch: 20 }  // 个人标签
  ]
  ws['!cols'] = colWidths

  // 添加工作表
  XLSX.utils.book_append_sheet(wb, ws, '联系方式导入模板')

  // 下载文件
  const fileName = `${platform}联系方式导入模板.xlsx`
  XLSX.writeFile(wb, fileName)

  message.success('模板下载成功')
}

// 生成模板数据
const generateTemplateData = (platform) => {
  const baseTemplate = [
    {
      '平台账号': platform === '抖音' ? 'douyin_user_001' : 'wechat_user_001',
      '联系方式': '13800138000',
      '个人备注': '这是一个示例备注',
      '个人标签': '标签1,标签2,标签3'
    },
    {
      '平台账号': platform === '抖音' ? 'douyin_user_002' : 'wechat_user_002',
      '联系方式': '<EMAIL>',
      '个人备注': '邮箱联系方式示例',
      '个人标签': '邮箱用户,重要客户'
    },
    {
      '平台账号': platform === '抖音' ? 'douyin_user_003' : 'wechat_user_003',
      '联系方式': 'wechat_id_123',
      '个人备注': '微信号联系方式示例',
      '个人标签': '微信用户'
    }
  ]

  // 添加说明行
  const instructions = {
    '平台账号': `请填写${platform}账号`,
    '联系方式': '支持手机号、微信号、邮箱等',
    '个人备注': '可选，个人备注信息',
    '个人标签': '可选，多个标签用逗号分隔'
  }

  return [instructions, ...baseTemplate]
}

// 生命周期
onMounted(() => {
  loadContactList()
})

// 显示导入进度弹窗
const showImportProgressModal = () => {
  showImportProgress.value = true
}

// 关闭导入进度弹窗
const closeImportProgress = () => {
  showImportProgress.value = false
  // 重置导入状态
  currentStep.value = 0
  fileList.value = []
  fullImportData.value = []
  importPreviewData.value = []
  selectedContactColumns.value = []
  selectedAdditionalFields.value = []
  // 刷新联系方式列表
  loadContactList()
}

// 简化失败原因显示
const getShortMessage = (message) => {
  if (!message) return '未知错误'

  // 简化常见的错误信息
  if (message.includes('已存在')) {
    return '已存在'
  }
  if (message.includes('格式不正确') || message.includes('格式错误')) {
    return '格式错误'
  }
  if (message.includes('不能为空')) {
    return '数据为空'
  }
  if (message.includes('重复')) {
    return '重复数据'
  }

  // 如果消息太长，截取前15个字符以显示更多信息
  return message.length > 15 ? message.substring(0, 15) + '...' : message
}

// 导出失败记录
const exportFailedRecords = async () => {
  exportFailedLoading.value = true
  try {
    // 筛选失败的记录
    const failedRecords = importProgressData.value.details.filter(detail => detail.status === 'error')

    if (failedRecords.length === 0) {
      message.warning('没有失败记录可导出')
      return
    }

    // 构建CSV数据
    const headers = ['序号', '联系方式', '联系方式类型', '平台账号', '失败原因']
    const csvContent = [
      headers.join(','),
      ...failedRecords.map(record => [
        record.index,
        `"${record.联系方式}"`,
        `"${record.联系方式类型}"`,
        `"${record.平台账号}"`,
        `"${record.message}"`
      ].join(','))
    ].join('\n')

    // 创建并下载文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `导入失败记录_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    message.success(`已导出 ${failedRecords.length} 条失败记录`)
  } catch (error) {
    console.error('导出失败记录错误:', error)
    message.error('导出失败记录失败')
  } finally {
    exportFailedLoading.value = false
  }
}
</script>

<style scoped>
.contact-info-page {
  padding: 24px;
  min-height: calc(100vh - 64px - 48px);
}

/* 
  未关联达人的警告样式
  
  当联系方式没有关联到达人时（达人id为null），显示红色感叹号警告
  这些样式用于美化警告信息的显示效果
*/
.no-talent-warning {
  display: flex;          /* 使用弹性布局 */
  align-items: center;    /* 垂直居中对齐 */
  gap: 12px;             /* 图标和文本之间的间距 */
  padding: 8px 0;        /* 上下内边距 */
  cursor: pointer;       /* 鼠标悬停时显示手形光标 */
  transition: all 0.3s;  /* 添加过渡效果 */
  
  &:hover {
    background-color: #fff1f0;  /* 悬停时的背景色 */
    border-radius: 6px;         /* 圆角边框 */
    padding: 8px 12px;          /* 悬停时增加左右内边距 */
    
    .no-talent-icon {
      transform: scale(1.1);    /* 悬停时图标放大 */
    }
    
    .warning-title {
      color: #ff7875;           /* 悬停时标题色彩变浅 */
    }
  }
}

.no-talent-icon {
  flex-shrink: 0;        /* 图标不缩放，保持固定大小 */
}

.no-talent-text {
  flex: 1;               /* 文本区域占据剩余空间 */
}

.warning-title {
  font-size: 14px;       /* 主标题字体大小 */
  font-weight: 600;      /* 主标题加粗 */
  color: #ff4d4f;        /* 红色警告色 */
  margin-bottom: 2px;    /* 标题与副标题之间的间距 */
}

.warning-subtitle {
  font-size: 12px;       /* 副标题字体大小 */
  color: #8c8c8c;        /* 灰色文本 */
}

/* 关联达人弹窗样式 */
.bind-talent-form {
  .contact-info-display {
    margin-bottom: 20px;
    
    .contact-display {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
    }
  }
  
  .search-section {
    margin-bottom: 20px;
  }
  
  .search-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
  }
  
  .search-results {
    max-height: 400px;
    overflow-y: auto;
    
    .results-list {
      .talent-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: #1890ff;
          background-color: #f0f8ff;
        }
        
        &.selected {
          border-color: #1890ff;
          background-color: #e6f7ff;
        }
        
        .talent-avatar {
          margin-right: 12px;
        }
        
        .talent-info {
          flex: 1;
          
          .talent-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }
          
          .talent-account {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }
          
          .talent-stats {
            display: flex;
            gap: 8px;
          }
        }
      }
    }
  }
  
  .empty-results {
    text-align: center;
    padding: 40px 20px;
  }
}



/* 导入模态框样式 */
.import-modal {
  .ant-modal-body {
    max-height: 75vh;
    overflow-y: auto;
  }
}

.import-container {
  padding: 0;
}

/* 基础配置区域 */
.import-settings {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

/* 设置卡片样式 */
.setting-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.setting-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.card-icon {
  color: #1890ff;
  font-size: 16px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.card-subtitle {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: auto;
}

/* 卡片内容 */
.card-content {
  padding: 16px;
}

/* 平台选择卡片 */
.platform-card .card-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.platform-radio {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.platform-btn {
  height: 36px;
  line-height: 34px;
  border-radius: 6px;
  font-weight: 500;
  text-align: center;
  width: 100%;
}

/* 上传卡片 */
.upload-card .card-content {
  padding: 12px;
}

.elegant-upload-dragger {
  height: 80px;
}

.elegant-upload-dragger .ant-upload-drag {
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.elegant-upload-dragger .ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 4px;
}

.upload-icon {
  font-size: 24px;
  color: #1890ff;
}

.upload-text {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.upload-hint {
  font-size: 11px;
  color: #8c8c8c;
}

/* 文件列表样式 */
.elegant-upload-dragger .ant-upload-list {
  margin-top: 8px;
}

.elegant-upload-dragger .ant-upload-list-item {
  padding: 6px 10px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 6px;
  font-size: 12px;
}

.elegant-upload-dragger .ant-upload-list-item:hover {
  background: #f0f8ff;
  border-color: #1890ff;
}

.elegant-upload-dragger .ant-upload-list-item-name {
  font-size: 12px;
  color: #262626;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.elegant-upload-dragger .ant-upload-list-item-actions {
  font-size: 12px;
}

/* 紧凑字段映射样式 */
.field-mapping {
  margin: 20px 0;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.field-mapping-header h4 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

.field-mapping-header p {
  margin: 0 0 16px 0;
  color: #8c8c8c;
  font-size: 12px;
}

.contact-field-section {
  margin-bottom: 16px;
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.other-fields-section {
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.compact-label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 6px;
}

.compact-select {
  width: 100%;
}

.required-mark {
  color: #ff4d4f;
  margin-right: 4px;
}

.selected-count {
  color: #1890ff;
  font-weight: normal;
  font-size: 12px;
}

.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0;
}

.search-filters {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.stats-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f2f5;
  border-radius: 6px;
}

.contact-list {
  margin-top: 16px;
}

.talent-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.talent-details {
  flex: 1;
}

.talent-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.talent-account {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

/* 未关联达人样式 */
.unlinked-talent-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  border: 1px solid #ffe7de;
  border-radius: 8px;
  background: #fff7f0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-left: 4px solid #ff9c6e;
}

.unlinked-talent-info:hover {
  background: #ffede4;
  border-color: #ffb38a;
  border-left-color: #ff7a45;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(255, 156, 110, 0.2);
}

.talent-avatar-container {
  position: relative;
}

.unlinked-avatar {
  background: #ffa39e;
  border: 2px solid #ffaaa5;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unlinked-icon {
  color: #ffffff;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unlinked-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #ff4d4f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #ffffff;
  animation: pulse 2s infinite;
}

.badge-dot {
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.unlinked-details {
  flex: 1;
}

.unlinked-name {
  font-weight: 600;
  color: #d46b08;
  margin-bottom: 4px;
}

.unlinked-account {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 6px;
}

.talent-tags {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.unlinked-tip {
  font-size: 11px;
  color: #ff7a45;
  font-weight: 500;
  opacity: 0.8;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-item {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .additional-info {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 4px;

    .additional-tag {
      font-size: 11px;
      padding: 1px 6px;
      border-radius: 2px;
    }
  }
}

.associate-contact-btn {
  margin-top: 4px;

  .ant-btn {
    padding: 0;
    height: auto;
    font-size: 12px;
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }
  }
}

/* 关联联系人弹窗样式 */
.associate-contact-content {
  .contact-info-display {
    margin-bottom: 16px;
  }

  .associate-tabs {
    .existing-contact-tab {
      .contact-list-container {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        padding: 8px;
      }

      .contact-list {
        .contact-item {
          padding: 12px;
          border: 1px solid #f0f0f0;
          border-radius: 6px;
          margin-bottom: 8px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #1890ff;
            background-color: #f6ffed;
          }

          &.active {
            border-color: #1890ff;
            background-color: #e6f7ff;
          }

          .contact-name {
            font-weight: 500;
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
          }

          .contact-id {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 4px;
          }

          .contact-methods {
            font-size: 12px;
            color: #666;

            .contact-methods-label {
              color: #8c8c8c;
              margin-right: 4px;
            }

            .contact-methods-list {
              color: #1890ff;
              font-weight: 500;
            }
          }

          .contact-no-methods {
            font-size: 12px;

            .no-methods-text {
              color: #bfbfbf;
              font-style: italic;
            }
          }
        }
      }
    }

    .new-contact-tab {
      padding-top: 16px;
    }

    .tab-actions {
      margin-top: 24px;
      text-align: center;
    }
  }
}

.contact-linked {
  margin-top: 4px;

  .ant-tag {
    margin: 0;
    font-size: 11px;
    padding: 1px 6px;
    border-radius: 2px;
    display: inline-flex;
    align-items: center;
    gap: 2px;
  }
}

/* 补充信息列样式 */
.additional-info-container {
  max-width: 200px;

  .additional-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .additional-items {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .additional-item {
        display: flex;
        align-items: center;
      }

      .additional-tag {
        margin: 0;
        max-width: 180px;
        display: flex;
        align-items: center;
        padding: 2px 6px;
        font-size: 11px;
        border-radius: 2px;

        .tag-key {
          font-weight: 500;
          color: #1890ff;
        }

        .tag-separator {
          margin: 0 4px;
          color: #666;
        }

        .tag-value {
          color: #333;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .additional-more {
      margin-top: 2px;

      .more-tag {
        font-size: 11px;
        padding: 1px 4px;
        cursor: default;
      }
    }
  }

  .no-additional {
    color: #8c8c8c;
    font-style: italic;
    font-size: 12px;
  }
}

.contact-value {
  font-family: monospace;
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.no-contact {
  color: #8c8c8c;
  font-style: italic;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.no-tags {
  color: #8c8c8c;
  font-style: italic;
  font-size: 12px;
}

.note-content {
  max-width: 200px;
}

.note-text {
  color: #595959;
  font-size: 12px;
  line-height: 1.4;
}

.no-note {
  color: #8c8c8c;
  font-style: italic;
  font-size: 12px;
}

.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
}

/* 弹窗内的达人信息样式 */
.ant-modal .talent-info {
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contact-info-page {
    padding: 16px;
  }
  .search-filters .ant-row {
    gap: 8px;
  }

  .stats-info .ant-row {
    gap: 8px;
  }

  .contact-list :deep(.ant-table) {
    font-size: 12px;
  }

  .talent-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f5f5f5;
}

:deep(.ant-table-fixed-left) {
  box-shadow: 6px 0 6px -4px rgba(0, 0, 0, 0.15);
}

:deep(.ant-table-fixed-right) {
  box-shadow: -6px 0 6px -4px rgba(0, 0, 0, 0.15);
}

/* 导入功能样式 */
.import-container {
  max-height: 70vh;
  overflow-y: auto;
}

.platform-selection {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.file-upload {
  margin-bottom: 24px;
}

.upload-tips {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.upload-tips p {
  margin: 0;
  line-height: 1.4;
}

/* 详情弹窗样式 */
.detail-modal {
  .detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .talent-detail-info {
    display: flex;
    gap: 16px;
    align-items: flex-start;

    .talent-avatar-section {
      flex-shrink: 0;
    }

    .talent-info-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  .info-row {
    display: flex;
    align-items: center;
    gap: 8px;

    .info-label {
      font-weight: 500;
      color: #666;
      min-width: 80px;
    }

    .info-value {
      color: #333;
      flex: 1;
    }
  }

  .contact-detail {
    .contact-value {
      font-family: monospace;
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 13px;
    }
  }

  .additional-detail {
    .additional-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 12px;
    }

    .additional-detail-item {
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 12px;
      background: #fafafa;

      .detail-item-header {
        margin-bottom: 8px;

        .detail-item-key {
          font-weight: 500;
          color: #1890ff;
          font-size: 13px;
        }
      }

      .detail-item-content {
        .detail-item-value {
          color: #333;
          word-break: break-all;
          line-height: 1.5;
        }
      }
    }
  }

  .tags-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .detail-tag {
      margin: 0;
    }
  }

  .note-detail {
    .note-detail-text {
      color: #333;
      line-height: 1.6;
      margin: 0;
      padding: 12px;
      background: #f9f9f9;
      border-radius: 6px;
      border-left: 3px solid #1890ff;
    }
  }

  .no-contact-detail,
  .no-additional-detail,
  .no-tags-detail,
  .no-note-detail {
    text-align: center;
    padding: 20px;
  }
}

/* 补充信息编辑样式 */
.additional-info-edit {
  .additional-items-edit {
    margin-bottom: 12px;

    .additional-item-edit {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .additional-actions {
    border-top: 1px dashed #d9d9d9;
    padding-top: 12px;
  }
}

.field-mapping {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.field-mapping h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 600;
}

.data-preview {
  margin-bottom: 24px;
}

.data-preview h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 600;
}

.additional-fields-section {
  margin-top: 24px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.additional-fields-section h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: 600;
}

.field-description {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 13px;
}

.additional-fields-list .mb-2 {
  margin-bottom: 8px;
}

/* 导入进度弹窗样式 */
.import-progress-container {
  padding: 16px 0;
}

.progress-summary {
  margin-bottom: 24px;
}

.progress-bar {
  margin-bottom: 24px;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.details-header h4 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.details-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.details-stats .success-count {
  color: #52c41a;
  font-weight: 500;
}

.details-stats .failure-count {
  color: #ff4d4f;
  font-weight: 500;
}

.details-stats .total-count {
  color: #666;
  font-weight: 500;
}

.details-list-container {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.details-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.detail-item.success {
  background: #f6ffed;
}

.detail-item.error {
  background: #fff2f0;
}

.detail-item .index {
  width: 40px;
  font-weight: 500;
  color: #666;
}

.detail-item .contact {
  flex: 1;
  font-family: monospace;
  color: #333;
}

.detail-item .type {
  width: 80px;
  color: #666;
}

.detail-item .account {
  width: 100px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.detail-item .status {
  width: 80px;
  text-align: right;
}

.progress-footer {
  margin-top: 24px;
  text-align: center;
}

.preview-stats {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.import-result {
  margin-top: 24px;
}

.result-details p {
  margin: 4px 0;
}

.result-details ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.result-details li {
  margin: 2px 0;
  font-size: 12px;
}

/* 导入功能响应式设计 */
@media (max-width: 768px) {
  .import-container {
    max-height: 60vh;
  }

  .field-mapping .ant-row {
    margin: 0;
  }

  .field-mapping .ant-col {
    padding: 0 4px;
    margin-bottom: 12px;
  }

}

/* 删除区域样式 */
.delete-section {
  text-align: center;
  padding: 16px 0;
}

.form-help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
