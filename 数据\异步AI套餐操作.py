from typing import Any, Dict, Optional

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 导入统一日志系统
from 日志 import 数据库日志器, 系统日志器, 错误日志器


# {{ AURA-X: Remove - 删除ai模型表相关代码，该表已被删除. Approval: 寸止(ID:1735372800). }}
# {{ Source: PostgreSQL MCP确认ai模型表不存在 }}
# async def 异步获取AI模型列表 - 已删除，ai模型表不存在
async def 异步获取AI模型列表(页码: int = 1, 每页数量: int = 10) -> Dict[str, Any]:
    """
    获取AI模型列表 - 已废弃，ai模型表不存在

    返回:
        空的模型列表
    """
    数据库日志器.warning("ai模型表已被删除，返回空列表")
    return {"模型列表": [], "总数": 0, "有下一页": False}


# {{ AURA-X: Remove - 删除ai模型表相关代码，该表已被删除. Approval: 寸止(ID:1735372800). }}
# {{ Source: PostgreSQL MCP确认ai模型表不存在 }}
async def 异步获取AI模型详情(模型id: int) -> Optional[Dict[str, Any]]:
    """
    获取单个AI模型的详细信息 - 已废弃，ai模型表不存在

    返回:
        None
    """
    数据库日志器.warning(f"ai模型表已被删除，无法获取模型详情: 模型id={模型id}")
    return None


async def 异步创建用户AI信息(
    用户id: int,
    组织id: Optional[int],
    模型id: int,
    智能体id: str,
    知识id: str,
    员工名称: Optional[str] = None,
    员工性格: Optional[str] = None,
    店铺名称: Optional[str] = None,
) -> Optional[int]:
    """
    创建用户AI信息记录 - 算力消耗模式，无需试用时间限制

    参数:
        用户id: 用户id
        组织id: 组织ID，可选
        套餐id: 套餐ID
        智能体id: 智能体id
        知识id: 知识id
        员工名称: 员工名称，可选
        员工性格: 员工性格，可选
        店铺名称: 店铺名称，可选
        试用开始时间: 试用开始时间，默认为当前时间
        试用结束时间: 试用结束时间，可选

    返回:
        新创建的记录id，失败返回None
    """

    # 参数验证
    if not 用户id or 用户id < 1:
        错误日志器.error("创建用户套餐关联失败: 无效的用户id")
        return None

    if not 模型id or 模型id < 1:
        错误日志器.error("创建用户AI信息失败: 无效的模型id")
        return None

    if not 智能体id or not isinstance(智能体id, str):
        错误日志器.error("创建用户套餐关联失败: 无效的智能体id")
        return None

    if not 知识id or not isinstance(知识id, str):
        错误日志器.error("创建用户套餐关联失败: 无效的知识id")
        return None

    # 准备参数 - 算力消耗模式无需时间限制
    参数 = (用户id, 组织id, 模型id, 智能体id, 知识id, 员工名称, 员工性格, 店铺名称)

    try:
        记录id = None

        # 使用单个数据库连接和事务处理整个过程，确保一致性
        async with 异步连接池实例.获取连接() as 连接:
            try:
                # {{ AURA-X: Modify - 修复PostgreSQL事务语法，使用事务上下文管理器. Approval: 寸止(ID:1735372800). }}
                async with 连接.transaction():
                    数据库日志器.debug(
                        f"开始事务: 创建用户AI信息及知识库记录, 用户id={用户id}, 模型id={模型id}"
                    )

                    # 插入用户AI信息记录 - 算力消耗模式
                    AI信息插入SQL = """
                    INSERT INTO 用户ai信息表 (
                        用户id, 组织ID, 套餐ID, 智能体id, 知识id,
                        员工名称, 员工性格, 店铺名称
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING id
                    """

                    记录id = await 连接.fetchval(AI信息插入SQL, *参数)

                    if not 记录id:
                        错误日志器.error("插入用户AI信息记录未返回id")
                        raise Exception("插入用户AI信息记录失败")

                    系统日志器.info(f"事务中创建用户套餐关联成功: ID={记录id}")

                    # 检查并创建知识库表记录
                    检查知识库SQL = "SELECT id FROM 知识库表 WHERE coze_知识id = $1"
                    知识库检查结果 = await 连接.fetchrow(检查知识库SQL, 知识id)

                    if not 知识库检查结果:
                        系统日志器.info(
                            f"知识id {知识id} 在知识库表中不存在，在同一事务中创建"
                        )
                        # 知识库表中不存在记录，创建一个默认记录
                        知识库名称 = f"用户{用户id}的默认知识库"
                        知识库描述 = f"用户{用户id}的默认知识库，套餐关联id: {记录id}"

                        插入知识库SQL = """
                        INSERT INTO 知识库表 (coze_知识id, 名称, 描述)
                        VALUES ($1, $2, $3)
                        RETURNING id
                        """

                        知识库表id = await 连接.fetchval(
                            插入知识库SQL, 知识id, 知识库名称, 知识库描述
                        )

                        if 知识库表id:
                            系统日志器.info(
                                f"事务中成功创建知识库表记录: ID={知识库表id}, coze_知识id={知识id}"
                            )
                        else:
                            系统日志器.warning("事务中创建知识库表记录未返回id")
                            raise Exception("创建知识库表记录失败")
                    else:
                        系统日志器.info(
                            f"知识id {知识id} 已存在于知识库表中，ID: {知识库检查结果.get('id', '未知')}"
                        )

                # 事务自动提交
                系统日志器.info(
                    f"成功提交事务: 创建用户套餐关联及知识库记录，返回记录id: {记录id}"
                )
                return 记录id

            except Exception as tx_error:
                # 事务自动回滚
                错误日志器.error(
                    f"创建用户套餐关联事务失败，已回滚: {str(tx_error)}", exc_info=True
                )
                return None

    except Exception as e:
        print(f"数据层 - 创建用户套餐关联异常({type(e).__name__}): {str(e)}")
        错误日志器.error(
            f"创建用户套餐关联异常({type(e).__name__}): {str(e)}", exc_info=True
        )
        return None


async def 异步检查用户是否已创建AI智能体(
    用户id: int, 组织id: Optional[int], 模型id: int
) -> bool:
    """
    检查用户或组织是否已创建指定AI智能体

    参数:
        用户id: 用户id
        组织id: 组织ID，可选
        模型id: 模型id

    返回:
        是否已创建
    """
    try:
        # 参数验证
        if not 用户id or 用户id < 1:
            数据库日志器.warning(f"检查用户购买套餐: 无效的用户id {用户id}")
            return False

        if not 模型id or 模型id < 1:
            数据库日志器.warning(f"检查用户创建AI智能体: 无效的模型id {模型id}")
            return False

        # 构建查询条件
        条件 = "套餐ID = $1 AND (用户id = $2"
        参数 = [模型id, 用户id]
        参数索引 = 3

        if 组织id and 组织id > 0:
            条件 += " OR 组织ID = $1"
            参数.append(组织id)
            参数索引 += 1

        条件 += ")"

        # 查询关联记录
        查询语句 = f"""
        SELECT COUNT(*) as 数量 FROM 用户ai信息表
        WHERE {条件}
        """

        # 使用PostgreSQL连接池执行查询
        结果 = await 异步连接池实例.执行查询(查询语句, tuple(参数))

        已创建 = 结果[0]["数量"] > 0 if 结果 else False
        数据库日志器.debug(
            f"检查用户创建AI智能体: 用户id={用户id}, 模型id={模型id}, 已创建={已创建}"
        )
        return 已创建

    except Exception as e:
        错误日志器.error(f"检查用户是否已创建AI智能体异常: {str(e)}", exc_info=True)
        return False


async def 异步获取用户AI配置列表(
    用户id: int, 页码: int = 1, 每页数量: int = 10
) -> Dict[str, Any]:
    """
    获取用户已创建的AI配置列表

    参数:
        用户id: 用户id
        页码: 当前页码，从1开始
        每页数量: 每页显示的数量

    返回:
        包含AI配置列表、总数和是否有下一页的字典
    """
    try:
        # 参数验证
        if not 用户id or 用户id < 1:
            错误日志器.warning(f"获取用户套餐列表: 无效的用户id {用户id}")
            return {
                "套餐列表": [],
                "总数": 0,
                "有下一页": False,
                "错误": "无效的用户id",
            }

        if 页码 < 1:
            数据库日志器.warning(f"获取用户套餐列表: 无效的页码 {页码}，使用默认值 1")
            页码 = 1

        if 每页数量 < 1:
            数据库日志器.warning(
                f"获取用户套餐列表: 无效的每页数量 {每页数量}，使用默认值 10"
            )
            每页数量 = 10

        # 计算偏移量
        偏移量 = (页码 - 1) * 每页数量

        # 使用单个连接处理所有查询
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            # 获取总数 - 优先查询用户ai信息表，只查询默认类型的AI
            总数查询 = """
            SELECT COUNT(*) as 总数
            FROM 用户ai信息表
            WHERE 用户id = $1 AND AI类型 = '默认'
            """
            总数结果 = await 连接.fetchrow(总数查询, 用户id)
            总数 = 总数结果["总数"] if 总数结果 else 0

            # 如果没有记录，直接返回空结果
            if 总数 == 0:
                数据库日志器.info(f"用户没有创建任何AI智能体: 用户id={用户id}")
                return {"AI配置列表": [], "总数": 0, "有下一页": False}

            # 获取分页数据 - 根据实际表结构优化查询，只查询默认类型的AI
            # {{ AURA-X: Fix - 移除ai模型表JOIN，该表已被删除. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL MCP确认ai模型表不存在 }}
            查询语句 = """
            SELECT
                uai.id, uai.用户id, uai.组织ID, uai.模型id,
                uai.智能体id, uai.知识id, uai.知识库文档ids,
                uai.员工名称, uai.员工性格, uai.店铺名称, uai.AI类型,
                NULL as 模型名称, NULL as 模型描述, NULL as 算力消耗, NULL as 头像,
                uai.创建时间, uai.更新时间
            FROM
                用户ai信息表 uai
            WHERE
                uai.用户id = $1 AND uai.AI类型 = '默认'
            ORDER BY
                uai.id DESC
            LIMIT $2 OFFSET $3
            """

            结果 = await 连接.fetch(查询语句, 用户id, 每页数量, 偏移量)

        # 计算是否有下一页
        有下一页 = (偏移量 + 每页数量) < 总数

        数据库日志器.debug(
            f"获取用户AI配置列表成功: 用户id={用户id}, 总数={总数}, 返回记录数={len(结果)}"
        )

        return {"AI配置列表": 结果, "总数": 总数, "有下一页": 有下一页}
    except Exception as e:
        错误日志器.error(f"获取用户套餐列表异常: {str(e)}", exc_info=True)
        return {"套餐列表": [], "总数": 0, "有下一页": False, "错误": str(e)}


async def 异步获取用户AI配置详情(
    用户id: int, 用户AI信息id: int
) -> Optional[Dict[str, Any]]:
    """
    获取用户已创建的AI配置详情

    参数:
        用户id: 用户id
        用户AI信息id: 用户ai信息表的ID

    返回:
        AI配置详情字典，不存在则返回None
    """
    try:
        # 参数验证
        if not 用户id or 用户id < 1:
            数据库日志器.warning(f"获取用户套餐详情: 无效的用户id {用户id}")
            return None

        if not 用户AI信息id or 用户AI信息id < 1:
            数据库日志器.warning(
                f"获取用户AI配置详情: 无效的用户AI信息id {用户AI信息id}"
            )
            return None

        # {{ AURA-X: Optimize - 使用连接池便利方法，避免手动获取连接. Approval: 寸止(ID:1735372800). }}
        # {{ Source: asyncpg最佳实践 - 直接使用连接池方法 }}
        # 根据实际表结构优化查询，只查询默认类型的AI
        # {{ AURA-X: Fix - 移除ai模型表JOIN，该表已被删除. Approval: 寸止(ID:1735372800). }}
        # {{ Source: PostgreSQL MCP确认ai模型表不存在 }}
        查询语句 = """
        SELECT
            uai.id, uai.用户id, uai.组织ID, uai.模型id,
            uai.智能体id, uai.知识id, uai.知识库文档ids,
            uai.员工名称, uai.员工性格, uai.店铺名称, uai.AI类型,
            NULL as 模型名称, NULL as 模型描述, NULL as 算力消耗, NULL as 头像,
            uai.创建时间, uai.更新时间
        FROM
            用户ai信息表 uai
        WHERE
            uai.用户id = $1 AND uai.id = $2 AND uai.AI类型 = '默认'
        LIMIT 1
        """

        结果 = await 异步连接池实例.执行单行查询(查询语句, (用户id, 用户AI信息id))

        if 结果:
            数据库日志器.debug(
                f"获取用户AI配置详情成功: 用户id={用户id}, 用户AI信息id={用户AI信息id}"
            )
            return 结果
        else:
            数据库日志器.info(
                f"未找到用户AI配置详情: 用户id={用户id}, 用户AI信息id={用户AI信息id}"
            )
            return None

    except Exception as e:
        错误日志器.error(f"获取用户套餐详情异常: {str(e)}", exc_info=True)
        return None


async def 异步更新用户套餐信息(
    套餐关联id: int,
    员工名称: Optional[str] = None,
    员工性格: Optional[str] = None,
    店铺名称: Optional[str] = None,
) -> bool:
    """
    更新用户套餐信息

    参数:
        套餐关联id: 用户套餐关联表的ID
        员工名称: 员工名称，可选
        员工性格: 员工性格，可选
        店铺名称: 店铺名称，可选

    返回:
        是否更新成功
    """
    try:
        # 参数验证
        if not 套餐关联id or 套餐关联id < 1:
            数据库日志器.warning(f"更新用户套餐信息: 无效的套餐关联id {套餐关联id}")
            return False

        # 构建更新字段
        更新字段 = []
        参数 = []

        参数索引 = 1

        if 员工名称 is not None:
            更新字段.append(f"员工名称 = ${参数索引}")
            参数.append(员工名称)
            参数索引 += 1

        if 员工性格 is not None:
            更新字段.append(f"员工性格 = ${参数索引}")
            参数.append(员工性格)
            参数索引 += 1

        if 店铺名称 is not None:
            更新字段.append(f"店铺名称 = ${参数索引}")
            参数.append(店铺名称)
            参数索引 += 1

        # 如果没有要更新的字段，直接返回成功
        if not 更新字段:
            数据库日志器.info(
                f"更新用户套餐信息: 没有提供需要更新的字段, 套餐关联id={套餐关联id}"
            )
            return True

        # 添加ID到参数列表
        参数.append(套餐关联id)

        # 构建更新语句
        更新语句 = f"""
        UPDATE 用户套餐关联表
        SET {", ".join(更新字段)}
        WHERE id = $1
        """

        # 使用事务执行更新
        async with 异步连接池实例.获取连接() as 连接:
            try:
                # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
                # PostgreSQL的asyncpg使用事务上下文管理器
                async with 连接.transaction():
                    # 执行更新并获取影响行数
                    更新结果 = await 连接.execute(更新语句, *参数)
                    # PostgreSQL返回的是"UPDATE n"格式，提取数字
                    影响行数 = int(更新结果.split()[-1]) if 更新结果.startswith('UPDATE') else 0

                    if 影响行数 == 0:
                        数据库日志器.warning(
                            f"更新用户套餐信息: 未找到记录或未发生更改, 套餐关联id={套餐关联id}"
                        )
                        # 事务会自动回滚
                        return False

                # 事务自动提交
                数据库日志器.info(
                    f"更新用户套餐信息成功: 套餐关联id={套餐关联id}, 更新字段={更新字段}"
                )
                return True

            except Exception as tx_error:
                # {{ AURA-X: Modify - 修复PostgreSQL事务语法，事务会自动回滚. Approval: 寸止(ID:1735372800). }}
                # 事务会自动回滚
                错误日志器.error(
                    f"更新用户套餐信息事务异常: {str(tx_error)}", exc_info=True
                )
                return False

    except Exception as e:
        错误日志器.error(f"更新用户套餐信息异常: {str(e)}", exc_info=True)
        return False


async def 异步获取知识id(套餐关联id: int) -> Optional[str]:
    """
    直接从数据库获取知识id

    参数:
        套餐关联id: 用户套餐关联表的ID

    返回:
        知识id，不存在则返回None
    """
    try:
        # 参数验证
        if not 套餐关联id or 套餐关联id < 1:
            数据库日志器.warning(f"获取知识id: 无效的套餐关联id {套餐关联id}")
            return None

        # 使用显式连接管理
        async with 异步连接池实例.获取连接() as 连接:
            # {{ AURA-X: Modify - 修复PostgreSQL cursor语法，直接使用连接. Approval: 寸止(ID:1735372800). }}
            查询语句 = "SELECT 知识id FROM 用户套餐关联表 WHERE id = $1 LIMIT 1"
            结果 = await 连接.fetchrow(查询语句, 套餐关联id)

            if 结果:
                知识id = 结果.get("知识id")
                if 知识id:
                    数据库日志器.debug(
                        f"成功获取知识id: 套餐关联id={套餐关联id}, 知识id={知识id}"
                    )
                else:
                    数据库日志器.warning(
                        f"关联记录存在但知识id为空: 套餐关联id={套餐关联id}"
                    )
                return 知识id
            else:
                数据库日志器.info(f"未找到套餐关联记录: 套餐关联id={套餐关联id}")
                return None
    except Exception as e:
        错误日志器.error(f"获取知识id异常: {str(e)}", exc_info=True)
        return None
