"""
LangChain工具管理器

功能：
1. 工具注册和管理
2. 工具调用和执行
3. 自定义工具支持
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

# 静态导入所有依赖 - 避免动态导入
from 数据.LangChain_工具数据层 import LangChain工具数据层, LangChain工具数据层实例
from 服务.LangChain_内部函数包装器 import 内部函数包装器实例
from 状态 import 状态

# 配置日志
工具日志器 = logging.getLogger("LangChain.工具管理器")

# LangChain工具组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain_core.tools import BaseTool, tool  

    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符类
    class BaseTool:  
        def run(self, *_args, **_kwargs):
            return "工具不可用"

    def tool(func):  # type: ignore
        return func

    class CallbackManagerForToolRun:  # type: ignore
        pass


# MCP工具组件已移除 - 当前系统只使用核心LangChain工具


class 简单工具:
    """简单工具实现"""

    def __init__(self, name: str, description: str, func: Callable):
        self.name = name
        self.description = description
        self.func = func

    async def run(self, *args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(self.func):
                return await self.func(*args, **kwargs)
            else:
                return self.func(*args, **kwargs)
        except Exception as e:
            return f"工具执行错误: {str(e)}"


class LangChain工具管理器:
    """LangChain工具管理器 - 简化版本，专注于数据库驱动的工具管理

    核心功能：
    - 从数据库加载工具配置
    - 内部函数工具同步
    - 基于智能体关联的工具加载
    - 基本的工具注册和调用
    """

    def __init__(self, 工具数据层: Optional[LangChain工具数据层] = None):
        """构造函数 - 依赖注入模式

        Args:
            工具数据层: LangChain工具数据层实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保工具数据层永远不为None
        self.工具数据层: LangChain工具数据层 = 工具数据层 or LangChain工具数据层实例
        self.工具注册表 = {}  # 工具名称 -> 工具实例 (运行时缓存)
        self.工具配置 = {}  # 工具名称 -> 配置信息 (运行时缓存)
        self.已初始化 = True  # 简化初始化逻辑

        工具日志器.info("LangChain工具管理器创建成功")

    @classmethod
    async def 创建实例(cls) -> "LangChain工具管理器":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保工具数据层已初始化
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 创建管理器实例
        实例 = cls(LangChain工具数据层实例)

        # 执行异步初始化逻辑
        await 实例._异步初始化()

        return 实例

    async def _异步初始化(self):
        """内部异步初始化方法"""
        try:
            # 从数据库加载工具配置
            await self._从数据库加载工具配置()

            # 注册默认工具
            await self._注册默认工具()

            # 初始化内部函数工具
            await self._初始化内部函数工具()

            工具日志器.info("✅ LangChain工具管理器异步初始化成功")

        except Exception as e:
            工具日志器.error(f"❌ LangChain工具管理器异步初始化失败: {str(e)}")
            raise

    async def _从数据库加载工具配置(self):
        """从数据库加载工具配置 - 统一的工具配置加载逻辑"""
        try:
            if not self.工具数据层:
                工具日志器.warning("工具数据层未初始化，跳过数据库加载")
                return

            # 获取所有工具配置
            工具配置列表 = await self.工具数据层.获取工具配置列表()

            加载成功数量 = 0
            for 工具配置 in 工具配置列表:
                if await self._加载单个工具配置(工具配置):
                    加载成功数量 += 1

            if 加载成功数量 > 0:
                工具日志器.info(f"从数据库加载了 {加载成功数量} 个工具配置")

        except Exception as e:
            工具日志器.error(f"从数据库加载工具配置失败: {str(e)}")

    async def _加载单个工具配置(self, 工具配置: Dict[str, Any]) -> bool:
        """加载单个工具配置的统一逻辑"""
        try:
            工具名称 = 工具配置.get("工具名称")

            # 类型检查 - 只检查工具名称
            if not isinstance(工具名称, str):
                工具日志器.warning(f"跳过无效工具配置: 工具名称={工具名称}")
                return False

            # 将数据库配置加载到内存缓存
            self.工具配置[工具名称] = self._构建工具配置信息(工具配置)

            # 内部函数工具不需要创建实例（它们由内部函数包装器管理）
            工具日志器.debug(f"✅ 工具配置已加载: {工具名称}")
            return True

        except Exception as e:
            工具日志器.error(
                f"加载工具配置失败 ({工具配置.get('工具名称', 'unknown')}): {str(e)}"
            )
            return False

    def _构建工具配置信息(self, 工具配置: Dict[str, Any]) -> Dict[str, Any]:
        """构建标准化的工具配置信息"""
        return {
            "描述": 工具配置.get("工具描述", ""),
            "权限要求": 工具配置.get("权限要求", []),
            "安全级别": 工具配置.get("安全级别", 1),
            "超时时间": 工具配置.get("超时时间", 30),
            "重试次数": 工具配置.get("重试次数", 3),
            "数据库配置": True,  # 标记为数据库配置
            "注册时间": 工具配置.get("创建时间"),
            "启用状态": 工具配置.get("启用状态", True),
        }

    async def _创建数据库工具实例(
        self, 工具名称: str, 工具配置: Dict[str, Any]
    ) -> bool:
        """根据数据库配置创建工具实例 - 统一的工具实例创建逻辑"""
        try:
            # 对于内部函数工具，不需要创建实例（它们由内部函数包装器管理）
            工具日志器.debug(f"✅ 内部函数工具配置已加载: {工具名称}")
            return True

        except Exception as e:
            工具日志器.error(f"创建数据库工具实例失败 ({工具名称}): {str(e)}")
            return False

    async def _注册默认工具(self):
        """注册默认工具 - 简化版本，避免与内部函数工具重复"""
        try:
            # 只注册标准LangChain工具，其他工具由内部函数包装器管理
            await self._注册标准LangChain工具()

            工具日志器.info("✅ 默认工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册默认工具失败: {str(e)}")

    async def _注册标准LangChain工具(self):
        """注册标准的LangChain工具"""
        try:
            if not LANGCHAIN_AVAILABLE:
                工具日志器.warning("LangChain不可用，跳过标准工具注册")
                return

            # 示例数学工具已移除 - 生产环境不需要基础数学运算工具

            工具日志器.info("✅ 标准LangChain工具注册完成")

        except Exception as e:
            工具日志器.error(f"注册标准LangChain工具失败: {str(e)}")

    async def 注册工具(
        self,
        工具名称: str,
        工具实例_或_函数,
        配置: Optional[Dict[str, Any]] = None,
        覆盖已存在: bool = False,
    ) -> bool:
        """统一的工具注册方法 - 支持多种工具类型"""
        try:
            # 检查工具是否已存在
            if 工具名称 in self.工具注册表:
                if not 覆盖已存在:
                    工具日志器.info(f"工具已存在，跳过注册: {工具名称}")
                    return True  # 返回True表示工具已存在，不需要重复注册
                else:
                    工具日志器.info(f"覆盖已存在的工具: {工具名称}")

            # 统一的工具实例处理
            工具实例 = await self._处理工具实例(工具名称, 工具实例_或_函数)

            if not 工具实例:
                工具日志器.error(f"无法创建工具实例: {工具名称}")
                return False

            # 原子性注册：同时更新注册表和配置
            try:
                self.工具注册表[工具名称] = 工具实例
                self.工具配置[工具名称] = self._构建注册工具配置(工具实例, 配置)

                工具日志器.info(f"注册工具成功: {工具名称}")
                return True

            except Exception as reg_error:
                # 回滚注册
                self.工具注册表.pop(工具名称, None)
                self.工具配置.pop(工具名称, None)
                raise reg_error

        except Exception as e:
            工具日志器.error(f"注册工具失败 {工具名称}: {str(e)}")
            return False

    async def _处理工具实例(self, 工具名称: str, 工具实例_或_函数):
        """处理不同类型的工具实例"""
        try:
            # LangChain BaseTool实例
            if LANGCHAIN_AVAILABLE and isinstance(工具实例_或_函数, BaseTool):
                return 工具实例_或_函数

            # 普通函数 - 转换为LangChain工具
            elif callable(工具实例_或_函数):
                if LANGCHAIN_AVAILABLE:
                    # 使用已导入的tool装饰器
                    工具实例 = tool(工具实例_或_函数)
                    工具实例.name = 工具名称
                    工具实例.description = 工具实例_或_函数.__doc__ or f"{工具名称}工具"
                    return 工具实例
                else:
                    # 回退到简单工具
                    描述 = getattr(工具实例_或_函数, "__doc__", "") or f"{工具名称}工具"
                    工具实例 = 简单工具(工具名称, 描述, 工具实例_或_函数)
                    return 工具实例

            # 简单工具实例
            elif isinstance(工具实例_或_函数, 简单工具):
                return 工具实例_或_函数

            else:
                工具日志器.error(f"不支持的工具类型: {type(工具实例_或_函数)}")
                return None

        except Exception as e:
            工具日志器.error(f"处理工具实例失败: {str(e)}")
            return None

    def _构建注册工具配置(
        self, 工具实例, 额外配置: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """构建注册工具的配置信息"""
        基础配置 = {
            "描述": getattr(工具实例, "description", "")
            or getattr(工具实例, "描述", ""),
            "注册时间": datetime.now(),
            "数据库配置": False,  # 标记为运行时注册
            "启用状态": True,
        }

        if 额外配置:
            基础配置.update(额外配置)

        return 基础配置

    # 保留向后兼容的方法
    async def 注册LangChain工具(
        self, 工具名称: str, 工具函数_或_实例, 配置: Optional[Dict[str, Any]] = None
    ) -> bool:
        """注册LangChain工具 - 向后兼容方法"""
        return await self.注册工具(工具名称, 工具函数_或_实例, 配置)

    async def 调用工具(
        self,
        工具名称: str,
        *参数列表,
        用户id: int,
        智能体id: int,
        **关键字参数,
    ) -> Dict[str, Any]:
        """调用工具 - 简化版本"""

        开始时间 = datetime.now()

        try:
            # 检查工具是否存在
            if 工具名称 not in self.工具注册表:
                return {
                    "status_code": 状态.工具调用.工具不存在,
                    "success": False,
                    "message": f"工具不存在: {工具名称}",
                    "result": None,
                    "execution_time": 0,
                }

            工具实例 = self.工具注册表[工具名称]

            # 执行工具调用
            执行结果 = await self._执行单次工具调用(工具实例, 参数列表, 关键字参数)
            执行时间 = (datetime.now() - 开始时间).total_seconds()

            # 记录成功调用到数据库
            if self.工具数据层:
                try:
                    await self.工具数据层.记录工具调用(
                        工具名称, 成功=True, 执行时间=执行时间
                    )
                except Exception as 统计异常:
                    工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

            return {
                "status_code": 状态.工具调用.调用成功,
                "success": True,
                "message": "工具调用成功",
                "result": 执行结果,
                "execution_time": 执行时间,
                "tool_name": 工具名称,
            }

        except Exception as e:
            执行时间 = (datetime.now() - 开始时间).total_seconds()
            工具日志器.error(f"工具调用失败 {工具名称}: {str(e)}")

            # 记录失败调用到数据库
            if self.工具数据层:
                try:
                    await self.工具数据层.记录工具调用(工具名称, 成功=False)
                except Exception as 统计异常:
                    工具日志器.warning(f"记录工具调用统计失败: {统计异常}")

            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"工具调用失败: {str(e)}",
                "result": None,
                "execution_time": 执行时间,
                "tool_name": 工具名称,
            }

    async def 获取工具列表(self, 分类: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取工具列表"""
        try:
            工具列表 = []

            for 工具名称, 配置 in self.工具配置.items():
                # 移除分类过滤，因为已经不再使用分类
                工具列表.append(
                    {
                        "工具名称": 工具名称,
                        "描述": 配置.get("描述", ""),
                        "注册时间": 配置.get("注册时间", "").isoformat()
                        if isinstance(配置.get("注册时间"), datetime)
                        else str(配置.get("注册时间", "")),
                    }
                )

            return 工具列表

        except Exception as e:
            工具日志器.error(f"获取工具列表失败: {str(e)}")
            return []

    async def 删除工具(self, 工具名称: str) -> bool:
        """删除工具"""
        try:
            if 工具名称 in self.工具注册表:
                del self.工具注册表[工具名称]

            if 工具名称 in self.工具配置:
                del self.工具配置[工具名称]

            工具日志器.info(f"删除工具成功: {工具名称}")
            return True

        except Exception as e:
            工具日志器.error(f"删除工具失败 {工具名称}: {str(e)}")
            return False

    # ==================== 内部函数工具集成 ====================

    async def _初始化内部函数工具(self):
        """初始化内部函数工具 - 简化版本"""
        try:
            工具日志器.info("开始初始化内部函数工具...")

            # 使用已导入的内部函数包装器实例
            await 内部函数包装器实例.初始化()

            # 注册内部函数工具
            for 工具名称, 工具实例 in 内部函数包装器实例.已注册工具.items():
                await self.注册LangChain工具(
                    工具名称, 工具实例, {"类型": "内部函数工具", "分类": "业务功能"}
                )

            工具日志器.info(
                f"✅ 内部函数工具初始化成功，注册了 {len(内部函数包装器实例.已注册工具)} 个工具"
            )

        except Exception as e:
            工具日志器.error(f"初始化内部函数工具失败: {str(e)}")

    def 获取状态(self) -> Dict[str, Any]:
        """获取工具管理器状态"""
        内部工具数量 = sum(
            1 for 配置 in self.工具配置.values() if 配置.get("类型") == "内部函数工具"
        )

        return {
            "已初始化": self.已初始化,
            "注册工具数量": len(self.工具注册表),
            "内部函数工具数量": 内部工具数量,
            "LangChain可用": LANGCHAIN_AVAILABLE,
        }

    async def 测试工具(self, 工具名称: str, 用户id: Optional[int] = None) -> Dict[str, Any]:
        """增强的工具测试功能 - 使用统一状态码体系"""

        测试开始时间 = datetime.now()
        测试结果 = {
            "工具名称": 工具名称,
            "测试时间": 测试开始时间.isoformat(),
            "测试项目": {},
        }

        try:
            # 1. 基础存在性测试
            if 工具名称 not in self.工具注册表:
                return {
                    "status_code": 状态.工具调用.工具不存在,
                    "success": False,
                    "message": f"工具不存在: {工具名称}",
                    "测试结果": 测试结果,
                }

            工具实例 = self.工具注册表[工具名称]
            测试结果["测试项目"]["存在性测试"] = {"通过": True, "消息": "工具存在"}

            # 2. 配置完整性测试
            工具配置 = self.工具配置.get(工具名称, {})
            配置测试结果 = {
                "有描述": bool(工具配置.get("描述")),
                "有类型": bool(工具配置.get("类型")),
                "启用状态": 工具配置.get("启用状态", False),
            }
            测试结果["测试项目"]["配置测试"] = 配置测试结果

            # 权限测试已移除（权限控制器未实现）

            # 4. 性能测试（简单调用测试）
            性能测试开始 = datetime.now()
            try:
                # 使用安全的测试参数
                测试参数 = self._获取工具测试参数(工具名称)
                测试调用结果 = await self._安全测试调用(工具实例, 测试参数)
                性能测试时间 = (datetime.now() - 性能测试开始).total_seconds()

                测试结果["测试项目"]["性能测试"] = {
                    "通过": 测试调用结果.get("success", False),
                    "响应时间": f"{性能测试时间:.3f}s",
                    "消息": 测试调用结果.get("message", ""),
                }
            except Exception as e:
                测试结果["测试项目"]["性能测试"] = {"通过": False, "错误": str(e)}

            # 计算总测试时间
            总测试时间 = (datetime.now() - 测试开始时间).total_seconds()
            测试结果["总测试时间"] = f"{总测试时间:.3f}s"

            # 判断整体测试结果
            所有测试通过 = all(
                项目.get("通过", True) for 项目 in 测试结果["测试项目"].values()
            )

            return {
                "status_code": 状态.工具调用.测试成功
                if 所有测试通过
                else 状态.工具调用.调用失败,
                "success": 所有测试通过,
                "message": "所有测试通过" if 所有测试通过 else "部分测试失败",
                "测试结果": 测试结果,
            }

        except Exception as e:
            return {
                "status_code": 状态.工具调用.调用失败,
                "success": False,
                "message": f"测试执行失败: {str(e)}",
                "测试结果": 测试结果,
            }

    def _获取工具测试参数(self, 工具名称: str) -> Dict[str, Any]:
        """获取工具的安全测试参数"""
        # 根据工具类型返回安全的测试参数
        工具配置 = self.工具配置.get(工具名称, {})
        工具类型 = 工具配置.get("类型", "unknown")

        if 工具类型 == "字符串处理":
            return {"text": "test"}
        elif 工具类型 == "数学计算":
            return {"a": 1, "b": 2}
        else:
            return {}

    async def _安全测试调用(self, 工具实例, 测试参数: Dict[str, Any]) -> Dict[str, Any]:
        """安全的工具测试调用 - 支持所有工具类型"""
        try:
            if isinstance(工具实例, 简单工具):
                # 对于简单工具，使用关键字参数
                结果 = await 工具实例.run(**测试参数)
            elif LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
                # 对于LangChain工具，使用字符串参数
                测试字符串 = str(测试参数) if 测试参数 else "test"
                结果 = 工具实例.run(测试字符串)
            # MCP工具支持已移除
            elif hasattr(工具实例, "run"):
                # 对于其他具有run方法的工具
                if asyncio.iscoroutinefunction(工具实例.run):
                    结果 = await 工具实例.run(**测试参数)
                else:
                    结果 = 工具实例.run(**测试参数)
            else:
                return {
                    "success": False,
                    "message": f"不支持的工具类型: {type(工具实例)}",
                }

            return {"success": True, "result": 结果, "message": "测试调用成功"}
        except Exception as e:
            return {"success": False, "message": f"测试调用失败: {str(e)}"}

    # 性能监控相关方法已移除

    def _记录日志(
        self, 级别: str, 消息: str, 异常: Optional[Exception] = None, 工具名称: str = ""
    ):
        """统一的日志记录方法 - 消除重复的日志记录代码"""
        完整消息 = f"[{工具名称}] {消息}" if 工具名称 else 消息

        if 级别 == "info":
            工具日志器.info(完整消息)
        elif 级别 == "warning":
            工具日志器.warning(完整消息)
        elif 级别 == "error":
            if 异常:
                工具日志器.error(f"{完整消息}: {str(异常)}")
            else:
                工具日志器.error(完整消息)
        elif 级别 == "debug":
            工具日志器.debug(完整消息)

    # 性能统计方法已移除

    async def _执行单次工具调用(self, 工具实例, 参数列表: tuple, 关键字参数: dict):
        """执行单次工具调用 - 简化版本，避免与智能体服务重复"""
        # 工具类型处理映射表
        工具处理器 = {
            简单工具: lambda: 工具实例.run(*参数列表, **关键字参数),
        }

        # 检查已知工具类型
        for 工具类型, 处理器 in 工具处理器.items():
            if isinstance(工具实例, 工具类型):
                result = 处理器()
                return await result if asyncio.iscoroutine(result) else result

        # 处理LangChain工具
        if LANGCHAIN_AVAILABLE and isinstance(工具实例, BaseTool):
            参数 = 参数列表[0] if 参数列表 else ""
            return 工具实例.run(参数)

        # 处理通用run方法工具
        if hasattr(工具实例, "run"):
            result = 工具实例.run(*参数列表, **关键字参数)
            return await result if asyncio.iscoroutine(result) else result

        raise ValueError(f"不支持的工具类型: {type(工具实例)}")

    # 工具调用失败处理方法已移除

    # 工具调用成功处理方法已移除

    # 工具调用异常处理方法已移除

    # 性能监控相关方法已移除

    # 定期清理和性能监控任务已移除

    # 历史统计数据加载和热度评分系统已移除

    # 性能分析和监控方法已移除

    # 性能报告、使用模式分析和预测方法已移除

    # 核心工具判断方法已移除

    def _清理工具实例(self, 工具名称: str):
        """清理单个工具的所有相关数据 - 统一清理逻辑"""
        self.工具注册表.pop(工具名称, None)
        self.工具配置.pop(工具名称, None)

    # 性能统计方法已移除

    async def 同步内部函数工具到数据库(self) -> Dict[str, Any]:
        """同步内部函数工具到数据库 - 业务逻辑层"""
        try:
            工具日志器.info("🔄 开始同步内部函数工具到数据库...")

            # 使用已导入的内部函数包装器实例
            # 初始化内部函数包装器（如果尚未初始化）
            if not 内部函数包装器实例.已初始化:
                await 内部函数包装器实例.初始化()

            # 获取所有内部函数工具
            内部工具字典 = await 内部函数包装器实例.获取可用工具列表()
            工具元数据字典 = await 内部函数包装器实例.获取工具元数据()

            if not 内部工具字典:
                工具日志器.warning("⚠️ 未发现任何内部函数工具")
                return {
                    "success": True,
                    "message": "未发现内部函数工具",
                    "新增": 0,
                    "更新": 0,
                    "删除": 0,
                }

            # 获取现有工具映射
            现有工具映射 = await self.工具数据层.获取现有工具映射()
            新增计数 = 0
            更新计数 = 0
            删除计数 = 0

            # 遍历所有内部函数工具
            for 工具名称, 工具实例 in 内部工具字典.items():
                元数据 = 工具元数据字典.get(工具名称, {})

                # 构建工具配置
                工具配置 = self._构建内部工具配置(工具实例, 元数据)

                if 工具名称 in 现有工具映射:
                    # 更新现有工具
                    if await self.工具数据层.更新工具配置(工具名称, 工具配置):
                        更新计数 += 1
                        工具日志器.debug(f"🔄 更新工具: {工具名称}")
                else:
                    # 插入新工具
                    工具配置["工具名称"] = 工具名称
                    if await self.工具数据层.插入工具配置(工具配置):
                        新增计数 += 1
                        工具日志器.info(f"✅ 新增工具: {工具名称}")

            # 清理数据库中多余的内部函数工具记录
            内部工具名称集合 = set(内部工具字典.keys())
            数据库工具名称集合 = set(现有工具映射.keys())
            多余工具名称集合 = 数据库工具名称集合 - 内部工具名称集合

            if 多余工具名称集合:
                工具日志器.info(
                    f"🧹 发现 {len(多余工具名称集合)} 个多余的工具记录，开始清理..."
                )
                for 多余工具名称 in 多余工具名称集合:
                    if await self.工具数据层.删除工具配置(多余工具名称):
                        删除计数 += 1
                        工具日志器.info(f"🗑️ 删除多余工具: {多余工具名称}")
                    else:
                        工具日志器.warning(f"⚠️ 删除工具失败: {多余工具名称}")

            结果消息 = f"同步完成: 新增 {新增计数} 个工具, 更新 {更新计数} 个工具, 删除 {删除计数} 个多余工具"
            工具日志器.info(f"🎉 {结果消息}")

            return {
                "success": True,
                "message": 结果消息,
                "新增": 新增计数,
                "更新": 更新计数,
                "删除": 删除计数,
                "总计": len(内部工具字典),
            }

        except Exception as e:
            错误消息 = f"同步内部函数工具到数据库失败: {str(e)}"
            工具日志器.error(错误消息, exc_info=True)
            return {
                "success": False,
                "message": 错误消息,
                "新增": 0,
                "更新": 0,
                "删除": 0,
            }

    def _构建内部工具配置(self, 工具实例, 元数据: Dict[str, Any]) -> Dict[str, Any]:
        """构建内部工具配置 - 提取配置构建逻辑"""

        # 处理工具参数
        工具参数 = None
        if hasattr(工具实例, "args_schema") and 工具实例.args_schema:
            try:
                if hasattr(工具实例.args_schema, "model_json_schema"):
                    工具参数 = 工具实例.args_schema.model_json_schema()
                else:
                    工具参数 = str(工具实例.args_schema)
            except Exception:
                工具参数 = None

        工具描述 = getattr(工具实例, "description", "") or 元数据.get("描述", "")

        # 处理安全级别：将字符串转换为数字
        安全级别数值 = {"low": 1, "medium": 2, "high": 3}.get(
            元数据.get("安全级别", "medium"), 2
        )

        return {
            "工具描述": 工具描述,
            "工具参数": json.dumps(工具参数, ensure_ascii=False) if 工具参数 else None,
            "权限要求": 元数据.get("权限要求", ""),
            "安全级别": 安全级别数值,
            "启用状态": True,  # 默认启用
            "超时时间": 30,  # 默认超时30秒
            "重试次数": 3,  # 默认重试3次
        }




# 创建全局工具管理器实例
LangChain工具管理器实例 = LangChain工具管理器()
