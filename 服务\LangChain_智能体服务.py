"""
LangChain智能体服务 - 重构后的统一入口服务
基于复合智能体架构的简化版本

功能：
1. 智能体对话处理（委托给复合智能体核心）
2. 智能体配置管理（委托给数据层）
3. 智能体生命周期管理
4. 统计和监控功能

重构说明：
- 移除了所有重复的智能体实例类
- 移除了冗余的配置管理代码
- 移除了重复的工具验证逻辑
- 专注于业务逻辑委托和接口兼容性
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

# 数据层导入
from 数据.LangChain_智能体数据层 import LangChain智能体数据层实例
from 数据.LangChain_模型数据层 import LangChain模型数据层实例

# 核心服务导入
from 服务.LangChain_复合智能体核心 import LangChain复合智能体管理器实例
from 服务.LangChain_文档处理器 import LangChain文档处理器实例
from 服务.LangChain_知识库服务 import LangChain知识库服务实例

# 日志导入
from 日志 import 应用日志器 as 智能体服务日志器
from 状态 import 状态


class LangChain智能体服务:
    """LangChain智能体统一服务 - 重构后的简化版本
    
    采用纯委托模式，所有核心功能委托给专门的管理器处理
    """

    def __init__(self):
        """构造函数 - 最小化初始化"""
        self.已初始化 = True
        self.初始化时间 = datetime.now()
        
        # 统计信息
        self.对话统计 = {
            "总对话数": 0,
            "成功对话数": 0,
            "失败对话数": 0,
            "平均响应时间": 0.0
        }
        
        智能体服务日志器.info("LangChain智能体服务创建成功")

    async def 智能体对话(
        self,
        智能体id: int,
        用户表id: int,
        用户消息: str,
        会话id: Optional[str] = None,
        测试模式: bool = False,
        自定义变量: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """智能体对话处理 - 委托给复合智能体核心"""
        开始时间 = time.time()
        
        try:
            智能体服务日志器.info(f"开始智能体对话: 智能体ID={智能体id}, 用户ID={用户表id}")
            
            # 生成会话ID
            if not 会话id:
                会话id = f"session_{智能体id}_{用户表id}_{int(time.time())}"

            # 获取或创建复合智能体实例
            复合智能体实例 = await LangChain复合智能体管理器实例.获取或创建实例(智能体id, 用户表id)
            
            # 处理对话
            回复内容 = await 复合智能体实例.处理对话(用户消息, 会话id, 自定义变量)
            
            # 更新统计信息
            耗时 = time.time() - 开始时间
            self._更新对话统计(True, 耗时)
            
            智能体服务日志器.info(f"🎯 对话完成: 回复{len(回复内容)}字符 | 总耗时={耗时:.2f}s")
            
            return {
                "success": True,
                "message": "对话处理成功",
                "data": {
                    "回复": 回复内容,
                    "智能体名称": 复合智能体实例.配置.智能体名称,
                    "时间戳": str(int(datetime.now().timestamp())),
                },
            }

        except Exception as e:
            耗时 = time.time() - 开始时间
            self._更新对话统计(False, 耗时)
            
            智能体服务日志器.error(f"智能体对话失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"智能体对话失败: {str(e)}",
                "data": None
            }

    def _更新对话统计(self, 成功: bool, 耗时: float):
        """更新对话统计信息"""
        try:
            self.对话统计["总对话数"] += 1
            
            if 成功:
                self.对话统计["成功对话数"] += 1
            else:
                self.对话统计["失败对话数"] += 1
            
            # 更新平均响应时间
            总耗时 = self.对话统计["平均响应时间"] * (self.对话统计["总对话数"] - 1) + 耗时
            self.对话统计["平均响应时间"] = 总耗时 / self.对话统计["总对话数"]
            
        except Exception as e:
            智能体服务日志器.error(f"更新对话统计失败: {str(e)}")

    async def 创建智能体(self, 智能体数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建智能体 - 委托给数据层"""
        try:
            # 数据验证
            必需字段 = ["智能体名称", "模型名称", "用户表id"]
            for 字段 in 必需字段:
                if not 智能体数据.get(字段):
                    return {
                        "status": 状态.LangChain.参数错误,
                        "message": f"缺少必需字段: {字段}",
                        "data": None
                    }

            # 委托给数据层创建
            智能体id = await LangChain智能体数据层实例.创建智能体(智能体数据)
            
            if 智能体id:
                智能体服务日志器.info(f"✅ 智能体创建成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体创建成功",
                    "data": {"智能体id": 智能体id}
                }
            else:
                return {
                    "status": 状态.LangChain.智能体创建失败,
                    "message": "智能体创建失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"创建智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体创建失败,
                "message": f"创建智能体失败: {str(e)}",
                "data": None
            }

    async def 更新智能体(self, 智能体id: int, 更新数据: Dict[str, Any]) -> Dict[str, Any]:
        """更新智能体 - 委托给数据层"""
        try:
            # 委托给数据层更新
            成功 = await LangChain智能体数据层实例.更新智能体(智能体id, 更新数据)
            
            if 成功:
                # 清理缓存，确保下次使用新配置
                LangChain复合智能体管理器实例.清理实例缓存()
                
                智能体服务日志器.info(f"✅ 智能体更新成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体更新成功",
                    "data": None
                }
            else:
                return {
                    "status": 状态.LangChain.智能体更新失败,
                    "message": "智能体更新失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"更新智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"更新智能体失败: {str(e)}",
                "data": None
            }

    async def 删除智能体(self, 智能体id: int) -> Dict[str, Any]:
        """删除智能体 - 委托给数据层"""
        try:
            # 委托给数据层删除
            成功 = await LangChain智能体数据层实例.删除智能体(智能体id)
            
            if 成功:
                # 清理缓存
                LangChain复合智能体管理器实例.清理实例缓存()
                
                智能体服务日志器.info(f"✅ 智能体删除成功: ID={智能体id}")
                return {
                    "status": 状态.通用.成功,
                    "message": "智能体删除成功",
                    "data": None
                }
            else:
                return {
                    "status": 状态.LangChain.智能体更新失败,  # 使用更新失败代替删除失败
                    "message": "智能体删除失败",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"删除智能体失败: {str(e)}")
            return {
                "status": 状态.LangChain.智能体更新失败,
                "message": f"删除智能体失败: {str(e)}",
                "data": None
            }

    async def 获取智能体列表(self, 用户表id: int, 页码: int = 1, 每页数量: int = 10) -> Dict[str, Any]:
        """获取智能体列表 - 委托给数据层"""
        try:
            # 委托给数据层查询
            智能体列表, 总数量 = await LangChain智能体数据层实例.获取用户智能体列表(
                用户表id, 页码, 每页数量
            )
            
            return {
                "status": 状态.通用.成功,
                "message": "获取智能体列表成功",
                "data": {
                    "智能体列表": 智能体列表,
                    "总数量": 总数量,
                    "当前页码": 页码,
                    "每页数量": 每页数量
                }
            }

        except Exception as e:
            智能体服务日志器.error(f"获取智能体列表失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"获取智能体列表失败: {str(e)}",
                "data": None
            }

    async def 获取智能体详情(self, 智能体id: int) -> Dict[str, Any]:
        """获取智能体详情 - 委托给数据层"""
        try:
            # 委托给数据层查询
            智能体详情 = await LangChain智能体数据层实例.获取智能体详情完整(智能体id)
            
            if 智能体详情:
                return {
                    "status": 状态.通用.成功,
                    "message": "获取智能体详情成功",
                    "data": 智能体详情
                }
            else:
                return {
                    "status": 状态.LangChain.智能体不存在,
                    "message": "智能体不存在",
                    "data": None
                }

        except Exception as e:
            智能体服务日志器.error(f"获取智能体详情失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"获取智能体详情失败: {str(e)}",
                "data": None
            }

    async def 获取服务统计(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            return {
                "status": 状态.通用.成功,
                "message": "获取服务统计成功",
                "data": {
                    "对话统计": self.对话统计,
                    "实例缓存数量": len(LangChain复合智能体管理器实例.实例缓存),
                    "服务运行时间": str(datetime.now() - self.初始化时间),
                    "初始化状态": self.已初始化
                }
            }

        except Exception as e:
            智能体服务日志器.error(f"获取服务统计失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"获取服务统计失败: {str(e)}",
                "data": None
            }

    async def 清理缓存(self) -> Dict[str, Any]:
        """清理所有缓存"""
        try:
            LangChain复合智能体管理器实例.清理实例缓存()
            
            智能体服务日志器.info("✅ 缓存清理完成")
            return {
                "status": 状态.通用.成功,
                "message": "缓存清理完成",
                "data": None
            }

        except Exception as e:
            智能体服务日志器.error(f"清理缓存失败: {str(e)}")
            return {
                "status": 状态.LangChain.配置错误,
                "message": f"清理缓存失败: {str(e)}",
                "data": None
            }

    # 兼容性方法 - 委托给知识库服务
    async def 获取支持的文件格式(self) -> Dict[str, Any]:
        """获取支持的文件格式 - 委托给文档处理器"""
        try:
            格式列表 = LangChain文档处理器实例.获取支持的文件格式列表()
            
            格式统计 = {
                "总格式数": len(格式列表),
                "文档类型": len([f for f in 格式列表 if f["类型"] == "document"]),
                "表格类型": len([f for f in 格式列表 if f["类型"] == "spreadsheet"]),
                "文本类型": len([f for f in 格式列表 if f["类型"] == "text"]),
                "其他类型": len([f for f in 格式列表 if f["类型"] not in ["document", "spreadsheet", "text"]])
            }

            return {
                "success": True,
                "支持的格式列表": 格式列表,
                "格式统计": 格式统计
            }
            
        except Exception as e:
            智能体服务日志器.error(f"获取支持的文件格式失败: {str(e)}")
            return {
                "success": False,
                "error": f"获取支持的文件格式失败: {str(e)}"
            }


# 创建全局实例
LangChain智能体服务实例 = LangChain智能体服务()
