"""
微信智能添加服务层

负责微信自动添加好友的核心业务逻辑
包括智能时间控制算法、联系方式数据获取和状态更新处理
"""

import asyncio
import random
import re
from typing import Dict, Any, Optional
from datetime import datetime, timedelta, time
import pytz
import 状态
from 数据.用户微信配置数据访问层 import 异步获取微信号配置数据访问服务
from 数据.微信添加记录数据访问层 import (
    异步验证用户微信关联数据访问服务,
    异步获取微信添加时间控制记录数据访问服务,
    异步更新微信添加时间控制记录数据访问服务,
    异步更新微信添加记录状态数据访问服务,
    异步动态计算微信添加计数数据访问服务
)
# from 服务.异步微信服务 import 异步查询用户微信好友请求状态业务服务  # 不再需要，已重构
from 日志 import 错误日志器, 系统日志器


def 处理消息模板(模板: str, 联系方式数据: Dict[str, Any]) -> str:
    """
    处理消息模板，替换其中的变量

    支持的变量：
    - {达人昵称}: 达人的昵称
    - {联系方式}: 联系方式（微信号、手机号等）
    - {联系方式类型}: 联系方式类型（微信号、手机号等）
    - {抖音账号}: 抖音账号
    - {粉丝数}: 粉丝数量

    参数：
        模板 (str): 消息模板字符串
        联系方式数据 (Dict[str, Any]): 联系方式相关数据

    返回：
        str: 处理后的消息
    """
    if not 模板 or not isinstance(模板, str):
        return "你好，想和你交流一下"

    # 准备替换变量
    替换变量 = {
        "达人昵称": 联系方式数据.get("达人昵称", "朋友"),
        "联系方式": 联系方式数据.get("联系方式", ""),
        "联系方式类型": 联系方式数据.get("联系方式类型", ""),
        "抖音账号": 联系方式数据.get("抖音账号", ""),
        "粉丝数": str(联系方式数据.get("粉丝数", "")) if 联系方式数据.get("粉丝数") else ""
    }

    # 使用正则表达式替换变量
    处理后的消息 = 模板
    for 变量名, 变量值 in 替换变量.items():
        if 变量值:  # 只有当变量值不为空时才替换
            处理后的消息 = re.sub(r'\{' + 变量名 + r'\}', str(变量值), 处理后的消息)
        else:
            # 如果变量值为空，移除整个变量占位符
            处理后的消息 = re.sub(r'\{' + 变量名 + r'\}', '', 处理后的消息)

    # 清理多余的空格和标点
    处理后的消息 = re.sub(r'\s+', ' ', 处理后的消息).strip()
    处理后的消息 = re.sub(r'[,，]\s*[,，]', '，', 处理后的消息)  # 清理重复逗号
    处理后的消息 = re.sub(r'^[,，\s]+|[,，\s]+$', '', 处理后的消息)  # 清理开头结尾的逗号和空格

    return 处理后的消息 if 处理后的消息 else "你好，想和你交流一下"


async def 异步执行微信智能添加分析业务服务(用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    执行微信智能添加分析业务服务
    
    功能说明：
    - 复用现有的friend-request-status逻辑获取联系方式数据
    - 获取用户个性化配置参数
    - 执行智能时间控制算法
    - 返回联系方式列表和详细的时间控制信息
    
    核心业务逻辑：
    1. 获取用户微信配置（个性化参数）
    2. 复用现有逻辑获取需要添加的联系方式
    3. 获取时间控制历史记录
    4. 执行智能时间控制算法
    5. 计算精确的计划执行时间
    6. 返回完整的分析结果
    
    参数：
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表的ID
    
    返回：
        Dict[str, Any]: 包含联系方式数据和时间控制信息的响应
    """
    try:
        # 步骤0：验证用户微信关联
        系统日志器.info(f"开始验证用户微信关联: 用户id={用户id}, 微信id={微信信息表id}")
        用户微信关联验证结果 = await 异步验证用户微信关联数据访问服务(用户id, 微信信息表id)
        
        if 用户微信关联验证结果["status"] != 状态.通用.成功:
            错误信息 = f"用户微信关联验证失败: {用户微信关联验证结果['message']}"
            错误日志器.warning(错误信息)
            return {
                "status": 状态.通用.失败,
                "message": "该微信账号未绑定到您的账户，请先在设置中绑定微信账号",
                "data": {
                    "是否可立即执行": False,
                    "执行状态描述": "微信账号关联验证失败",
                    "关联验证结果": 用户微信关联验证结果["data"]
                }
            }
        
        系统日志器.info(f"用户微信关联验证通过: 用户id={用户id}, 微信id={微信信息表id}")
        
        # 步骤1：获取用户微信配置
        配置获取结果 = await 异步获取微信号配置数据访问服务(用户id, 微信信息表id)
        if 配置获取结果.get("status") != 状态.通用.成功:
            return 配置获取结果
        
        用户配置 = 配置获取结果["data"]
        系统日志器.info(f"获取用户微信配置成功: 用户id={用户id}, 配置来源={用户配置['配置来源']}")
        
        # 步骤2：查询是否有待处理的记录（好友请求状态为空或=0）
        from 数据.用户微信好友请求状态数据访问层 import 用户微信好友请求状态数据访问层

        待处理记录 = await 用户微信好友请求状态数据访问层.查询用户微信添加记录表中好友请求状态为空或为0的记录(用户id, 微信信息表id)

        if 待处理记录:
            # 有待处理记录，获取详细信息
            达人详细信息 = await 用户微信好友请求状态数据访问层.根据用户达人补充信息表id查询达人详细信息(
                待处理记录["用户达人补充信息表id"]
            )

            if 达人详细信息:
                # 获取该记录的计划添加时间
                时间控制记录结果 = await 异步获取微信添加时间控制记录数据访问服务(用户id, 微信信息表id)
                if 时间控制记录结果.get("status") != 状态.通用.成功:
                    return 时间控制记录结果

                时间控制记录 = 时间控制记录结果["data"]
                计划添加时间 = 时间控制记录.get("计划添加时间")

                # 检查计划时间
                上海时区 = pytz.timezone('Asia/Shanghai')
                当前时间 = datetime.now(上海时区)
                if 计划添加时间 and isinstance(计划添加时间, datetime):
                    # 确保计划添加时间有时区信息
                    if 计划添加时间.tzinfo is None:
                        计划添加时间 = 上海时区.localize(计划添加时间)
                    elif 计划添加时间.tzinfo:
                        计划添加时间 = 计划添加时间.astimezone(上海时区)

                    if 当前时间 >= 计划添加时间:
                        # 计划时间已到，可以立即执行
                        超时小时数 = (当前时间 - 计划添加时间).total_seconds() / 3600
                        if 超时小时数 > 1:
                            执行状态描述 = f"计划时间已过期{超时小时数:.1f}小时，现在可以立即执行"
                        else:
                            执行状态描述 = "计划时间已到，可以立即执行添加操作"

                        # 获取动态计数数据用于响应构建
                        动态计数结果 = await 异步动态计算微信添加计数数据访问服务(用户id, 微信信息表id)
                        动态计数数据 = 动态计数结果["data"] if 动态计数结果.get("status") == 状态.通用.成功 else None

                        return await 构建智能分析响应(
                            联系方式数据={
                                "状态类型": "需要添加好友请求",
                                "微信添加记录id": 待处理记录["微信添加记录id"],
                                "达人昵称": 达人详细信息.get("达人昵称"),
                                "联系方式": 达人详细信息.get("联系方式"),
                                "联系方式类型": 达人详细信息.get("联系方式类型"),

                                "好友请求状态": 待处理记录["好友请求状态"]
                            },
                            用户配置=用户配置,
                            时间控制记录=时间控制记录,
                            是否可立即执行=True,
                            消息=执行状态描述,
                            计划添加时间=计划添加时间,
                            动态计数数据=动态计数数据
                        )
                    else:
                        # 计划时间未到，需要等待
                        等待时间 = 计划添加时间 - 当前时间
                        等待小时数 = 等待时间.total_seconds() / 3600
                        if 等待小时数 < 1:
                            等待分钟数 = int(等待时间.total_seconds() / 60)
                            执行状态描述 = f"计划时间未到，还需等待{等待分钟数}分钟"
                        else:
                            执行状态描述 = f"计划时间未到，还需等待{等待小时数:.1f}小时"

                        # 获取动态计数数据用于响应构建
                        动态计数结果 = await 异步动态计算微信添加计数数据访问服务(用户id, 微信信息表id)
                        动态计数数据 = 动态计数结果["data"] if 动态计数结果.get("status") == 状态.通用.成功 else None

                        return await 构建智能分析响应(
                            联系方式数据={
                                "状态类型": "需要添加好友请求",
                                "微信添加记录id": 待处理记录["微信添加记录id"],
                                "达人昵称": 达人详细信息.get("达人昵称"),
                                "联系方式": 达人详细信息.get("联系方式"),
                                "联系方式类型": 达人详细信息.get("联系方式类型"),
                                "用户达人补充信息表id": 达人详细信息["用户达人补充信息表id"],
                                "用户达人关联表id": 达人详细信息["用户达人关联表id"],
                                "好友请求状态": 待处理记录["好友请求状态"]
                            },
                            用户配置=用户配置,
                            时间控制记录=时间控制记录,
                            是否可立即执行=False,
                            消息=执行状态描述,
                            计划添加时间=计划添加时间,
                            动态计数数据=动态计数数据
                        )

        # 步骤3：没有待处理记录，查找新的未处理记录并创建
        未完成记录 = await 用户微信好友请求状态数据访问层.查询用户达人补充信息表中未完成微信添加的记录(用户id, 微信信息表id)

        if 未完成记录:
            # 获取时间控制历史记录
            时间控制记录结果 = await 异步获取微信添加时间控制记录数据访问服务(用户id, 微信信息表id)
            if 时间控制记录结果.get("status") != 状态.通用.成功:
                return 时间控制记录结果

            时间控制记录 = 时间控制记录结果["data"]

            # 计算新记录的计划时间
            计划时间 = await 计算新记录计划时间(用户配置, 时间控制记录)

            # 创建新记录并设置计划时间
            try:
                新微信添加记录id = await 用户微信好友请求状态数据访问层.创建用户联系方式微信添加记录(
                    未完成记录["用户达人补充信息表id"],
                    微信信息表id,
                    用户id,
                    计划时间
                )

                系统日志器.info(f"已创建新微信添加记录: 用户id={用户id}, 新记录id={新微信添加记录id}, 计划时间={计划时间}")

                # 检查新创建记录的计划时间
                上海时区 = pytz.timezone('Asia/Shanghai')
                当前时间 = datetime.now(上海时区)
                # 确保计划时间有时区信息
                if 计划时间.tzinfo is None:
                    计划时间 = 上海时区.localize(计划时间)
                elif 计划时间.tzinfo:
                    计划时间 = 计划时间.astimezone(上海时区)

                if 当前时间 >= 计划时间:
                    是否可立即执行 = True
                    执行状态描述 = "新记录已创建，计划时间已到，可以立即执行"
                else:
                    是否可立即执行 = False
                    等待时间 = 计划时间 - 当前时间
                    等待小时数 = 等待时间.total_seconds() / 3600
                    if 等待小时数 < 1:
                        等待分钟数 = int(等待时间.total_seconds() / 60)
                        执行状态描述 = f"新记录已创建，还需等待{等待分钟数}分钟"
                    else:
                        执行状态描述 = f"新记录已创建，还需等待{等待小时数:.1f}小时"

                # 获取动态计数数据用于响应构建
                动态计数结果 = await 异步动态计算微信添加计数数据访问服务(用户id, 微信信息表id)
                动态计数数据 = 动态计数结果["data"] if 动态计数结果.get("status") == 状态.通用.成功 else None

                return await 构建智能分析响应(
                    联系方式数据={
                        "状态类型": "需要添加好友请求",
                        "微信添加记录id": 新微信添加记录id,
                        "达人昵称": 未完成记录.get("达人昵称"),
                        "联系方式": 未完成记录.get("联系方式"),
                        "联系方式类型": 未完成记录.get("联系方式类型"),
                        "用户达人补充信息表id": 未完成记录["用户达人补充信息表id"],
                        "用户达人关联表id": 未完成记录["用户达人关联表id"],
                        "好友请求状态": None
                    },
                    用户配置=用户配置,
                    时间控制记录=时间控制记录,
                    是否可立即执行=是否可立即执行,
                    消息=执行状态描述,
                    计划添加时间=计划时间,
                    动态计数数据=动态计数数据
                )

            except Exception as e:
                系统日志器.error(f"创建微信添加记录异常: 用户id={用户id}, 错误={str(e)}")
                return {
                    "status": 状态.微信.获取对接进度列表失败,
                    "message": "创建添加记录失败，请稍后重试",
                    "data": None
                }

        # 没有找到任何可处理的记录
        return {
            "status": 状态.通用.成功,
            "message": "暂无可添加的联系方式",
            "data": {
                "是否可立即执行": False,
                "当日已添加计数": 0,
                "当日剩余配额": 用户配置.get("每日最大添加次数", 10),
                "连续操作计数": 0,
                "需要长时间休息": False,
                "计划添加时间": None,
                "实际添加时间": None,
                "限制原因说明": "暂无可添加的联系方式",
                "验证消息": 处理消息模板(用户配置.get("验证消息模板", "你好，想和你交流一下"), {}),
                "好友备注": 处理消息模板(用户配置.get("好友备注模板", "未知联系人"), {}),
                "当前生效配置": {
                    "配置来源": 用户配置["配置来源"],
                    "每日上限": 用户配置["每日最大添加次数"],
                    "添加间隔": f"{用户配置['最小添加间隔分钟']}-{用户配置['最大添加间隔分钟']}分钟",
                    "工作时间": f"{用户配置['工作开始时间']}-{用户配置['工作结束时间']}",
                    "批次控制": f"连续{用户配置['连续添加次数上限']}次后休息{用户配置['批次休息最小分钟']}-{用户配置['批次休息最大分钟']}分钟",
                    "随机延迟": f"{用户配置['随机延迟最小分钟']}-{用户配置['随机延迟最大分钟']}分钟",
                    "成功率模拟": f"{用户配置['成功率模拟概率']*100:.1f}%"
                },
                "联系方式数据": None
            }
        }
        
    except Exception as e:
        错误日志器.error(f"执行微信智能添加分析业务异常: 用户id={用户id}, 微信信息表id={微信信息表id}, 错误={str(e)}")
        return {
            "status": 状态.微信.获取对接进度列表失败,
            "message": "系统繁忙，请稍后重试",
            "data": None
        }


async def 异步执行智能时间控制算法业务服务(用户配置: Dict[str, Any], 时间控制记录: Dict[str, Any], 用户id: int, 微信信息表id: int) -> Dict[str, Any]:
    """
    执行智能时间控制算法业务服务
    
    功能说明：
    - 基于用户配置和历史记录执行智能时间控制算法
    - 考虑工作时间、每日限制、批次控制、随机化等因素
    - 计算精确的下次可执行时间
    
    核心算法逻辑：
    1. 检查工作时间段限制
    2. 检查每日添加次数限制
    3. 检查添加间隔时间限制
    4. 检查批次休息需求
    5. 检查每小时添加限制
    6. 应用随机延迟和成功率模拟
    7. 计算最终的计划执行时间
    
    参数：
        用户配置 (Dict[str, Any]): 用户的个性化配置参数
        时间控制记录 (Dict[str, Any]): 历史时间控制记录
        用户id (int): 当前用户的ID
        微信信息表id (int): 微信信息表的ID
    
    返回：
        Dict[str, Any]: 包含时间控制分析结果的字典
    """
    try:
        上海时区 = pytz.timezone('Asia/Shanghai')
        当前时间 = datetime.now(上海时区)
        是否可立即执行 = True
        限制原因说明 = ""
        计划执行时间 = None
        
        # 获取配置参数
        每日最大添加次数 = 用户配置["每日最大添加次数"]
        连续添加次数上限 = 用户配置["连续添加次数上限"]
        
        # 动态计算历史记录（替代静态字段）
        动态计数结果 = await 异步动态计算微信添加计数数据访问服务(用户id, 微信信息表id)
        if 动态计数结果.get("status") != 状态.通用.成功:
            错误日志器.error(f"动态计算微信添加计数失败: 用户id={用户id}, 微信信息表id={微信信息表id}")
            # 使用默认值继续执行，避免阻塞
            动态计数数据 = {
                "当日添加计数": 0,
                "连续添加计数": 0,
                "最后操作时间": None
            }
        else:
            动态计数数据 = 动态计数结果["data"]

        # 使用动态计算的数据
        当日已添加计数 = 动态计数数据["当日添加计数"]
        连续操作计数 = 动态计数数据["连续添加计数"]
        实际添加时间 = 动态计数数据["最后操作时间"]
        最后长休息时间 = 时间控制记录.get("最后长休息时间")
        历史计划时间 = 时间控制记录.get("计划添加时间")

        系统日志器.info(f"使用动态计算数据: 用户id={用户id}, 微信信息表id={微信信息表id}, 当日计数={当日已添加计数}, 连续计数={连续操作计数}")
        
        # 预检查：处理历史计划时间超时的情况（简化版，仅记录日志）
        if 历史计划时间 and 历史计划时间 <= 当前时间:
            # 历史计划时间已过期，记录超时信息
            超时小时数 = (当前时间 - 历史计划时间).total_seconds() / 3600
            if 超时小时数 > 24:  # 超时超过24小时
                系统日志器.info(f"检测到长期超时({超时小时数:.1f}小时): 用户id={用户id}, 微信信息表id={微信信息表id}")
                # 注意：使用动态计算后，不需要手动重置计数，系统会自动基于实际数据计算
        
        # 检查1：工作时间段限制
        当前时分 = 当前时间.time()
        if not 异步检查是否在工作时间内业务函数(当前时分, 用户配置):
            是否可立即执行 = False
            限制原因说明 = "当前不在工作时间内"
            计划执行时间 = 异步计算下一个工作时间业务函数(当前时间, 用户配置)
        
        # 检查2：每日添加次数限制
        if 是否可立即执行:
            今日上限, 限制检查结果 = 异步检查每日添加限制业务函数(当前时间, 用户配置, 当日已添加计数)
            if not 限制检查结果["可以执行"]:
                是否可立即执行 = False
                限制原因说明 = 限制检查结果["限制原因"]
                计划执行时间 = 限制检查结果["计划时间"]
        
        # 检查3：添加间隔时间限制
        if 是否可立即执行 and 实际添加时间:
            间隔检查结果 = 异步检查添加间隔限制业务函数(当前时间, 实际添加时间, 用户配置)
            if not 间隔检查结果["可以执行"]:
                是否可立即执行 = False
                限制原因说明 = 间隔检查结果["限制原因"]
                计划执行时间 = 间隔检查结果["计划时间"]
        
        # 检查4：批次休息需求
        if 是否可立即执行 and 连续操作计数 >= 连续添加次数上限:
            # 检查是否需要长时间休息
            if not 最后长休息时间 or 异步检查是否需要重新批次休息业务函数(最后长休息时间, 当前时间):
                是否可立即执行 = False
                限制原因说明 = f"需要批次休息，连续添加已达{连续添加次数上限}次上限"
                批次休息分钟 = random.randint(用户配置["批次休息最小分钟"], 用户配置["批次休息最大分钟"])
                计划执行时间 = 当前时间 + timedelta(minutes=批次休息分钟)
                
                # 调整到工作时间内
                if not 异步检查是否在工作时间内业务函数(计划执行时间.time(), 用户配置):
                    计划执行时间 = 异步计算下一个工作时间业务函数(计划执行时间, 用户配置)
        
        # 检查5：应用随机延迟和成功率模拟
        if 是否可立即执行:
            # 随机延迟
            随机延迟分钟 = random.randint(用户配置["随机延迟最小分钟"], 用户配置["随机延迟最大分钟"])
            
            # 成功率模拟（偶尔故意延长等待时间）
            if random.random() > 用户配置["成功率模拟概率"]:
                是否可立即执行 = False
                限制原因说明 = "模拟真人行为，随机延迟执行"
                额外延迟分钟 = random.randint(30, 120)  # 额外延迟30-120分钟
                计划执行时间 = 当前时间 + timedelta(minutes=随机延迟分钟 + 额外延迟分钟)
            else:
                if 随机延迟分钟 > 0:
                    计划执行时间 = 当前时间 + timedelta(minutes=随机延迟分钟)
        
        # 最终检查：如果计划执行时间已经过去，则可以立即执行
        if 计划执行时间 and 计划执行时间 <= 当前时间:
            是否可立即执行 = True
            限制原因说明 = ""
            计划执行时间 = None
        
        # 特殊处理：如果历史计划时间过期且当前没有新的限制，清除过期时间
        if 是否可立即执行 and 历史计划时间 and 历史计划时间 <= 当前时间:
            计划执行时间 = None  # 确保返回的计划时间为空，表示可以立即执行
        
        # 计算剩余配额
        今日上限 = 每日最大添加次数
        if 异步检查是否为周末业务函数(当前时间) and 用户配置["周末是否添加"]:
            今日上限 = 用户配置.get("周末每日最大添加次数", 10)
        
        当日剩余配额 = max(0, 今日上限 - (当日已添加计数 or 0))
        
        # 生成执行状态描述（合并等待时长信息）
        if 是否可立即执行:
            if 历史计划时间 and 历史计划时间 <= 当前时间:
                超时小时数 = (当前时间 - 历史计划时间).total_seconds() / 3600
                if 超时小时数 > 1:
                    执行状态描述 = f"计划时间已过期{超时小时数:.1f}小时，现在可以立即执行"
                else:
                    执行状态描述 = "计划时间已到，可以立即执行添加操作"
            else:
                执行状态描述 = "可以立即执行添加操作"
        else:
            # 计算等待时间并合并到状态描述中
            if 计划执行时间:
                等待分钟数 = int((计划执行时间 - 当前时间).total_seconds() / 60)
                if 等待分钟数 < 60:
                    等待时长描述 = f"约{等待分钟数}分钟后"
                elif 等待分钟数 < 1440:  # 24小时
                    等待时长描述 = f"约{等待分钟数//60}小时{等待分钟数%60}分钟后"
                else:
                    等待时长描述 = f"约{等待分钟数//1440}天后"
                执行状态描述 = f"需要等待：{限制原因说明}，{等待时长描述}可执行"
            else:
                执行状态描述 = f"需要等待：{限制原因说明}"
        
        return {
            "是否可立即执行": 是否可立即执行,
            "执行状态描述": 执行状态描述,
            "当日已添加计数": 当日已添加计数,
            "当日剩余配额": 当日剩余配额,
            "连续操作计数": 连续操作计数,
            "需要长时间休息": 连续操作计数 >= 连续添加次数上限,
            "计划添加时间": 计划执行时间,
            "限制原因说明": 限制原因说明
        }
        
    except Exception as e:
        错误日志器.error(f"执行智能时间控制算法业务异常: 错误={str(e)}")
        raise e


async def 异步处理微信添加状态更新业务服务(
    用户id: int,
    记录id: int,
    新状态: int,
    备注信息: Optional[str] = None
) -> Dict[str, Any]:
    """
    处理微信添加状态更新业务服务

    功能说明：
    - 更新联系方式添加记录的状态
    - 自动记录实际操作时间
    - 简单记录状态，不进行复杂的时间控制计算

    核心业务逻辑：
    1. 更新记录状态和备注信息
    2. 自动生成并记录实际操作时间
    3. 返回更新结果

    参数：
        用户id (int): 当前用户的ID
        记录id (int): 要更新的记录id
        新状态 (int): 新的好友请求状态（整数类型）
        备注信息 (Optional[str]): 备注信息

    返回：
        Dict[str, Any]: 包含更新结果的响应
    """
    try:
        # 步骤1：更新记录状态
        状态更新结果 = await 异步更新微信添加记录状态数据访问服务(记录id, 新状态, 备注信息)
        if 状态更新结果.get("status") != 状态.通用.成功:
            return 状态更新结果

        记录信息 = 状态更新结果["data"]
        微信信息表id = 记录信息["微信信息表id"]

        # 步骤2：记录状态更新完成（实际添加时间已在数据访问层自动更新）
        系统日志器.info(f"微信添加状态更新完成: 用户id={用户id}, 记录id={记录id}, 新状态={新状态}")

        return {
            "status": 状态.通用.成功,
            "message": "状态更新完成",
            "data": {
                "记录id": 记录id,
                "微信信息表id": 微信信息表id,
                "原状态": 记录信息["原状态"],
                "新状态": 新状态,
                "备注信息": 备注信息
            }
        }
        
    except Exception as e:
        错误日志器.error(f"处理微信添加状态更新业务异常: 用户id={用户id}, 记录id={记录id}, 错误={str(e)}")
        return {
            "status": 状态.微信.更新对接进度失败,
            "message": "状态更新失败，请稍后重试",
            "data": None
        }


# ================== 辅助业务函数 ==================

def 计算下次添加时间(实际操作时间: datetime, 用户配置: Dict[str, Any]) -> datetime:
    """
    计算下次添加的计划时间

    Args:
        实际操作时间: 实际执行操作的时间
        用户配置: 用户配置对象

    Returns:
        datetime: 下次添加的计划时间
    """
    # 计算随机间隔
    最小间隔分钟 = 用户配置.get("最小添加间隔分钟") or 10
    最大间隔分钟 = 用户配置.get("最大添加间隔分钟") or 30
    间隔分钟 = random.randint(最小间隔分钟, 最大间隔分钟)

    # 基于实际操作时间计算初始计划时间
    计划时间 = 实际操作时间 + timedelta(minutes=间隔分钟)

    # 调整到工作时间内
    if not 异步检查是否在工作时间内业务函数(计划时间.time(), 用户配置):
        计划时间 = 异步计算下一个工作时间业务函数(计划时间, 用户配置)

    return 计划时间



def 解析时间字段(时间值) -> time:
    """
    智能解析时间字段，支持多种格式

    Args:
        时间值: 可能是 timedelta、字符串或其他格式

    Returns:
        time: 解析后的时间对象
    """
    if isinstance(时间值, timedelta):
        # 处理 timedelta 对象
        总秒数 = int(时间值.total_seconds())
        小时 = 总秒数 // 3600
        分钟 = (总秒数 % 3600) // 60
        秒 = 总秒数 % 60

        # 确保小时数在有效范围内 (0-23)
        小时 = 小时 % 24
        return time(小时, 分钟, 秒)
    elif isinstance(时间值, str):
        # 处理字符串格式
        if 时间值.startswith('PT') and 时间值.endswith('H'):
            # ISO Duration 格式 (PT9H)
            小时 = int(时间值[2:-1])
            # 确保小时数在有效范围内 (0-23)
            小时 = 小时 % 24
            return time(小时, 0, 0)
        elif ':' in 时间值:
            # 标准时间格式
            return datetime.strptime(时间值, "%H:%M:%S").time()
    elif isinstance(时间值, time):
        # 已经是 time 对象
        return 时间值

    # 默认返回午夜
    return time(0, 0, 0)


def 异步检查是否在工作时间内业务函数(当前时分: time, 用户配置: Dict[str, Any]) -> bool:
    """
    检查当前时间是否在工作时间内

    Args:
        当前时分: 当前时间
        用户配置: 用户配置对象（包含所有时间设置）

    Returns:
        bool: True表示在工作时间内，False表示不在
    """
    # 从用户配置中提取时间设置，智能解析各种格式
    工作开始时间 = 解析时间字段(用户配置["工作开始时间"])
    工作结束时间 = 解析时间字段(用户配置["工作结束时间"])
    是否启用午休 = 用户配置.get("是否启用午休", False)

    # 检查是否在工作时间范围内
    if 当前时分 < 工作开始时间 or 当前时分 > 工作结束时间:
        return False

    # 检查午休时间
    if 是否启用午休:
        午休开始时间 = 解析时间字段(用户配置["午休开始时间"])
        午休结束时间 = 解析时间字段(用户配置["午休结束时间"])
        if 午休开始时间 <= 当前时分 <= 午休结束时间:
            return False

    return True


def 异步检查是否为周末业务函数(当前时间: datetime) -> bool:
    """检查当前是否为周末"""
    return 当前时间.weekday() >= 5  # 5=Saturday, 6=Sunday


def 异步检查每日添加限制业务函数(当前时间: datetime, 用户配置: Dict[str, Any], 当日已添加计数: int) -> tuple:
    """
    检查每日添加次数限制

    Args:
        当前时间: 当前时间
        用户配置: 用户配置对象
        当日已添加计数: 当日已添加计数

    Returns:
        tuple: (今日上限, 检查结果)
    """
    每日最大添加次数 = 用户配置["每日最大添加次数"]

    # 检查周末限制
    if 异步检查是否为周末业务函数(当前时间):
        if not 用户配置["周末是否添加"]:
            return 0, {
                "可以执行": False,
                "限制原因": "周末不允许添加好友",
                "计划时间": 异步计算下个工作日开始时间业务函数(当前时间, 用户配置)
            }
        else:
            今日上限 = 用户配置.get("周末每日最大添加次数", 10)
    else:
        今日上限 = 每日最大添加次数
    
    # 检查今日次数限制
    if 当日已添加计数 >= 今日上限:
        return 今日上限, {
            "可以执行": False,
            "限制原因": f"今日添加次数已达上限({今日上限}次)",
            "计划时间": 异步计算明日工作开始时间业务函数(当前时间, 用户配置)
        }
    
    return 今日上限, {"可以执行": True, "限制原因": "", "计划时间": None}


def 异步检查添加间隔限制业务函数(当前时间: datetime, 实际添加时间: datetime, 用户配置: Dict[str, Any]) -> Dict[str, Any]:
    """
    检查添加间隔时间限制

    Args:
        当前时间: 当前时间
        实际添加时间: 上次实际添加时间
        用户配置: 用户配置对象（包含间隔设置）

    Returns:
        Dict[str, Any]: 检查结果
    """
    # 从用户配置中提取间隔设置
    最小间隔分钟 = 用户配置["最小添加间隔分钟"]
    最大间隔分钟 = 用户配置["最大添加间隔分钟"]

    距离上次添加分钟数 = (当前时间 - 实际添加时间).total_seconds() / 60
    所需间隔分钟 = random.randint(最小间隔分钟, 最大间隔分钟)
    
    if 距离上次添加分钟数 < 所需间隔分钟:
        计划执行时间 = 实际添加时间 + timedelta(minutes=所需间隔分钟)
        
        # 如果计算出的时间不在工作时间内，调整到下一个工作时间
        if not 异步检查是否在工作时间内业务函数(计划执行时间.time(), 用户配置):
            计划执行时间 = 异步计算下一个工作时间业务函数(计划执行时间, 用户配置)
        
        return {
            "可以执行": False,
            "限制原因": f"添加间隔时间不足，还需等待{所需间隔分钟 - int(距离上次添加分钟数)}分钟",
            "计划时间": 计划执行时间
        }
    
    return {"可以执行": True, "限制原因": "", "计划时间": None}


def 异步计算下一个工作时间业务函数(基准时间: datetime, 用户配置: Dict[str, Any]) -> datetime:
    """
    智能计算下一个可用的工作时间

    Args:
        基准时间: 基准时间点
        用户配置: 用户配置对象（包含所有时间设置）

    Returns:
        datetime: 下一个可用的工作时间
    """
    当前日期 = 基准时间.date()
    当前时间 = 基准时间.time()

    # 从用户配置中提取时间设置，智能解析各种格式
    工作开始时间 = 解析时间字段(用户配置["工作开始时间"])
    工作结束时间 = 解析时间字段(用户配置["工作结束时间"])
    是否启用午休 = 用户配置.get("是否启用午休", False)

    # 获取午休时间设置
    午休开始时间 = None
    午休结束时间 = None
    if 是否启用午休:
        午休开始时间 = 解析时间字段(用户配置["午休开始时间"])
        午休结束时间 = 解析时间字段(用户配置["午休结束时间"])

    # 尝试在当天找到合适的时间
    候选时间 = 基准时间

    # 情况1：如果当前时间早于工作开始时间，调整到工作开始时间
    if 当前时间 < 工作开始时间:
        候选时间 = datetime.combine(当前日期, 工作开始时间)

    # 情况2：如果当前时间在午休时间内，调整到午休结束时间
    elif 是否启用午休 and 午休开始时间 and 午休结束时间 and 午休开始时间 <= 当前时间 <= 午休结束时间:
        候选时间 = datetime.combine(当前日期, 午休结束时间)

    # 情况3：如果当前时间超过工作结束时间，调整到明天工作开始时间
    elif 当前时间 > 工作结束时间:
        候选时间 = datetime.combine(当前日期 + timedelta(days=1), 工作开始时间)

    # 情况4：当前时间在正常工作时间内，使用基准时间
    else:
        候选时间 = 基准时间

    # 处理周末：如果是周末，调整到下周一
    while 候选时间.weekday() >= 5:  # 5=周六, 6=周日
        候选时间 = datetime.combine(候选时间.date() + timedelta(days=1), 工作开始时间)

    return 候选时间


def 异步计算下个工作日开始时间业务函数(当前时间: datetime, 用户配置: Dict[str, Any]) -> datetime:
    """
    计算下个工作日的开始时间

    Args:
        当前时间: 当前时间
        用户配置: 用户配置对象

    Returns:
        datetime: 下个工作日的开始时间
    """
    工作开始时间 = 解析时间字段(用户配置["工作开始时间"])
    下个工作日 = 当前时间 + timedelta(days=1)
    下个工作日 = 下个工作日.replace(hour=工作开始时间.hour, minute=工作开始时间.minute, second=0, microsecond=0)

    while 下个工作日.weekday() >= 5:
        下个工作日 += timedelta(days=1)

    return 下个工作日


def 异步计算明日工作开始时间业务函数(当前时间: datetime, 用户配置: Dict[str, Any]) -> datetime:
    """
    计算明日工作开始时间

    Args:
        当前时间: 当前时间
        用户配置: 用户配置对象

    Returns:
        datetime: 明日工作开始时间
    """
    工作开始时间 = 解析时间字段(用户配置["工作开始时间"])
    明日开始时间 = (当前时间 + timedelta(days=1)).replace(
        hour=工作开始时间.hour,
        minute=工作开始时间.minute,
        second=0,
        microsecond=0
    )
    return 明日开始时间


def 异步检查是否需要重新批次休息业务函数(最后长休息时间: datetime, 当前时间: datetime) -> bool:
    """检查是否需要重新进行批次休息"""
    if not 最后长休息时间:
        return True
    
    # 如果距离上次长休息超过24小时，需要重新休息
    return (当前时间 - 最后长休息时间).total_seconds() > 24 * 3600



async def 计算新记录计划时间(用户配置: Dict[str, Any], 时间控制记录: Dict[str, Any]) -> datetime:
    """
    计算新记录的计划添加时间

    Args:
        用户配置: 用户配置参数
        时间控制记录: 时间控制历史记录

    Returns:
        datetime: 计划添加时间
    """
    上海时区 = pytz.timezone('Asia/Shanghai')
    当前时间 = datetime.now(上海时区)
    实际添加时间 = 时间控制记录.get("实际添加时间")
    计划添加时间 = 时间控制记录.get("计划添加时间")

    # 检查是否为首次添加（没有任何历史记录）
    是否首次添加 = not (实际添加时间 or 计划添加时间)

    if 是否首次添加:
        # 首次添加应该立即执行，不需要延时
        系统日志器.info(f"检测到首次添加，设置为立即执行: 当前时间={当前时间}")

        # 检查当前时间是否在工作时间内
        if 异步检查是否在工作时间内业务函数(当前时间.time(), 用户配置):
            # 在工作时间内，立即执行
            return 当前时间
        else:
            # 不在工作时间内，调整到下一个工作时间开始
            计划时间 = 异步计算下一个工作时间业务函数(当前时间, 用户配置)
            系统日志器.info(f"首次添加但不在工作时间内，调整到下一个工作时间: {计划时间}")
            return 计划时间

    # 非首次添加，需要考虑间隔时间
    # 简化逻辑：实际添加时间 > 计划添加时间（当做实际添加时间） > 当前时间
    基准时间 = None

    if 实际添加时间 and isinstance(实际添加时间, datetime):
        # 优先使用实际添加时间作为基准，确保时区一致
        基准时间 = 实际添加时间
        if 基准时间.tzinfo is None:
            基准时间 = 上海时区.localize(基准时间)
        elif 基准时间.tzinfo:
            基准时间 = 基准时间.astimezone(上海时区)
        系统日志器.debug(f"使用实际添加时间作为基准: {基准时间}")
    elif 计划添加时间 and isinstance(计划添加时间, datetime):
        # 如果实际添加时间为NULL，把计划添加时间当做实际添加时间处理
        基准时间 = 计划添加时间
        if 基准时间.tzinfo is None:
            基准时间 = 上海时区.localize(基准时间)
        elif 基准时间.tzinfo:
            基准时间 = 基准时间.astimezone(上海时区)
        系统日志器.debug(f"实际添加时间为空，使用计划添加时间作为基准: {基准时间}")
    else:
        # 理论上不应该到这里，因为上面已经处理了首次添加的情况
        基准时间 = 当前时间
        系统日志器.warning(f"意外情况：非首次添加但没有历史时间记录，使用当前时间作为基准: {基准时间}")

    # 基于基准时间计算下次计划时间
    最小间隔分钟 = 用户配置["最小添加间隔分钟"]
    最大间隔分钟 = 用户配置["最大添加间隔分钟"]
    间隔分钟 = random.randint(最小间隔分钟, 最大间隔分钟)
    计划时间 = 基准时间 + timedelta(minutes=间隔分钟)

    # 确保计划时间不早于当前时间
    if 计划时间 < 当前时间:
        计划时间 = 当前时间 + timedelta(minutes=random.randint(用户配置["随机延迟最小分钟"], 用户配置["随机延迟最大分钟"]))
        系统日志器.debug(f"计划时间早于当前时间，调整为: {计划时间}")

    # 调整到工作时间内
    if not 异步检查是否在工作时间内业务函数(计划时间.time(), 用户配置):
        计划时间 = 异步计算下一个工作时间业务函数(计划时间, 用户配置)

    return 计划时间


async def 构建智能分析响应(
    联系方式数据: Dict[str, Any],
    用户配置: Dict[str, Any],
    时间控制记录: Dict[str, Any],
    是否可立即执行: bool,
    消息: str,
    计划添加时间: Optional[datetime] = None,
    动态计数数据: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    构建智能分析响应数据

    参数：
        动态计数数据: 动态计算的计数数据，如果提供则优先使用，否则从时间控制记录获取
    """
    # 使用动态计算的数据，如果没有则使用默认值
    if 动态计数数据:
        当日已添加计数 = 动态计数数据["当日添加计数"]
        连续操作计数 = 动态计数数据["连续添加计数"]
    else:
        当日已添加计数 = 0
        连续操作计数 = 0

    智能分析结果 = {
        "是否可立即执行": 是否可立即执行,
        "当日已添加计数": 当日已添加计数,
        "当日剩余配额": max(0, 用户配置["每日最大添加次数"] - 当日已添加计数),
        "连续操作计数": 连续操作计数,
        "需要长时间休息": 连续操作计数 >= 用户配置["连续添加次数上限"],
        "计划添加时间": 计划添加时间,
        "实际添加时间": 时间控制记录.get("实际添加时间"),
        "验证消息": 处理消息模板(用户配置.get("验证消息模板", "你好，我是{达人昵称}的朋友，想和你交流一下"), 联系方式数据),
        "好友备注": 处理消息模板(用户配置.get("好友备注模板", "{达人昵称}-{联系方式类型}"), 联系方式数据),
        "当前生效配置": {
            "配置来源": 用户配置["配置来源"],
            "每日上限": 用户配置["每日最大添加次数"],
            "添加间隔": f"{用户配置['最小添加间隔分钟']}-{用户配置['最大添加间隔分钟']}分钟",
            "工作时间": f"{用户配置['工作开始时间']}-{用户配置['工作结束时间']}",
            "批次控制": f"连续{用户配置['连续添加次数上限']}次后休息{用户配置['批次休息最小分钟']}-{用户配置['批次休息最大分钟']}分钟",
            "随机延迟": f"{用户配置['随机延迟最小分钟']}-{用户配置['随机延迟最大分钟']}分钟",
            "成功率模拟": f"{用户配置['成功率模拟概率']*100:.1f}%"
        },
        "联系方式数据": 联系方式数据
    }

    return {
        "status": 状态.通用.成功,
        "message": 消息,
        "data": 智能分析结果
    }

