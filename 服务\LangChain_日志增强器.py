"""
LangChain智能体日志增强器
提供统一、优雅的日志格式化功能
"""

import time
from typing import Any, Dict, List, Optional
from datetime import datetime


class 智能体对话日志增强器:
    """统一的智能体对话日志格式化器"""
    
    @staticmethod
    def 对话开始(智能体名称: str, 用户消息: str, 会话id: str = "") -> str:
        """对话开始日志"""
        消息预览 = 用户消息[:50] + "..." if len(用户消息) > 50 else 用户消息
        会话信息 = f" | 会话={会话id}" if 会话id else ""
        return f"🤖 【{智能体名称}】开始处理用户问题: {消息预览}{会话信息}"
    
    @staticmethod
    def RAG检索开始(查询文本: str, 知识库数量: int, 检索策略: str = "similarity") -> str:
        """RAG检索开始日志"""
        查询预览 = 查询文本[:30] + "..." if len(查询文本) > 30 else 查询文本
        return f"📚 RAG检索启动: 查询='{查询预览}' | 知识库数量={知识库数量} | 策略={检索策略}"
    
    @staticmethod
    def RAG检索结果(结果数量: int, 最高相似度: float, 耗时: float, 知识库名称: str = "") -> str:
        """RAG检索结果日志"""
        知识库信息 = f" | 来源={知识库名称}" if 知识库名称 else ""
        return f"📊 RAG检索完成: {结果数量}个结果 | 最高相似度={最高相似度:.3f} | 耗时={耗时:.2f}s{知识库信息}"
    
    @staticmethod
    def RAG检索失败(错误信息: str, 耗时: float) -> str:
        """RAG检索失败日志"""
        return f"❌ RAG检索失败: {错误信息} | 耗时={耗时:.2f}s"
    
    @staticmethod
    def 工具调用开始(工具名称: str, 参数: Dict[str, Any]) -> str:
        """工具调用开始日志"""
        参数预览 = str(参数)[:100] + "..." if len(str(参数)) > 100 else str(参数)
        return f"🔧 工具调用: {工具名称} | 参数={参数预览}"
    
    @staticmethod
    def 工具调用完成(工具名称: str, 结果: str, 耗时: float, 成功: bool = True) -> str:
        """工具调用完成日志"""
        状态图标 = "✅" if 成功 else "❌"
        状态文本 = "完成" if 成功 else "失败"
        结果预览 = 结果[:50] + "..." if len(结果) > 50 else 结果
        return f"{状态图标} 工具{状态文本}: {工具名称} | 结果={结果预览} | 耗时={耗时:.2f}s"
    
    @staticmethod
    def 智能体思考(思考内容: str) -> str:
        """智能体思考过程日志"""
        思考预览 = 思考内容[:80] + "..." if len(思考内容) > 80 else 思考内容
        return f"💭 智能体思考: {思考预览}"
    
    @staticmethod
    def 模型调用开始(模型名称: str, 令牌数: int = 0) -> str:
        """模型调用开始日志"""
        令牌信息 = f" | 输入令牌={令牌数}" if 令牌数> 0 else ""
        return f"🧠 模型调用: {模型名称}{令牌信息}"
    
    @staticmethod
    def 模型调用完成(模型名称: str, 输出令牌: int, 耗时: float, 成功: bool = True) -> str:
        """模型调用完成日志"""
        状态图标 = "✅" if 成功 else "❌"
        状态文本 = "完成" if 成功 else "失败"
        return f"{状态图标} 模型{状态文本}: {模型名称} | 输出令牌={输出令牌} | 耗时={耗时:.2f}s"
    
    @staticmethod
    def 对话完成(回复长度: int, 总耗时: float, 令牌消耗: int, 工具调用次数: int = 0) -> str:
        """对话完成日志"""
        工具信息 = f" | 工具调用={工具调用次数}次" if 工具调用次数 > 0 else ""
        return f"🎯 对话完成: 回复{回复长度}字符 | 总耗时={总耗时:.2f}s | 令牌={令牌消耗}{工具信息}"
    
    @staticmethod
    def 配置加载(配置类型: str, 配置名称: str, 成功: bool = True) -> str:
        """配置加载日志"""
        状态图标 = "✅" if 成功 else "❌"
        状态文本 = "成功" if 成功 else "失败"
        return f"{状态图标} {配置类型}加载{状态文本}: {配置名称}"
    
    @staticmethod
    def 实例创建(实例类型: str, 实例ID: str, 成功: bool = True) -> str:
        """实例创建日志"""
        状态图标 = "✅" if 成功 else "❌"
        状态文本 = "成功" if 成功 else "失败"
        return f"{状态图标} {实例类型}创建{状态文本}: {实例ID}"
    
    @staticmethod
    def 性能统计(操作名称: str, 耗时: float, 阈值: float = 5.0) -> str:
        """性能统计日志"""
        if 耗时 > 阈值:
            状态图标 = "⚠️"
            状态文本 = "慢"
        else:
            状态图标 = "⚡"
            状态文本 = "快"
        return f"{状态图标} 性能统计: {操作名称} | 耗时={耗时:.2f}s | 评级={状态文本}"
    
    @staticmethod
    def 错误处理(错误类型: str, 错误信息: str, 恢复策略: str = "") -> str:
        """错误处理日志"""
        恢复信息 = f" | 恢复策略={恢复策略}" if 恢复策略 else ""
        return f"🚨 错误处理: {错误类型} | 错误={错误信息}{恢复信息}"
    
    @staticmethod
    def 缓存操作(操作类型: str, 缓存键: str, 命中: bool = True) -> str:
        """缓存操作日志"""
        状态图标 = "🎯" if 命中 else "💾"
        状态文本 = "命中" if 命中 else "未命中"
        return f"{状态图标} 缓存{状态文本}: {操作类型} | 键={缓存键}"


class 日志计时器:
    """日志计时器 - 用于测量操作耗时"""
    
    def __init__(self, 操作名称: str):
        self.操作名称 = 操作名称
        self.开始时间 = None
        self.结束时间 = None
    
    def __enter__(self):
        self.开始时间 = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.结束时间 = time.time()
    
    @property
    def 耗时(self) -> float:
        """获取耗时（秒）"""
        if self.开始时间 and self.结束时间:
            return self.结束时间 - self.开始时间
        return 0.0
    
    def 获取性能日志(self, 阈值: float = 5.0) -> str:
        """获取性能统计日志"""
        return 智能体对话日志增强器.性能统计(self.操作名称, self.耗时, 阈值)


class 对话上下文日志器:
    """对话上下文日志器 - 维护对话过程中的上下文信息"""
    
    def __init__(self, 智能体名称: str, 会话id: str):
        self.智能体名称 = 智能体名称
        self.会话id = 会话id
        self.开始时间 = time.time()
        self.工具调用次数 = 0
        self.RAG检索次数 = 0
        self.模型调用次数 = 0
        self.总令牌消耗 = 0
    
    def 记录工具调用(self):
        """记录工具调用"""
        self.工具调用次数 += 1
    
    def 记录RAG检索(self):
        """记录RAG检索"""
        self.RAG检索次数 += 1
    
    def 记录模型调用(self, 令牌消耗: int = 0):
        """记录模型调用"""
        self.模型调用次数 += 1
        self.总令牌消耗 += 令牌消耗
    
    def 获取对话完成日志(self, 回复长度: int) -> str:
        """获取对话完成日志"""
        总耗时 = time.time() - self.开始时间
        return 智能体对话日志增强器.对话完成(
            回复长度, 总耗时, self.总令牌消耗, self.工具调用次数
        )
    
    def 获取对话统计(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        总耗时 = time.time() - self.开始时间
        return {
            "智能体名称": self.智能体名称,
            "会话id": self.会话id,
            "总耗时": 总耗时,
            "工具调用次数": self.工具调用次数,
            "RAG检索次数": self.RAG检索次数,
            "模型调用次数": self.模型调用次数,
            "总令牌消耗": self.总令牌消耗,
        }


# 创建全局实例
日志增强器 = 智能体对话日志增强器()
