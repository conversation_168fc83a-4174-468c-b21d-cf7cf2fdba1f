"""
用户相关数据模型
包含用户登录、注册、密码管理等相关的请求和响应模型
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


# -----------------------------
# 用户账号相关模型
# -----------------------------

class 用户登录请求(BaseModel):
    """用户登录请求模型 - 中文字段版本"""
    手机号: str = Field(..., description="用户手机号")
    密码: str = Field(..., description="用户密码")


class 用户注册请求(BaseModel):
    """用户注册请求模型 - 中文字段版本"""
    手机号: str = Field(..., description="用户手机号")
    密码: str = Field(..., description="用户密码")
    验证码: str = Field(..., description="短信验证码")
    邀请码: Optional[str] = Field(None, description="邀请码（可选）")


class 修改密码请求(BaseModel):
    """修改密码请求模型 - 中文字段版本"""
    旧密码: str = Field(..., description="当前密码")
    新密码: str = Field(..., description="新密码")


class 重置密码请求(BaseModel):
    """重置密码请求模型 - 中文字段版本"""
    手机号: str = Field(..., description="用户手机号")
    验证码: str = Field(..., description="短信验证码")
    新密码: str = Field(..., description="新密码")


class 验证码请求(BaseModel):
    """验证码请求模型 - 中文字段版本"""
    手机号: str = Field(..., description="用户手机号")
    类型: str = Field(..., description="验证码类型")


# -----------------------------
# 用户信息相关模型
# -----------------------------

class 用户基本信息响应(BaseModel):
    """用户基本信息响应模型"""
    用户id: int = Field(..., description="用户id")
    昵称: Optional[str] = Field(None, description="用户昵称")
    手机号: Optional[str] = Field(None, description="用户手机号")
    邮箱: Optional[str] = Field(None, description="用户邮箱")
    等级: Optional[int] = Field(None, description="用户等级")
    经验值: Optional[int] = Field(None, description="用户经验值")
    算力值: Optional[int] = Field(None, description="用户算力值")
    每日邀约次数: Optional[int] = Field(None, description="每日邀约次数")
    每日快递查询次数: Optional[int] = Field(None, description="每日快递查询次数")
    可创建团队数: Optional[int] = Field(None, description="可创建团队数")
    是否管理员: Optional[bool] = Field(None, description="是否管理员")
    状态: Optional[str] = Field(None, description="用户状态")
    创建时间: Optional[datetime] = Field(None, description="创建时间")


class 会员信息响应(BaseModel):
    """会员信息响应模型"""
    会员id: Optional[int] = Field(None, description="会员id")
    会员名称: Optional[str] = Field(None, description="会员名称")
    每月费用: Optional[int] = Field(None, description="每月费用")
    每年费用: Optional[int] = Field(None, description="每年费用")
    每月算力点: Optional[int] = Field(None, description="每月算力点")
    可创建团队数: Optional[int] = Field(None, description="可创建团队数")
    创建团队默认人数上限: Optional[int] = Field(None, description="创建团队默认人数上限")
    可加入团队数: Optional[int] = Field(None, description="可加入团队数")
    开通时间: Optional[datetime] = Field(None, description="会员开通时间")
    到期时间: Optional[datetime] = Field(None, description="会员到期时间")
    是否有效: Optional[bool] = Field(None, description="会员是否有效")


class 代理类型信息响应(BaseModel):
    """代理类型信息响应模型"""
    代理类型id: Optional[int] = Field(None, description="代理类型id")
    返佣比例: Optional[int] = Field(None, description="返佣比例")
    赠送天数: Optional[int] = Field(None, description="赠送天数")
    赠送会员类型: Optional[int] = Field(None, description="赠送会员类型")
    代理等级: Optional[int] = Field(None, description="代理等级")


class 权限信息响应(BaseModel):
    """权限信息响应模型"""
    权限ID: int = Field(..., description="权限ID")
    权限名称: str = Field(..., description="权限名称")
    权限描述: Optional[str] = Field(None, description="权限描述")
    开通时间: Optional[datetime] = Field(None, description="权限开通时间")
    到期时间: Optional[datetime] = Field(None, description="权限到期时间")
    是否有效: Optional[bool] = Field(None, description="权限是否有效")


class 团队权限信息响应(BaseModel):
    """团队权限信息响应模型"""
    团队id: int = Field(..., description="团队id")
    团队名称: str = Field(..., description="团队名称")
    团队代码: Optional[str] = Field(None, description="团队代码")
    职位: Optional[str] = Field(None, description="职位")
    状态: Optional[str] = Field(None, description="状态")
    加入时间: Optional[datetime] = Field(None, description="加入时间")
    团队权限列表: List[权限信息响应] = Field(default_factory=list, description="团队权限列表")


class 用户完整权限响应(BaseModel):
    """用户完整权限信息响应模型"""
    用户基本信息: 用户基本信息响应 = Field(..., description="用户基本信息")
    会员信息: Optional[会员信息响应] = Field(None, description="会员信息")
    代理类型信息: Optional[代理类型信息响应] = Field(None, description="代理类型信息")
    个人权限列表: List[权限信息响应] = Field(default_factory=list, description="个人权限列表")
    团队权限列表: List[团队权限信息响应] = Field(default_factory=list, description="团队权限列表")
    权限配置: Dict[str, Any] = Field(default_factory=dict, description="其他权限配置信息")


# -----------------------------
# 用户权限相关模型
# -----------------------------

class 用户权限获取请求(BaseModel):
    """用户权限获取请求模型"""
    包含团队权限: bool = Field(True, description="是否包含团队权限信息")
    包含历史权限: bool = Field(False, description="是否包含历史权限信息")


# -----------------------------
# 用户信息相关模型
# -----------------------------

class 用户基本信息模型(BaseModel):
    """用户基本信息模型"""
    用户id: int = Field(..., description="用户id")
    手机号: str = Field(..., description="用户手机号")
    昵称: Optional[str] = Field(None, description="用户昵称")
    邮箱: Optional[str] = Field(None, description="用户邮箱")
    状态: Optional[str] = Field(None, description="用户状态")
    注册时间: Optional[datetime] = Field(None, description="注册时间")
    最后登录时间: Optional[datetime] = Field(None, description="最后登录时间")


class 用户详细信息模型(用户基本信息模型):
    """用户详细信息模型"""
    ip地址: Optional[str] = Field(None, description="最后登录IP地址")
    登录次数: Optional[int] = Field(None, description="总登录次数")
    首次登录时间: Optional[datetime] = Field(None, description="首次登录时间")
    最近7天登录次数: Optional[int] = Field(None, description="最近7天登录次数")
    设备信息: Optional[str] = Field(None, description="最后登录设备信息")
    地理位置: Optional[str] = Field(None, description="最后登录地理位置")


# -----------------------------
# 登录响应相关模型
# -----------------------------

class 登录成功响应模型(BaseModel):
    """登录成功响应数据模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    用户信息: Optional[用户基本信息模型] = Field(None, description="用户基本信息")


class 登录历史记录模型(BaseModel):
    """登录历史记录模型"""
    id: int = Field(..., description="记录id")
    登陆时间: datetime = Field(..., description="登录时间")
    ip地址: Optional[str] = Field(None, description="登录IP地址")
    格式化时间: Optional[str] = Field(None, description="格式化的登录时间")


class 登录历史分页响应模型(BaseModel):
    """登录历史分页响应模型"""
    列表: list[登录历史记录模型] = Field(..., description="登录记录列表")
    总数: int = Field(..., description="总记录数")
    页码: int = Field(..., description="当前页码")
    每页数量: int = Field(..., description="每页数量")
    总页数: int = Field(..., description="总页数")
